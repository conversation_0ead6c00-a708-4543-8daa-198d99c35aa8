## app related
NODE_ENV=stage
NG_APP_PRODUCTION=false
NG_APP_API_URL=https://some.awesome.xyz/
NG_APP_API_MICROSERVICE_URL=https://some.awesome.xyz/
NG_APP_MERCHANT_ACCESS_BASE_URL=https://some.awesome.xyz/
NG_APP_LANDING_URL=https://some.awesome.xyz/
NG_APP_CUSTOMER_LOGIN_URL=https://some.awesome.xyz/
NG_APP_CUSTOMER_REGISTER_URL=https://some.awesome.xyz/
NG_APP_POSUI_URL=https://some.awesome.xyz/
NG_APP_POSUI_REGISTER_URL=https://some.awesome.xyz/
NG_APP_POS_API_URL=https://some.awesome.xyz/
NG_APP_MERCHANT_LOGIN_URL=https://some.awesome.xyz/
NG_APP_MERCHANT_REGISTER_URL=https://some.awesome.xyz/
NG_APP_MERCHANT_PATH=merchant
NG_APP_I18N_URL=https://some.awesome.xyz/
NG_APP_FLAGS_AUTHORIZATION_KEY=myAwesomeKey
NG_APP_FLAGS_ENV=staging
NG_APP_GTM_ID=myAwesomeKey
NG_APP_WEBCHAT_API_KEY=myAwesomeKey
NG_APP_WEBCHAT_BRAND_ID=myAwesomeID
NG_APP_DATADOG_APPLICATION_ID=myAwesomeID
NG_APP_DATADOG_CLIENT_TOKEN=myAwesomeKey
NG_APP_DATADOG_ENV=stage
NG_APP_DATADOG_SERVICE=datadog-service
NG_APP_PROMO_API_URL=https://some.awesome.xyz/
## for docker
AUTH_TOKEN=myAwesomeToken
ENV=stg

