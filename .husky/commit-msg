# The commit message file path is passed as the first argument
COMMIT_MSG_FILE=$1

echo "🔍 Validating commit message format..."
npx commitlint --config ./commitlint.config.js --edit $COMMIT_MSG_FILE

if [ $? -ne 0 ]; then
  echo "❌ Commit message format validation failed!"
  echo "Your commit message does not follow the project's commit convention."
  echo "Expected format example: 'feat(scope): add new feature'"
  echo ""
  echo "Please fix your commit message to continue."
  exit 1
fi

echo "✅ Commit message format validation passed!"
