# Set to exit on error
set -e

# Store the current staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$' | tr '\n' ' ')
if [ -z "$STAGED_FILES" ]; then
  echo "No JS/TS files to lint."
  exit 0
fi

echo "🔍 Running lint-fix on staged files..."

# For Angular projects, ng lint needs special handling
# Run lint with fix flag for Angular CLI
for FILE in $STAGED_FILES; do
  echo "Linting $FILE"
  npm run lint -- --fix --lint-file-patterns "$FILE" || { echo "❌ Linting failed for $FILE"; exit 1; }
done

# If linting has changed files, add them back to staging
echo "✅ Adding linted files back to staging area..."
git add $STAGED_FILES || { echo "❌ Failed to add files back to staging"; exit 1; }

# Skip commit message generation as it might be causing issues
# We'll let the commit-msg hook handle it instead

echo "🚀 Pre-commit hook code linting completed successfully!"
exit 0
