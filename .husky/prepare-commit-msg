# The commit message file is passed as the first parameter
COMMIT_MSG_FILE=$1
# The commit source is passed as the second parameter
COMMIT_SOURCE=$2

# Only modify the message if it's a default commit (not from merge, template, etc.)
if [ -z "$COMMIT_SOURCE" ]; then
  # Generate a commit message based on branch name
  BRANCH_NAME=$(git symbolic-ref --short HEAD 2>/dev/null || echo "")
  TICKET_NUMBER=$(echo $BRANCH_NAME | grep -o -E '(feature|bugfix|hotfix)/[A-Z]+-[0-9]+' | grep -o -E '[A-Z]+-[0-9]+')

  # Count linted files
  LINTED_FILES=$(git diff --cached --name-only | wc -l | tr -d ' ')

  if [ -n "$TICKET_NUMBER" ]; then
    # If we have a ticket number from the branch, use it in a conventional commit format
    echo "fix($TICKET_NUMBER): auto-lint $LINTED_FILES files" > $COMMIT_MSG_FILE
  else
    # Otherwise use a generic conventional commit message
    echo "chore: auto-lint $LINTED_FILES files" > $COMMIT_MSG_FILE
  fi

  echo "📝 Auto-generated commit message for linting changes"
fi
