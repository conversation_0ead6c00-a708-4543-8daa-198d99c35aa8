{"eslint.options": {"extensions": [".ts", ".html"]}, "eslint.validate": ["javascript", "typescript", "html"], "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[html][typescript]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}}