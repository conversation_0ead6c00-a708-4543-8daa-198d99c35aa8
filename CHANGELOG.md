# Change Log

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

## [7.1.0]

- [MEXP-667](https://aplazo.atlassian.net/browse/MEXP-667) - fix bug for service worker updater toastr
- [MEXP-661](https://aplazo.atlassian.net/browse/MEXP-661) - get active banner with platform field added

## [7.0.0-RELEASE] - 2025-07-09

- [MEXP-637](https://aplazo.atlassian.net/browse/MEXP-637) - Upgrade Angular to 20

## [6.12.0] - 2025-06-18

- [MEXP-625](https://aplazo.atlassian.net/browse/MEXP-625) - Add “Integración” section to the Merchant Panel navigation

## [6.11.0-RELEASE] - 2025-06-16

- [MEXP-534](https://aplazo.atlassian.net/browse/MEXP-534) - Redesign Monthly Summary Dashboard with Floating Cards Layout
- [MEXP-602](https://aplazo.atlassian.net/browse/MEXP-602) - new loader

## [6.10.0-RELEASE] - 2025-06-05

- [MEXP-547](https://aplazo.atlassian.net/browse/MEXP-547) - remove unused labels from balance status

## [6.9.0-RELEASE] - 2025-05-19

- [MEXP-580](https://aplazo.atlassian.net/browse/MEXP-580) - Change statsig identifiers

## [6.8.0-RELEASE] - 2025-05-15

- [MEXP-571](https://aplazo.atlassian.net/browse/MEXP-571) - replace feature flag dependency with statsig
- [MEXP-540](https://aplazo.atlassian.net/browse/MEXP-540) - Create ROLE_PANEL_MANAGER - Frontend (panel)

## [6.7.0-RELEASE] - 2025-04-29

- [MEXP-481](https://aplazo.atlassian.net/browse/MEXP-481) - PWA simple installation and implementation

## [6.6.2-RELEASE] - 2025-04-14

- [MEXP-489](https://aplazo.atlassian.net/browse/MEXP-489) - add a new section información del Comercio-Front
- [MEXP-466](https://aplazo.atlassian.net/browse/MEXP-466) - allow to delete or edit a contact

## [6.6.1-RELEASE] - 2025-03-31

- [MEXP-289](https://aplazo.atlassian.net/browse/MEXP-289) - change to new login centralized service

## [6.6.0-RELEASE] - 2025-03-19

- [MEXP-433](https://aplazo.atlassian.net/browse/MEXP-433) - environment variables injection by aws secrets manager
- [MEXP-446](https://aplazo.atlassian.net/browse/MEXP-446) - add maintenance mode

## [6.5.4-RELEASE] - 2025-02-27

- revert the change of the domain to `com.mx`

## [6.5.3-RELEASE] - 2025-02-27

- change prod domain to `com.mx`

## [6.5.2-RELEASE] - 2025-02-12

- [MEXP-292](https://aplazo.atlassian.net/browse/MEXP-292) - fix rfc data source

## [6.5.1-RELEASE] - 2025-01-23

- [MEXP-253](https://aplazo.atlassian.net/browse/MEXP-253) - improve reports names

## [6.5.0-RELEASE] - 2025-01-10

- [MEXP-214](https://aplazo.atlassian.net/browse/MEXP-214) - add new refund status
- [MEXP-229](https://aplazo.atlassian.net/browse/MEXP-229) - remove unnecessary refund status

## [6.4.0-RELEASE] - 2024-12-18

- improve DX for development and CI
- [MEXP-188](https://aplazo.atlassian.net/browse/MEXP-188) - fix border overlap datepicker component

## [6.3.1-RELEASE] - 2024-12-12

- solve bug that prevents click of a button within a form

## [6.3.0-RELEASE] - 2024-12-12

- [MEXP-77](https://aplazo.atlassian.net/browse/MEXP-77) - improve datepicker component

## [6.2.0-RELEASE] - 2024-11-27

- [MEXP-39](https://aplazo.atlassian.net/browse/MEXP-39) - refund list
- [MEXP-121](https://aplazo.atlassian.net/browse/MEXP-121) - improve multi select component

## [6.1.0-RELEASE] - 2024-10-29

- [MEXP-70](https://aplazo.atlassian.net/browse/MEXP-70) - replace split with statsig

## [6.0.1-RELEASE] - 2024-10-03

- [APM-2967](https://aplazo.atlassian.net/browse/APM-2967) - fix typo for the refund button

## [6.0.0-RELEASE] - 2024-09-23

- [APM-2841](https://aplazo.atlassian.net/browse/APM-2841) - Upgrade Angular to 17

## [5.13.0]

- [APM-2700](https://aplazo.atlassian.net/browse/APM-2700) - [MP] - Tagging Refund Button

## [5.12.1-RELEASE] - 2024-09-10

- [APM-2867](https://aplazo.atlassian.net/browse/APM-2867) - hotjar

## [5.12.0-RELEASE] - 2024-09-03

- [APM-2724](https://aplazo.atlassian.net/browse/APM-2724) - add hotjar events to refund success

## [5.11.0-RELEASE] - 2024-09-02

- [APM-2659](https://aplazo.atlassian.net/browse/APM-2659) - download invoice and invoices

## [5.10.0-RELEASE] - 2024-08-15

- [APM-2488](https://aplazo.atlassian.net/browse/APM-2488) - Change search component for a box style
-

## [5.9.0] - 2024-08-08

### Changed

- [APM-2570](https://aplazo.atlassian.net/browse/APM-2570) - Enhanced balance statement with a new download-receipt-option
- [APM-2614](https://aplazo.atlassian.net/browse/APM-2614) - Enhanced balance-details statement with a new download-receipt-option
- [APM-2544](https://aplazo.atlassian.net/browse/APM-2544) - Remove disputes select options to avoid confusion with merchants
- [APM-2583](https://aplazo.atlassian.net/browse/APM-2583) - analytics for receipt download in balance view
- [APM-2576](https://aplazo.atlassian.net/browse/APM-2576) - Receipt download for all the balances in one month
-

## [5.8.0-RELEASE] - 2024-07-19

- [APM-2528](https://aplazo.atlassian.net/browse/APM-2528) - Sidebar menu click outside improvements.
- [APM-2534](https://aplazo.atlassian.net/browse/APM-2534) - Admin refunds prefix
- [APM-2556](https://aplazo.atlassian.net/browse/APM-2556) - Remove refunds feature flag

## [5.7.0-RELEASE] - 2024-06-27

- [APM-2459](https://aplazo.atlassian.net/browse/APM-2459) - handle usecase for request refund in cancelled loan
- [APM-2480](https://aplazo.atlassian.net/browse/APM-2480) - Update login form i18n
- [APM-2478](https://aplazo.atlassian.net/browse/APM-2478) - improve stat cards
- [APM-2476](https://aplazo.atlassian.net/browse/APM-2476) - Align tool-tip to headlines

## [5.6.1-RELEASE] - 2024-06-18

- [APM-2472](https://aplazo.atlassian.net/browse/APM-2472) - fix register url

## [5.6.0-RELEASE] - 2024-06-18

- [APM-2441](https://aplazo.atlassian.net/browse/APM-2441) - request refund for admin role

## [5.5.1] - 2024-06-04

- [APM-2408](https://aplazo.atlassian.net/browse/APM-2408) - clear selected loan when status change

## [5.5.0-RELEASE] - 2024-04-25

- [APM-2199](https://aplazo.atlassian.net/browse/APM-2199) - improve SonarQube analysis

## [5.4.2-RELEASE] - 2024-03-25

- retrieve and used only the necesary flags for the project

## [5.4.1-RELEASE] - 2024-03-21

- fix final amount in [balance](src/app/features/balance/pages/balance/balance.component.ts) component

## [5.4.0-RELEASE] - 2024-03-20

- [APM-2184](https://aplazo.atlassian.net/browse/APM-2134) - info del comercio

## [5.3.0-RELEASE] - 2024-03-14

- improve SonarQube Code Quality coverage
- [APM-2217](https://aplazo.atlassian.net/browse/APM-2217) - Fixing UI issues

## [5.2.0-RELEASE] - 2024-03-13

- remove `@aplazo/merchant-ui`
- Notification QA Wolf
- [APM-2134](https://aplazo.atlassian.net/browse/APM-2134) - QA Wolf

## [5.1.6-RELEASE] - 2024-03-07

- fix status loan selection label typos

## [5.1.5-RELEASE] - 2024-03-06

- [APM-2175](https://aplazo.atlassian.net/browse/APM-2175) - Remove clarifications feature flag
- [APM-2176](https://aplazo.atlassian.net/browse/APM-2176) - Remove Split IO flag loan status label

## [5.1.4-RELEASE] - 2024-02-22

- fix page title for clarification

## [5.1.3-RELEASE]

- [APM-2107](https://aplazo.atlassian.net/browse/APM-2107) - Add new disputes status

## [5.1.2-RELEASE] - 2024-02-06

- [APM-2094](https://aplazo.atlassian.net/browse/APM-2094) - Merchant Panel - Disable Kustomer split IO

## [5.1.1-RELEASE] - 2024-01-30

- [APM-2045](https://aplazo.atlassian.net/browse/APM-2045) - Merchant Panel - Enable recover password for new roles
- [APM-2061](https://aplazo.atlassian.net/browse/APM-2061) - [ Payment Balance ] Improve experience
- [APM-2071](https://aplazo.atlassian.net/browse/APM-2071) - PAYMENT BALANCE - To Pay issue

## [5.1.0-RELEASE] - 2024-01-18

- [APM-2049](https://aplazo.atlassian.net/browse/APM-2049) - Payment Balance feature flag
- [APM-2050](https://aplazo.atlassian.net/browse/APM-2050) - Payment Balance - start date handling

- remove `@aplazo/merchant-domain`
- remove `@aplazo/merchant-data-access`
- remove `@aplazo/merchant-utils`
- update [Dockerfile](Dockerfile) to use node `20.10.0`
- update [Jenkinsfile](Jenkinsfile) to use static nexus registry

### Changed

- Add new Feature Flag to handle the usecase of login in the Aplazo's VPN migration
- Add role `ROLE_ADMIN_INC` to routes by role config file

## [5.0.1-RELEASE] - 2024-01-10

- [APM-2037](https://aplazo.atlassian.net/browse/APM-2037) - change from "aclaratoria" to "aclaración" in the clarifications page

## [5.0.0-RELEASE] - 2023-12-19

- [APM-1973](https://aplazo.atlassian.net/browse/APM-1973) - add new Google Analytics events to refunds and clarifications pages
- [APM-1983](https://aplazo.atlassian.net/browse/APM-1983) - Merchant Panel : Implement Kustomer

### Changed

- update angular version to 15

## [4.17.1-RELEASE] - 2023-11-24

- [APM-1980](https://aplazo.atlassian.net/browse/APM-1980) - fix handle response from new balance report service

## [4.17.0-RELEASE] - 2023-11-07

### Changed

- update `@aplazo/front-feature-flags` to `0.2.0`
- [routes by role](src/app/features/home/<USER>

### Remove

- protractor

## [4.16.0-RELEASE] - 2023-10-17

- [APM-1838](https://aplazo.atlassian.net/browse/APM-1838) - add clarifications view

## [4.15.1-RELEASE] - 2023-10-12

- add [UnavailableGuard](src/app/features/home/<USER>/unavailable.guard.ts) to prevent access into the designated route when some feature flag is enabled
- add [UnavailableComponent](src/app/features/unavailable/unvailable.component.ts) to show a message when some page view is unavailable

## [4.15.0-RELEASE] - 2023-10-04

- [APM-1748](https://aplazo.atlassian.net/browse/APM-1748) - add download summary report to payment balance view
- [APM-1803](https://aplazo.atlassian.net/browse/APM-1803) - allow to retrieve info from all selected branches with some special logic

### Changed

- [`paymenSettlementComponent`](src/app/features/payment-settlement/pages/payment-settlement.component.ts) - implement new `branchOfficesSelection` event to get info from list and summary with the new logic for all branches selected
- [`paymentSettlementResolver`](src/app/features/payment-settlement/resolvers/criteria.resolver.ts) - load the initial branchOffices to the store with the new logic for all branches selected

## [4.14.0-RELEASE] - 2023-07-24

- [APM-1591](https://aplazo.atlassian.net/browse/APM-1591) - allow login with email or username
- [APM-1594](https://aplazo.atlassian.net/browse/APM-1594) - restrict access to certain routes depending on the user role
- [APM-1628](https://aplazo.atlassian.net/browse/APM-1628) - add constraint to allow only certain merchants to see the balances info

### Changed

- update `@aplazo/merchant-domain` to `2.0.0`
- update `@aplazo/merchant-utils` to `2.0.0`
- update `@aplazo/merchant-data-access` to `2.0.0`
- update `@aplazo/merchant-ui` to `3.0.0`
- [`loginComponent`](src/app/features/login/pages/login/login.component.ts) - to allow login with email or username
- [`homeComponent`](src/app/features/home/<USER>/home/<USER>
- change logic to consume userStoreService from a single source and prevent memory leaks from multiple instances
- change [`accountPageComponent`](src/app/features/account/pages/account/account.component.ts) to shown change password section only for _ROLE_MERCHANT_
- [`balanceComponent`](src/app/features/balance/pages/balance/balance.component.ts) - to add constraint to allow only certain merchants to see the balances info
- update eslintrc

### Added

- [`routesByRole`](src/app/features/home/<USER>
- [`roleGuard`](src/app/features/home/<USER>/role.guard.ts) - guard that restrict access to certain routes depending on the user role

### Removed

- `ngrx` from the project

## [4.13.1-RELEASE] - 2023-06-21

- [APM-1596](https://aplazo.atlassian.net/browse/APM-1596) - loan status it was showing incorrect status

### Fixed

- [`dashboardComponent`](src/app/features/dashboard/pages/dashboard/dashboard.component.ts) - to fix the loan status
- [`paymentSettlementComponent`](src/app/features/payment-settlement/pages/payment-settlement.component.ts) - to fix the loan status

## [4.13.0-RELEASE] - 2023-06-20

- [APM-1571](https://aplazo.atlassian.net/browse/APM-1571) - add feature flag to loan status for showing/hiding the loan status select combo
- [APM-1572](https://aplazo.atlassian.net/browse/APM-1572) - change wording for payment navlink and some loan status options

### Changed

- [`dashboardComponent`](src/app/features/dashboard/pages/dashboard/dashboard.component.ts) - to improve the loan status select combo to be shown/hidden depending on the feature flag value
- [`paymentSettlementComponent`](src/app/features/payment-settlement/pages/payment-settlement.component.ts) - to improve the loan status select combo to be shown/hidden depending on the feature flag value

## [4.12.0-RELEASE] - 2023-05-23

- [APM-1511](https://aplazo.atlassian.net/browse/APM-1511) - add refund-type selection to refunds page
- [APM-1512](https://aplazo.atlassian.net/browse/APM-1512) - improve refunds summary to show each requested and refunded operations information

### Changed

- [`refundComponent`](src/app/features/refund/pages/refunds/refunds.component.ts) - add refund-type selection to refunds page
- update `@aplazo/merchant-ui` to `2.1.0`
- update `@aplazo/merchant-data-access` to `1.8.0`
- update `@aplazo/merchant-utils` to `1.11.0`
- update `@aplazo/merchant-domain` to `1.8.0`

## [4.11.0-RELEASE] - 2023-05-11

- [APM-1327](https://aplazo.atlassian.net/browse/APM-1327) - add events to payment settlement page
- [APM-1476](https://aplazo.atlassian.net/browse/APM-1476) - improve balance feature flag for allowed only certain merchants

### Changed

- [`paymentSettlementComponent`](src/app/features/payment-settlement/pages/payment-settlement.component.ts) - add custom track events to google analytics for payment settlement page
- update `@aplazo/merchant-ui` to `2.0.0`
- update `@aplazo/web-ui` to `3.0.0`

## [4.10.0-RELEASE] - 2023-03-08

- [APM-1346](https://aplazo.atlassian.net/browse/APM-1346) - sale report improvements
- [APM-1345](https://aplazo.atlassian.net/browse/APM-1345) - payment report improvements
- [APM-1325](https://aplazo.atlassian.net/browse/APM-1325) - add events to "ventas por dia" page
- [APM-1326](https://aplazo.atlassian.net/browse/APM-1326) - add events to legacy report page
- [APM-1328](https://aplazo.atlassian.net/browse/APM-1328) - add events to refunds page

### Changed

- [`legacyReportComponent`](src/app/features/legacy-report/pages/legacy-report/legacy-report.component.ts) - improve component to allow dates before current 3 months but with a range not greater than 3 months. At the same time adding custom track events to google analytics.
- [`paymentSettlementComponent`](src/app/features/payment-settlement/pages/payment-settlement.component.ts) - improve component to allow dates before current 3 months but with a range not greater than 3 months.
- [`weekdayscomponent`](src/app/features/loans-days-statistics/pages/weekdays/weekdays.component.ts) - add custom track events to google analytics for loans days statistics page (weekdays view)
- [`hoursComponent`](src/app/features/loans-days-statistics/pages/hours/hours.component.ts) - add custom track events to google analytics for loans days statistics page (hours view)
- [`dayHoursComponent`](src/app/features/loans-days-statistics/pages/day-hours/day-hours.component.ts) - add custom track events to google analytics for loans days statistics page (day hours view)
- [`refundsComponent`](src/app/features/refund/pages/refunds/refunds.component.ts) - add custom track events to google analytics for refunds page

### Added

- [`eventManager`](src/app/services/event-manger.service.ts) - to handle the events to be sent to Google Analytics

## [4.9.0] - 2023-02-27

- [APM-1324](https://aplazo.atlassian.net/browse/APM-1324) - Add custom track events to Google Analytics for dashboard page (loans view)

### Changed

- upgrade `@aplazo/merchant-data-access` to version `1.7.0`
- upgrade `@aplazo/merchant-utils` to version `1.10.0`

---

## [4.8.0-RELEASE] - 2023-02-16

- [APM-1310](https://aplazo.atlassian.net/browse/APM-1310) - Add Tag Manager to Aplazo's Merchants Dashboard
- [APM-1272](https://aplazo.atlassian.net/browse/APM-1272) - Add Google Analytics to Aplazo's Merchants Dashboard

### Changed

- upgrade `@aplazo/merchant-data-access` to version `1.6.1`
- upgrade `@aplazo/merchant-utils` to version `1.9.0`
- upgrade `@aplazo/merchant-merchant-ui` to version `1.8.0`
- upgrade `@aplazo/merchant-domain` to version `1.7.1`

### Added

- env variable for Tag Manager Id
- `tagManagerService` from `@aplazo/merchant-utils` to handle the Google Tag Manager
- execute `trackEvent` from `tagManagerService` on `appComponent` with every navigation end event to track the page view

---

## [4.7.0-RELEASE] - 2023-01-19

### Added

- `@aplazo/frontend-feature-flags` - to handle the feature flags
- [`balanceDateGuard`](src/app/features/home/<USER>/balance-date.guard.ts) - to apply a customization over the canActivateGuard and handle a custom dynamic date when the flag is enabled

### Changed

- [`homeRoutingModule`](src/app/features/home/<USER>
- [`homeComponent`](src/app/features/home/<USER>/home/<USER>
- [`balanceCriteriaService`](src/app/features/balance/services/criteria-balance.service.ts) - to add a dynamic date when the flag is enabled

---

## [4.6.0-RELEASE] - 2022-12-22

- [APM-1146](https://aplazo.atlassian.net/browse/APM-1146) - Set the initial date for the consult of the merchant's balances
- [APM-1214](https://aplazo.atlassian.net/browse/APM-1214) - Remove info heading component

### Changed

- upgrade `@aplazo/merchant-data-access` to version `1.6.0`
- upgrade `@aplazo/merchant-domain` to version `1.7.0`
- upgrade `@aplazo/merchant-ui` to version `1.8.0`
- upgrade `@aplazo/merchant-utils` to version `1.9.0`
- remove `infoHeadingComponent` from [`homeComponent`](src/app/features/home/<USER>/home/<USER>

---

## [4.5.0-RELEASE] - 2022-12-08

- [APM-1155](https://aplazo.atlassian.net/browse/APM-1155) - Update view for Merchant info
- [APM-1156](https://aplazo.atlassian.net/browse/APM-1156) - Update view for Merchant Billing info
- [APM-1157](https://aplazo.atlassian.net/browse/APM-1157) - Update view for Change password
- [APM-1158](https://aplazo.atlassian.net/browse/APM-1158) - Update view for get Api Key

### Added

- [`profileModule`](src/app/features/profile/profile.module.ts) - to handle the lazy loading of the profile module
- [`bankingInfoModule`](src/app/features/banking-info/banking-info.module.ts) - to handle the lazy loading of the banking info module
- [`changePasswordModule`](src/app/features/change-password/change-password.module.ts) - to handle the lazy loading of the change password module
- [`apiKeyModule`](src/app/features/get-api-key-sdk/get-api-key-sdk.module.ts) - to handle the lazy loading of the api key module

### Changed

- [`appModule`](src/app/app.module.ts) - to add `ngxMask` module
- upgrade `@aplazo/merchant-data-access` to version `1.5.0`
- upgrade `@aplazo/merchant-domain` to version `1.6.0`
- upgrade `@aplazo/merchant-ui` to version `1.6.0`
- upgrade `@aplazo/merchant-utils` to version `1.7.0`

### Fixed

- [`accessTokenInterceptor](src/app/interceptors/user-access-token.interceptor.ts) - to fix the execution of logout when the error response have an 403 status

---

## [4.4.0-RELEASE] - 2022-11-29

- [APM-1109](https://aplazo.atlassian.net/browse/APM-1109) - Download refunds report

### Changed

- update `@aplazo/merchant-ui` to version `1.5.0`
- update `@aplazo/merchant-data-access` to version `1.4.0`
- update `@aplazo/merchant-utils` to version `1.6.0`
- update `@aplazo/merchant-domain` to version `1.5.0`
- [`refundsPage`](src/app/features/refund/pages/refunds/refunds.component.ts) - to include the download report usecase

---

## [4.3.1-RELEASE] - 2022-11-18

- update `@aplazo/merchant-ui` to version `1.4.1`
- update `@aplazo/merchant-data-access` to version `1.3.1`
- update `@aplazo/merchant-utils` to version `1.4.1`
- update `@aplazo/merchant-domain` to version `1.4.1`

---

## [4.3.0-RELEASE] - 2022-11-01

- [APM-950](https://aplazo.atlassian.net/browse/APM-950) - Payment Balance list
- [APM-951](https://aplazo.atlassian.net/browse/APM-951) - Payment Balance download report
- [APM-952](https://aplazo.atlassian.net/browse/APM-952) - Payment Balance search by id
- [APM-1028](https://aplazo.atlassian.net/browse/APM-1028) - Payment Balance Details
- components' refactor for improve I18n resolution with scoped blocks

### Added

- [`balanceModule`](src/app/features/balance/balance.module.ts) - as a complete feature lazy loaded from main router

### Changed

- update `@aplazo/partner-styles` to version `2.1.0`
- update `@aplazo/merchant-ui` to version `1.4.0`
- update `@aplazo/merchant-data-access` to version `1.3.0`
- update `@aplazo/merchant-utils` to version `1.4.0`
- update `@aplazo/merchant-domain` to version `1.4.0`
- add loader while `i18n service` is retrieving the data

---

## [4.2.0-RELEASE]

- [APM-880](https://aplazo.atlassian.net/browse/APM-880) - use new endpoint for loan details
- [APM-824](https://aplazo.atlassian.net/browse/APM-824) - use new endpoint for daily sales statistics
- [APM-825](https://aplazo.atlassian.net/browse/APM-825) - use new endpoint for legacy reports
- [APM-826](https://aplazo.atlassian.net/browse/APM-826) - use new endpoint for payment reports
- [APM-827](https://aplazo.atlassian.net/browse/APM-827) - use new endpoint for refunds
- [APM-828](https://aplazo.atlassian.net/browse/APM-828) - use new endpoint for company info
- [APM-881](https://aplazo.atlassian.net/browse/APM-881) - use new endpoint for search loan

### Changed

- update `@aplazo/merchant-domain` to version `1.3.0`
- update `@aplazo/merchant-utils` to version `1.3.0`
- update `@aplazo/merchant-data-access` to version `1.2.0`
- update `@aplazo/merchant-ui` to version `1.3.0`
- improve [`paymentSettlementComponent`](src/app/features/payment-settlement/pages/payment-settlement.component.ts)

## [4.1.0-RELEASE] - 2022-08-04

- [APM-821](https://aplazo.atlassian.net/browse/APM-821) - change one property from refunds entitites, take totalAmount to refundAmount

### Changed

- update `@aplazo/merchant-domain` library
- update `@aplazo/merchant-utils` library
- update `@aplazo/merchant-data-access` library
- update `@aplazo/merchant-ui` library
- update [`refunds-component`](src/app/features/refund/pages/refunds/refunds.component.ts)
- update [`i18n/es.json`](src/assets/i18n/es.json)
- update `home-component` to change the form of render label in menu button layout

## [4.0.0 - RELEASE] - 2022-07-14

### Changed

- Upgrade angular version
- Upgrade libraries versions
- Change linter from tslint to eslint-prettier
- Prune dependencies and move libraries to devdependencies for development mode
- fix `daterange-picker` handle emision event from `merchant-ui` library

## [3.1.1 - RELEASE] - 2022-06-30

- fix broken styles chrome's latest release

## [3.1.0-RELEASE] - 2022-06-16

[APM-598](https://aplazo.atlassian.net/browse/APM-598)
[APM-648](https://aplazo.atlassian.net/browse/APM-648)
[APM-649](https://aplazo.atlassian.net/browse/APM-649)

### Added

- `refunds` module and lazy loading into home routing module
- add refunds route
- refund list use case

### Changed

- `criteria-resolver` from daily loans stats
- `loans-list` change initial loans from _all_ to _approved_
- fix the min date from `settlements-payment` and inject into `daterange-picker` component
- `loader` delay to improve the UX
- `daily-stats` have a common criteria to improve UX

## [3.0.0 - RELEASE] - 2022-06-02

## Added

- `@aplazo/merchant-domain`
- `@aplazo/merchant-utils`
- `@aplazo/merchant-data-access`
- `@aplazo/merchant-ui`
- `app-routes` core injection
- `merchant-core-environment` core injection
- Login use-case
- Re-login use-case
- Forgot password use-case
- Verification token use-case
- Set new password use-case
- Legacy report use-case
- Logout use-case
- Loans-list use-case
- Comparison-stats use-case
- Loans-stats use-case
- Loans-stats-by-weekday use-case
- Loans-stats-by-hour use-case
- Loans-stats-by-day-and-hour use-case
- Search-loan use-case
- Settlements payments list use-case
- Settlements payments report use-case
- Settlements payments search use-case
- Settlements payments statistics use-case
- `info-heading` on main layout

## Changed

- remove `@aplazo/web-ui` from dependencies and add as a dev dependency
- remove dependency `@ngrx/effects`
- remove dependency `@aplazo/partner-core`
- remove old dashboard module
- remove old profile module
- remove old authentication module
- remove old report module
- remove old legacy report module
- remove partner core environment
- improve bearer token interceptor
- improve is auth guards
- fix bug from `loans-comparison-usecase`
- centralize dependency injection of services from app-module

## [2.1.0-RELEASE] - 2022-05-10

- [514](https://aplazo.atlassian.net/browse/APM-514)
- [APM-543](https://aplazo.atlassian.net/browse/APM-543)
- [APM-544](https://aplazo.atlassian.net/browse/APM-544)
- [APM-546](https://aplazo.atlassian.net/browse/APM-546)

## Added

- functionality to search within payment reports
- New endpoint to legacy-report
- Exposed `payment-reports-component`

### Changed

- update `@aplazo/web-ui` to `1.8.0`
- update `@aplazo/partner-core` to `1.5.0`
- update `@aplazo/partner-styles` to `1.3.0`
- `lottie-web` versioning only for fixes
- `ngx-lottie` versioning only for fixes
- change the path for a payment-reports
- remove unused columns from payment-reports
- clear state from payments `report` on destroy lifecycle
- tooltips for `paument-reports-component` help icons
- hide pending status on `loans-status`
- Add padding on `payment-report-component`

## [2.0.0-RELEASE] - 2022-03-29

### Added

- store for `branch-offices`.
- store for `payments&settlements`
- `DateService` for handle date ranges
- split multiple components to used as a dumb components
  1. `Params-config-component`
  1. `Searchbar-component`
  1. `Settlement-stats-component`
  1. `Pagination-component`
  1. `Legacy-report-component`
  1. `Payments-component`
  1. `Payments-count-component`

### Changed

- `@aplazo/web-ui` to `1.4.0`
- `@aplazo/partner-core` to `1.3.0`
- `@aplazo/partner-styles` to `1.1.0`
- `imageIcons` enum
- `routesNames` enum
- `loan-table-component`
- `report-downloaded-componente`
- `footer-component`
- `main-lauyout-component`
- `stats-comparison-component`
- `dashboard-layout-component`
- `dynamic-stats-component`
- `status-form-component`
- new `reports-layout-component`
- `merchant-api-service`
- `merchant-dashboard-service`
- `settlement-payments-service`
- improve logic over loans store
- new entries on local environment
- fix typos and change text for empty state on dashboard loans
- refactor `loans-status.directive` & tests
- new endpoint to download `payment-settlements-report`

## [1.1.0]

### Changed

- Remove **_store devtools_** from production

## [1.0.0-RELEASE] - 2022-03-01

### Added

- New merchant dashboard
