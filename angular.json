{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"merchant-online-dashboard": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@ngx-env/builder:application", "options": {"outputPath": "dist/merchant-online-dashboard", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "manifest.webmanifest", "input": "src/", "output": "/"}], "styles": ["node_modules/ngx-toastr/toastr.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css", "src/styles.scss"], "preserveSymlinks": true, "allowedCommonJsDependencies": ["*"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "serviceWorker": "ngsw-config.json"}, "stage": {"optimization": true, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}], "serviceWorker": "ngsw-config.json"}, "development": {"preserveSymlinks": true, "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "15mb"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@ngx-env/builder:dev-server", "options": {"open": false, "host": "0.0.0.0"}, "configurations": {"production": {"buildTarget": "merchant-online-dashboard:build:production"}, "stage": {"buildTarget": "merchant-online-dashboard:build:stage"}, "development": {"buildTarget": "merchant-online-dashboard:build:development"}}, "defaultConfiguration": "stage"}, "extract-i18n": {"builder": "@ngx-env/builder:extract-i18n", "options": {"buildTarget": "merchant-online-dashboard:build"}}, "test": {"builder": "@ngx-env/builder:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "inlineStyleLanguage": "scss", "preserveSymlinks": true, "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["node_modules/ngx-toastr/toastr.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css", "src/styles.scss"], "scripts": [], "karmaConfig": "karma.conf.js", "browsers": "Chrome"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": false, "cache": {"enabled": false}}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}