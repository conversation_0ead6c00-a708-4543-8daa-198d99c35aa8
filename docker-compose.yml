version: '3'
services:
  dash_local:
    container_name: merchantPanelLocal
    image: merchant_dash_local
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 4200:80

  dash_local_test:
    container_name: merchantPanelLocalTest
    image: merchant_dash_local_test
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - merchant_dash_local_test_coverage_vol:/usr/src/app/coverage

  dash_local_dev:
    container_name: merchantPanelLocalDev
    image: dash_local_dev
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
        ENV: ${ENV}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - '/usr/src/app/node_modules'
      - '.:/usr/src/app'

  # Tagged image services for CI/CD pipeline
  tagged1:
    image: ${TAG_1}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env

  tagged2:
    image: ${TAG_2}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env

volumes:
  dash_app:
  merchant_dash_local_test_coverage_vol:
