import { expect, test } from '@playwright/test';

test('login render normally', async ({ page }) => {
  await page.goto('/');

  await expect(page).toHaveTitle(
    /Aplazo | Paga tus compras online en cuotas sin tarjeta de crédito/
  );
  await expect(page.getByRole('heading').first()).toBeVisible();
});

test('login render after statsig failed', async ({ page }) => {
  const warnLogs: string[] = [];

  test.setTimeout(300000);

  page.on('console', msg => {
    if (msg.type() === 'warning') {
      warnLogs.push(msg.text());
    }
  });

  await page.route('https://featureassets.org/**', async route => {
    await route.fulfill({
      status: 500,
    });
  });
  await page.goto('/');

  await expect(page).toHaveTitle(/Aplazo | Offline PoS/);
  await expect(page.getByRole('heading').first()).toBeVisible({
    timeout: 20000,
  });

  await expect(warnLogs).toContain('Statsig::Initialization::Failed');
});
