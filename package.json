{"name": "merchant-online-dashboard", "version": "7.1.0", "description": "Online dashboard for Aplazo Merchants", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://aplazo.mx", "email": "<EMAIL>"}, "homepage": "https://merchantui.aplazo.mx", "repository": {"type": "git", "url": "https://github.com/aplazo/angular.merchant-online-dashboard"}, "engines": {"node": ">=22.13.0", "angular": ">=20.0.0"}, "scripts": {"ng": "ng", "dev:dev": "ng serve --configuration development", "dev:stg": "ng serve --configuration stage", "dev:prod": "ng serve --configuration production", "build": "ng build --configuration production", "test:watch": "ng test --code-coverage", "test": "ng test --code-coverage --no-watch --no-progress --browsers=ChromeHeadlessNoSandbox", "e2e": "CI=true playwright test", "lint": "ng lint", "prepare": "husky", "prettier": "prettier --write src/app/**/*.{ts,js,css,html}"}, "private": true, "dependencies": {"@angular/animations": "^20.1.0", "@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/platform-browser-dynamic": "^20.1.0", "@angular/router": "^20.1.0", "@angular/service-worker": "^20.1.0", "@aplazo/front-analytics": "~3.2.0", "@aplazo/front-observability": "~3.2.0", "@aplazo/i18n": "~3.2.0", "@aplazo/merchant": "~3.2.0", "@aplazo/shared-ui": "~3.2.0", "@aplazo/ui-icons": "~3.2.0", "@aplazo/workers": "~3.2.0", "@datadog/browser-logs": "~6.6.0", "@datadog/browser-rum": "~6.6.0", "@date-fns/utc": "~2.1.0", "@jsverse/transloco": "~7.4.0", "@ngneat/dialog": "~5.1.0", "@statsig/angular-bindings": "~3.16.2", "@statsig/session-replay": "~3.16.2", "@statsig/web-analytics": "~3.16.2", "chart.js": "~4.4.0", "date-fns": "~4.1.0", "date-fns-tz": "^3.1.3", "nanoid": "5.0.8", "ngx-mask": "^19.0.7", "ngx-toastr": "~19.0.0", "rxjs": "~7.8.0", "tslib": "^2.6.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.1.0", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.1.0", "@angular/compiler-cli": "^20.1.0", "@auth0/angular-jwt": "~5.2.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@ngx-env/builder": "^19.0.0", "@playwright/test": "1.53.2", "@types/jasmine": "~3.8.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^22.13.0", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "air-datepicker": "~3.5.3", "autoprefixer": "^10.4.16", "esbuild": "0.25.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^9.1.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "~8.5.5", "postcss-scss": "4.0.9", "prettier": "~3.1.1", "prettier-eslint": "^16.1.2", "stylelint": "^16.21.0", "stylelint-config-standard": "^38.0.0", "stylelint-scss": "^6.12.0", "tailwindcss": "3.4.15", "ts-node": "~8.3.0", "typescript": "~5.8.3", "xlsx": "^0.18.5"}}