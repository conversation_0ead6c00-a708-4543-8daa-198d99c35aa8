import { AsyncPipe } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { LoaderService, WebchatService } from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';
import { filter, Subject, takeUntil } from 'rxjs';
import { EventManagerService } from './services/event-manger.service';

@Component({
  selector: 'app-root',
  imports: [AplazoPillLoaderComponent, RouterOutlet, AsyncPipe],
  template: `
    <router-outlet></router-outlet>
    <aplz-ui-pill-loader
      [loading]="(isLoading$ | async) === true"
      [messages]="loaderMessage$ | async"></aplz-ui-pill-loader>
  `,
})
export class AppComponent implements OnInit, OnD<PERSON>roy {
  readonly #webchatService = inject(WebchatService);
  readonly #loader = inject(LoaderService);
  readonly #router = inject(Router);
  readonly #tagService = inject(EventManagerService);

  readonly #destroy$ = new Subject<void>();
  readonly isLoading$ = this.#loader.isLoading$;
  readonly loaderMessage$ = this.#loader.loaderMessage$;

  ngOnInit(): void {
    this.#router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.#destroy$)
      )
      .subscribe(() => {
        const href = document.location.href;

        if (href.endsWith('balance')) {
          this.#tagService.sendTrackEvent('balancePageView', {
            url: href,
            title: 'Account Statement',
          });

          return;
        }

        this.#tagService.sendTrackEvent('pageView', {});
      });

    this.#router.events
      .pipe(
        filter(evt => evt instanceof NavigationEnd),
        takeUntil(this.#destroy$)
      )
      .subscribe(() => {
        const url = this.#router.url;
        const isMainViews = !url.includes('authorization');

        if (!isMainViews) {
          this.#webchatService.hide();
          return;
        }

        this.#webchatService.show();
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
