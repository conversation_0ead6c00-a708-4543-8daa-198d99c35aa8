import { provideHttpClient, withInterceptors } from '@angular/common/http';
import {
  ApplicationConfig,
  importProvidersFrom,
  inject,
  isDevMode,
  provideAppInitializer,
} from '@angular/core';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideServiceWorker } from '@angular/service-worker';
import { provideMerchantsGTM } from '@aplazo/front-analytics/tag-manager';
import { provideObservability } from '@aplazo/front-observability';
import { provideI18N } from '@aplazo/i18n';
import {
  provideBrowserUtils,
  provideConnectionStatus,
  provideCustomErrorHandler,
  provideDateCalculator,
  provideJwtDecoder,
  provideKustomerWebchat,
  provideLoader,
  provideNotifier,
  provideRedirecter,
  provideTemporal,
  provideUseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideCsvMapper,
  provideXlsxFileGenerator,
} from '@aplazo/merchant/shared-dash';
import { provideScriptRenderer } from '@aplazo/shared-ui/script-renderer';
import { provideServiceWorkerUpdater } from '@aplazo/workers';
import { LogsInitConfiguration } from '@datadog/browser-logs';
import { RumInitConfiguration } from '@datadog/browser-rum';
import { STATSIG_INIT_CONFIG, StatsigService } from '@statsig/angular-bindings';
import { StatsigSessionReplayPlugin } from '@statsig/session-replay';
import { StatsigAutoCapturePlugin } from '@statsig/web-analytics';
import { provideNgxMask } from 'ngx-mask';
import { ToastNoAnimationModule } from 'ngx-toastr';
import { lastValueFrom, take } from 'rxjs';
import packageJson from '../../package.json';
import { routes } from './app.routes';
import {
  merchantCoreEnvironment,
  provideCoreEnvironment,
} from './config/merchant-core.environment';
import { provideBalanceTools } from './features/balance/infra/config/providers';
import { provideClarificationsRepositories } from './features/clarifications/infra/config/providers';
import { provideDashboardRefundsDeps } from './features/dashboard/infra/config/providers';
import { provideLoanRepositories } from './features/loan/infra/config/providers';
import { providePaymentSettlementsRepositories } from './features/payment-settlement/infra/config/providers';
import { provideRefunds } from './features/refund/infra/config/providers';
import { UserRefreshLoginUseCase } from './features/user/src/application/usecases/refresh-login.usecase';
import { User } from './features/user/src/domain/entities/user';
import {
  provideUserRepositories,
  provideUserServices,
} from './features/user/src/infra/config/providers';
import { unauthorizedInterceptor } from './interceptors/unauthorized.interceptor';
import { userAccessTokenInterceptor } from './interceptors/user-access-token.interceptor';

export function initServices(): () => Promise<User> {
  const featureFlagsService = inject(StatsigService);
  const usecase = inject(UserRefreshLoginUseCase);

  return async () => {
    const user = await lastValueFrom(usecase.execute().pipe(take(1)));

    if (user?.isLoggedIn) {
      await featureFlagsService.updateUserAsync({
        ...featureFlagsService.getClient()?.getContext().user,
        custom: {
          ...featureFlagsService.getClient()?.getContext().user.custom,
          merchantId: String(user.merchantId),
        },
        userID: String(user.merchantId),
        email: user.email,
      });

      featureFlagsService.logEvent('panel_front_login_refreshed', Date.now(), {
        merchantId: String(user.merchantId),
        role: user.role,
        username: user.username,
        lastLogin: user.lastLogin.getTime().toString(),
      });
    }

    return user;
  };
}

const settings = {
  sdkKey: merchantCoreEnvironment.featureFlagsApiKey,
  user: {
    appVersion: packageJson.version,
    userAgent: navigator.userAgent,
    locale: 'es-MX',
    custom: {
      appName: 'panel',
    },
  },
  options: {
    environment: {
      tier: merchantCoreEnvironment.featureFlagsEnv,
    },
    plugins: [new StatsigAutoCapturePlugin(), new StatsigSessionReplayPlugin()],
  },
};

const datadogConfig: RumInitConfiguration = {
  applicationId: merchantCoreEnvironment.observabilityAppId,
  clientToken: merchantCoreEnvironment.observabilityClientToken,
  env: merchantCoreEnvironment.observabilityEnv,
  service: merchantCoreEnvironment.observabilityService,
  sessionSampleRate:
    merchantCoreEnvironment.observabilityEnv === 'production' ? 8 : 0,
  allowedTracingUrls: [
    /https:\/\/(api|pos|merchant-acs|merchantdash|mpromotions)\.aplazo\.(mx)/,
  ],
  version: packageJson.version,
};

const datadogLoggerConfig: LogsInitConfiguration = {
  clientToken: merchantCoreEnvironment.observabilityClientToken,
  sessionSampleRate:
    merchantCoreEnvironment.observabilityEnv === 'production' ? 8 : 0,
  version: packageJson.version,
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideCoreEnvironment(),
    {
      provide: STATSIG_INIT_CONFIG,
      useValue: settings,
    },
    provideNoopAnimations(),
    provideRouter(routes),
    provideHttpClient(
      withInterceptors([unauthorizedInterceptor, userAccessTokenInterceptor])
    ),
    provideScriptRenderer(),
    provideObservability({
      rumConfig: datadogConfig,
      loggerConfig: datadogLoggerConfig,
    }),
    provideNgxMask(),
    importProvidersFrom(
      ToastNoAnimationModule.forRoot({
        closeButton: true,
        timeOut: 5000,
      })
    ),
    provideMerchantsGTM({
      apiKey: merchantCoreEnvironment.gtmId,
    }),
    provideKustomerWebchat({
      apiKey: merchantCoreEnvironment.webChatApiKey,
      brandId: merchantCoreEnvironment.webChatBrandId,
    }),
    provideAppInitializer(async () => {
      return await initServices()();
    }),
    provideUseCaseErrorHandler(),
    provideLoader(),
    provideNotifier(),
    provideRedirecter(),
    provideXlsxFileGenerator(),
    provideCsvMapper(),
    provideDateCalculator(),
    provideJwtDecoder(),
    provideUserServices(),
    provideUserRepositories(),
    provideLoanRepositories(),
    provideRefunds(),
    provideDashboardRefundsDeps(),
    provideBalanceTools(),
    provideClarificationsRepositories(),
    providePaymentSettlementsRepositories(),
    provideTemporal(),
    provideI18N({
      remoteUrl: merchantCoreEnvironment.i18nUrl,
      fallbackLocalUrl: '/assets/i18n',
    }),
    provideBrowserUtils(),
    provideCustomErrorHandler('chunkFile', 'offlineToastr'),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideConnectionStatus({
      notifierText: {
        title: 'Sin conexión a internet',
        message:
          'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
      },
      position: 'toast-bottom-left',
    }),
    provideServiceWorkerUpdater({
      pollInterval: 5 * 1000,
      dialogTitle: 'Actualización disponible',
      dialogMessage:
        'Una nueva versión está disponible. ¿Quieres actualizar ahora?',
    }),
  ],
};
