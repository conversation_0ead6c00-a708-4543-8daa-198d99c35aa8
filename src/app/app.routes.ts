import { Routes } from '@angular/router';
import { DASH_ROUTES } from './config/app-route-core';
import { isUnderMaintenanceGuard } from './guards/maintenance.guard';
import { preventNavigationByAuthenticatedUser } from './guards/only-authenticated-user.guard';
import { preventNavigationByUnauthenticatedUser } from './guards/only-unauthenticated-user.guard';
export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: DASH_ROUTES.authentication,
  },

  {
    path: DASH_ROUTES.legacyDashboard,
    pathMatch: 'full',
    redirectTo: DASH_ROUTES.authentication,
  },

  {
    path: DASH_ROUTES.legacyProfile,
    pathMatch: 'full',
    redirectTo: DASH_ROUTES.authentication,
  },

  {
    path: DASH_ROUTES.rootApp,
    loadChildren: () => import('./features/home/<USER>'),
    canActivate: [
      isUnderMaintenanceGuard,
      preventNavigationByAuthenticatedUser,
    ],
  },

  {
    path: DASH_ROUTES.authentication,
    loadChildren: () => import('./features/auth/auth.routes'),
    canActivate: [
      isUnderMaintenanceGuard,
      preventNavigationByUnauthenticatedUser,
    ],
  },

  {
    path: DASH_ROUTES.unavailable,
    canActivate: [isUnderMaintenanceGuard],
    loadComponent: () =>
      import('./features/maintenance/maintenance-message.component').then(
        m => m.MaintenanceMessageComponent
      ),
  },

  {
    path: '**',
    pathMatch: 'full',
    redirectTo: DASH_ROUTES.authentication,
  },
];
