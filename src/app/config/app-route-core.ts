import { InjectionToken } from '@angular/core';

export const DASH_ROUTES = {
  legacyDashboard: 'merchant-dashboard',
  legacyProfile: 'merchant-profile',

  rootApp: 'merchant',

  dashboard: 'dashboard',
  report: 'payment-report',
  legacyReport: 'reports',
  account: 'account',
  refunds: 'refunds',
  clarifications: 'clarifications',
  help: 'help',

  loansDailyStatistics: 'statistics',
  weekdaysStats: 'weekdays',
  hoursStats: 'hours',
  dayHoursStats: 'day-hours',

  profileRoot: 'profile',
  profileMyCompany: 'my-company',
  profilePaymentDetails: 'payment',
  profileChangePassword: 'change-password',
  profileSdk: 'sdk',
  profileContacts: 'contacts',

  balance: 'balance',
  integration: 'integration',
  balanceDetails: 'balance-details',

  unavailable: 'unavailable',

  // keep the authorization name because the loadBalancer is redirecting
  // to this route from the old app implementation (registration-flow-ui)
  // change the name can cause issues about redirecting to the correct route
  authentication: 'authorization',
  login: 'login',
  forgotPass: 'forgot-password',
  forgotPassLink: 'forgot-password-link',
  enterNewPassword: 'enter-new-password',
} as const;

export type AppRoutes = typeof DASH_ROUTES;
export type AppRoutesKey = keyof typeof DASH_ROUTES;
export type AppRoutesPath = (typeof DASH_ROUTES)[AppRoutesKey];

export const ROUTES_TOKEN = new InjectionToken<AppRoutes>('DASH_ROUTES_TOKEN');
