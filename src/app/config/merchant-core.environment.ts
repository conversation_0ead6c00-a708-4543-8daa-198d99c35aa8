import {
  EnvironmentProviders,
  InjectionToken,
  makeEnvironmentProviders,
} from '@angular/core';

export type MerchantCoreEnvironment = {
  isProduction: boolean;
  apiBaseUrl: string;
  exposedPublicApiUrl: string;
  apiMerchantPath: string;
  apiPosUiBaseUrl: string;
  apiMerchantAccessBaseUrl: string;
  apiMicroserviceBaseUrl: string;
  redirectionsLandingPage: string;
  redirectionsCustomerLoginPage: string;
  redirectionsCustomerRegisterPage: string;
  redirectionsMerchantLoginPage: string;
  redirectionsMerchantRegisterPage: string;
  redirectionsPosUiLoginPage: string;
  redirectionsPosUiRegisterPage: string;
  i18nUrl: string;
  featureFlagsApiKey: string;
  featureFlagsEnv: string;
  gtmId: string;
  webChatApiKey: string;
  webChatBrandId: string;
  observabilityAppId: string;
  observabilityClientToken: string;
  observabilityEnv: string;
  observabilityService: string;
  promoApiUrl: string;
};

const isProduction = import.meta.env.NG_APP_PRODUCTION;
const apiBaseUrl = import.meta.env.NG_APP_API_URL;
const exposedPublicApiUrl = import.meta.env.NG_APP_EXPOSED_PUBLIC_API_URL;
const apiMerchantPath = import.meta.env.NG_APP_MERCHANT_PATH;
const apiPosUiBaseUrl = import.meta.env.NG_APP_POS_API_URL;
const apiMerchantAccessBaseUrl = import.meta.env
  .NG_APP_MERCHANT_ACCESS_BASE_URL;
const apiMicroserviceBaseUrl = import.meta.env.NG_APP_API_MICROSERVICE_URL;
const redirectionsLandingPage = import.meta.env.NG_APP_LANDING_URL;
const redirectionsCustomerLoginPage = import.meta.env.NG_APP_CUSTOMER_LOGIN_URL;
const redirectionsCustomerRegisterPage = import.meta.env
  .NG_APP_CUSTOMER_REGISTER_URL;
const redirectionsMerchantLoginPage = import.meta.env.NG_APP_MERCHANT_LOGIN_URL;
const redirectionsMerchantRegisterPage = import.meta.env
  .NG_APP_MERCHANT_REGISTER_URL;
const redirectionsPosUiLoginPage = import.meta.env.NG_APP_POSUI_URL;
const redirectionsPosUiRegisterPage = import.meta.env.NG_APP_POSUI_REGISTER_URL;
const i18nUrl = import.meta.env.NG_APP_I18N_URL;
const featureFlagsApiKey = import.meta.env.NG_APP_FLAGS_AUTHORIZATION_KEY;
const featureFlagsEnv = import.meta.env.NG_APP_FLAGS_ENV;
const gtmId = import.meta.env.NG_APP_GTM_ID;
const webChatApiKey = import.meta.env.NG_APP_WEBCHAT_API_KEY;
const webChatBrandId = import.meta.env.NG_APP_WEBCHAT_BRAND_ID;
const observabilityAppId = import.meta.env.NG_APP_DATADOG_APPLICATION_ID;
const observabilityClientToken = import.meta.env.NG_APP_DATADOG_CLIENT_TOKEN;
const observabilityEnv = import.meta.env.NG_APP_DATADOG_ENV;
const observabilityService = import.meta.env.NG_APP_DATADOG_SERVICE;
const promoApiUrl = import.meta.env.NG_APP_PROMO_API_URL;

export const merchantCoreEnvironment: MerchantCoreEnvironment = {
  isProduction,
  apiBaseUrl,
  exposedPublicApiUrl,
  apiMerchantPath,
  apiPosUiBaseUrl,
  apiMerchantAccessBaseUrl,
  apiMicroserviceBaseUrl,
  redirectionsLandingPage,
  redirectionsCustomerLoginPage,
  redirectionsCustomerRegisterPage,
  redirectionsMerchantLoginPage,
  redirectionsMerchantRegisterPage,
  redirectionsPosUiLoginPage,
  redirectionsPosUiRegisterPage,
  i18nUrl,
  featureFlagsApiKey,
  featureFlagsEnv,
  gtmId,
  webChatApiKey,
  webChatBrandId,
  observabilityAppId,
  observabilityClientToken,
  observabilityEnv,
  observabilityService,
  promoApiUrl,
};

export const MERCHANT_CORE_ENVIRONMENT =
  new InjectionToken<MerchantCoreEnvironment>('MERCHANT_CORE_ENVIRONMENT');

export function provideCoreEnvironment(): EnvironmentProviders {
  return makeEnvironmentProviders([
    { provide: MERCHANT_CORE_ENVIRONMENT, useValue: merchantCoreEnvironment },
  ]);
}
