import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';

export const BUSINESS_AREA = {
  Finance: 1,
  Marketing: 2,
  Support: 3,
  IT: 4,
  Commercial: 5,
} as const;

export type BusinessArea = keyof typeof BUSINESS_AREA;
export type BusinessAreaId = (typeof BUSINESS_AREA)[BusinessArea];

export interface RetrievedContactResponse {
  id: number;
  email: string;
  name: string;
  role: string;
  businessArea: string;
  phone: string | null;
  merchantId?: number;
  businessAreaId: number;
  createdAt?: string;
  updateAt?: string;
}

export interface NewContactDto {
  email: string;
  name: string;
  role: string;
  phone: string;
  businessAreaId: BusinessAreaId;
}

export interface CreateContactDto extends Partial<NewContactDto> {
  id: number;
}

export interface UpdateContactDto extends Partial<NewContactDto> {
  id: number;
}

export interface ContactUI {
  id?: number;
  phone: string;
  email: string;
  name: string;
  role: string;
  businessArea: BusinessArea;
  merchantId?: number;
  updateAt?: string;
  createdAt?: string;
}

export const serializeRetrievedContactResponse = (
  args: RetrievedContactResponse
): ContactUI => {
  const email = args.email;
  const businessArea = args.businessArea as BusinessArea;

  return {
    id: args.id,
    email: email,
    name: args.name,
    phone: args.phone ?? '',
    role: args.role,
    businessArea: businessArea as BusinessArea,
    merchantId: args.merchantId,
    updateAt: args.updateAt,
  };
};

export const fromRepositoryToUI = (args: NewContactDto): ContactUI => {
  const businessArea = Object.entries(BUSINESS_AREA).find(
    ([, val]) => val === args.businessAreaId
  );

  return {
    email: args.email,
    name: args.name,
    role: args.role,
    phone: args.phone,
    businessArea: businessArea
      ? (businessArea[0] as BusinessArea)
      : ('Finance' as BusinessArea),
  };
};

export const fromUIToRepository = (args: ContactUI): NewContactDto => {
  const errors = ['email', 'name', 'role', 'businessArea', 'phone']
    .map(key => {
      const result = Guard.againstNullOrUndefined(args, key as keyof ContactUI);
      return result.succeeded ? null : result.message;
    })
    .filter(Boolean);

  if (errors.length > 0) {
    throw new RuntimeMerchantError(
      'Detectamos errores en la creacion de un nuevo contacto:: ' +
        errors.join(', '),
      'Contact::fromUIToRepository::emptyFields'
    );
  }

  const businessAreaId = BUSINESS_AREA[args.businessArea];

  if (!businessAreaId) {
    throw new RuntimeMerchantError(
      'El área de negocio no es válida',
      'Contact::fromUIToRepository::emptyBusinessAreaId'
    );
  }

  const result = {
    email: args.email,
    name: args.name,
    role: args.role,
    phone: args.phone,
    businessAreaId: BUSINESS_AREA[args.businessArea],
  };

  return result;
};

export const fromUIToUpdateRepository = (args: ContactUI): UpdateContactDto => {
  let email = '';
  if (args.email) {
    email = args.email;
  }

  // Validate name field
  const name = args.name?.trim() || '';

  const businessAreaId = BUSINESS_AREA[args.businessArea];

  if (!businessAreaId) {
    throw new RuntimeMerchantError(
      'El área de negocio no es válida',
      'Contact::fromUIToUpdateRepository::emptyBusinessAreaId'
    );
  }

  Guard.againstInvalidNumbers(
    args.id,
    'Contact::fromUIToUpdateRepository',
    'El id del contacto debe ser un número entero válido.'
  );

  const final: UpdateContactDto = {
    id: args.id!,
    email,
    name,
    role: args.role,
    businessAreaId: BUSINESS_AREA[args.businessArea],
  };

  if (args.phone && args.phone.length === 10) {
    final.phone = args.phone;
  }

  return final;
};
