import { Merchant } from '../../domain/entities/merchant';
import { IMerchantWithoutBillingDto } from '../dtos/merchant-without-billing.dto';

export class MerchantMapper {
  public static fromDtoToDomain(
    response: IMerchantWithoutBillingDto
  ): Omit<Merchant, 'billingInfo'> {
    return {
      name: response.name,
      address: response.address,
      website: response.website,
      email: response.email,
      legalRepresentative: {
        role: response.representativeRole,
        name: response.representativeName,
        phoneNumber: response.representativePhoneNumber,
      },
    };
  }

  public static fromDomainToDto(
    merchant: Omit<Merchant, 'billingInfo'>
  ): IMerchantWithoutBillingDto {
    return {
      name: merchant.name,
      address: merchant.address,
      website: merchant.website,
      email: merchant.email,
      representativeRole: merchant.legalRepresentative.role,
      representativeName: merchant.legalRepresentative.name,
      representativePhoneNumber: merchant.legalRepresentative.phoneNumber,
    };
  }
}
