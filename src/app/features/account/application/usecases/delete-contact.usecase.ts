import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, take, tap, throwError } from 'rxjs';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';

@Injectable()
export class DeleteContactUseCase
  implements BaseUsecase<number, Observable<void>>
{
  readonly #repository = inject(MerchantContactsRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(contactId: number): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      if (!contactId || isNaN(contactId) || contactId <= 0) {
        throw new Error('El identificador del contacto es inválido');
      }

      return this.#repository.deleteOne(contactId).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Contacto eliminado exitosamente',
          });
        }),

        catchError(err => this.#errorHandler.handle<never>(err)),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
