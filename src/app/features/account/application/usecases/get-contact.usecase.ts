import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { UTCDate } from '@date-fns/utc';
import { catchError, finalize, map, Observable, of } from 'rxjs';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';
import {
  ContactUI,
  serializeRetrievedContactResponse,
} from '../dtos/contact.dto';

@Injectable()
export class GetContactInfoUseCase
  implements BaseUsecase<void, Observable<ContactUI[]>>
{
  readonly #repository = inject(MerchantContactsRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(): Observable<ContactUI[]> {
    const idLoader = this.#loader.show();

    try {
      return this.#repository.getInfo().pipe(
        map(res => res.map(serializeRetrievedContactResponse)),

        map(contacts => {
          return contacts.map(contact => {
            const updateAt = contact.updateAt
              ? new UTCDate(contact.updateAt)
              : undefined;
            const updateAtIso = updateAt?.toISOString() ?? '';

            return {
              ...contact,
              updateAt: contact.updateAt ? updateAtIso : undefined,
            };
          });
        }),

        map(contacts => {
          const sorted = contacts.slice();

          if (sorted.every(contact => contact.id && contact.updateAt)) {
            sorted.sort((a, b) => {
              const dateA = a.updateAt!.toLocaleLowerCase();
              const dateB = b.updateAt!.toLocaleLowerCase();

              if (dateA > dateB) return -1;
              if (dateA < dateB) return 1;
              return 0;
            });
          }

          return sorted;
        }),

        catchError(err => {
          if (err instanceof HttpErrorResponse && err.status >= 500) {
            throw new RuntimeMerchantError(
              'Error en el servidor. Por favor, intente más tarde.',
              'GetContactInfoUseCase::serverError'
            );
          }

          if (err instanceof HttpErrorResponse && err.status === 404) {
            return of([]);
          }

          throw err;
        }),

        catchError(err => {
          return this.#errorHandler.handle(err, of([]));
        }),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, of([]));
    }
  }
}
