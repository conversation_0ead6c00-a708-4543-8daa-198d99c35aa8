import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import { MerchantBillingInfo } from '../../domain/entities/merchant-billing-info';
import { MerchantBillingRepository } from '../../domain/repositories/merchant-billing.repository';

export const merchantBillingDefault: MerchantBillingInfo = {
  accountNumber: 'No disponible',
  bankName: 'No disponible',
  rfc: 'No disponible',
};

@Injectable({ providedIn: 'any' })
export class MerchantBillingInfoUseCase
  implements BaseUsecase<any, Observable<MerchantBillingInfo>>
{
  readonly #default = merchantBillingDefault;

  constructor(
    private readonly repository: MerchantBillingRepository<
      Observable<MerchantBillingInfo>
    >,
    private readonly loaderService: LoaderService,
    private readonly useCaseErrorHandler: UseCaseErrorHandler
  ) {}

  execute(): Observable<MerchantBillingInfo> {
    const idLoader = this.loaderService.show();

    return this.repository.getMerchantBilling().pipe(
      map(response => {
        return {
          accountNumber: response?.accountNumber || this.#default.accountNumber,
          bankName: response?.bankName || this.#default.bankName,
          rfc: response?.rfc || this.#default.rfc,
        };
      }),
      catchError(error =>
        this.useCaseErrorHandler.handle(error, of(this.#default))
      ),
      finalize(() => this.loaderService.hide(idLoader)),
      take(1)
    );
  }
}
