import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { EMPTY, Observable, catchError, finalize, take, tap } from 'rxjs';
import { MerchantChangePasswordRepository } from '../../domain/repositories/merchant-change-password.repository';
import { IChangePasswordDto } from '../dtos/merchant-change-password.dto';

export const changePasswordV1InvalidConfirmationErrorMsg =
  'La nueva contraseña no coincide con la confirmación de la contraseña.';

export const changePasswordSameOldAndNewPasswordErrorMsg =
  'La contraseña actual y la nueva contraseña no pueden ser iguales.';

export const changePasswordNullOldPasswordErrorMsg =
  'La contraseña actual no puede ser nula o indefinida.';

export const changePasswordNullNewPasswordErrorMsg =
  'La nueva contraseña no puede ser nula o indefinida.';

@Injectable({ providedIn: 'any' })
export class MerchantChangePasswordUseCase
  implements BaseUsecase<IChangePasswordDto, Observable<void>>
{
  constructor(
    private readonly repository: MerchantChangePasswordRepository<
      IChangePasswordDto,
      Observable<void>
    >,
    private readonly loader: LoaderService,
    private readonly notifier: NotifierService,
    private readonly usecaseErrorHandler: UseCaseErrorHandler
  ) {}

  execute(args: IChangePasswordDto): Observable<void> {
    const idLoader = this.loader.show();

    try {
      if (!Guard.againstNullOrUndefined(args, 'oldPassword').succeeded) {
        throw new RuntimeMerchantError(
          changePasswordNullOldPasswordErrorMsg,
          'ChangePasswordUsecase::nullOldPassword'
        );
      }

      if (
        !Guard.againstNullOrUndefined(args, 'newPassword').succeeded ||
        !Guard.againstNullOrUndefined(args, 'confirmNewPassword').succeeded
      ) {
        throw new RuntimeMerchantError(
          changePasswordNullNewPasswordErrorMsg,
          'ChangePasswordUsecase::nullNewPassword'
        );
      }

      const newPasswordConfirmed = args.newPassword === args.confirmNewPassword;

      if (!newPasswordConfirmed) {
        throw new RuntimeMerchantError(
          changePasswordV1InvalidConfirmationErrorMsg,
          'ChangePasswordUsecase::invalidConfirmation'
        );
      }

      if (args.newPassword === args.oldPassword) {
        throw new RuntimeMerchantError(
          changePasswordSameOldAndNewPasswordErrorMsg,
          'ChangePasswordUsecase::sameOldAndNewPassword'
        );
      }

      return this.repository.changePassword(args).pipe(
        tap(() => {
          this.notifier.success({
            title: 'Contraseña cambiada satisfactoriamente',
          });
        }),
        catchError(error => this.usecaseErrorHandler.handle(error, EMPTY)),
        take(1),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.usecaseErrorHandler.handle(error, EMPTY);
    }
  }
}
