import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import { MerchantWithoutBillingRepository } from '../../domain/repositories/merchant-without-billing.repository';
import { IMerchantWithoutBillingDto } from '../dtos/merchant-without-billing.dto';

export const merchantWoBillingDefault: IMerchantWithoutBillingDto = {
  name: 'No disponible',
  address: 'No disponible',
  website: 'No disponible',
  representativeRole: 'No disponible',
  representativeName: 'No disponible',
  representativePhoneNumber: 'No disponible',
  email: 'No disponible',
};

@Injectable({ providedIn: 'any' })
export class MerchantWithoutBillingInfoUseCase
  implements BaseUsecase<any, Observable<IMerchantWithoutBillingDto>>
{
  readonly #default = merchantWoBillingDefault;

  constructor(
    private readonly repository: MerchantWithoutBillingRepository<
      Observable<IMerchantWithoutBillingDto>
    >,
    private readonly loaderService: LoaderService,
    private readonly usecaseErrorHandler: UseCaseErrorHandler
  ) {}

  execute(): Observable<IMerchantWithoutBillingDto> {
    const idLoader = this.loaderService.show();

    return this.repository.getMerchantWithoutBilling().pipe(
      map(response => {
        return {
          name: response?.name || this.#default.name,
          address: response?.address || this.#default.address,
          website: response?.website || this.#default.website,
          representativeRole:
            response?.representativeRole || this.#default.representativeRole,
          representativeName:
            response?.representativeName || this.#default.representativeName,
          representativePhoneNumber:
            response?.representativePhoneNumber ||
            this.#default.representativePhoneNumber,
          email: response?.email || this.#default.email,
        };
      }),
      catchError(error =>
        this.usecaseErrorHandler.handle(error, of(this.#default))
      ),
      finalize(() => {
        this.loaderService.hide(idLoader);
      }),
      take(1)
    );
  }
}
