import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, tap, throwError } from 'rxjs';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';
import {
  ContactUI,
  RetrievedContactResponse,
  fromUIToRepository,
} from '../dtos/contact.dto';

@Injectable()
export class CreateOneContactUseCase
  implements BaseUsecase<ContactUI, Observable<RetrievedContactResponse>>
{
  readonly #repository = inject(MerchantContactsRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: ContactUI): Observable<RetrievedContactResponse> {
    const idLoader = this.#loader.show();

    try {
      const request = fromUIToRepository(args);

      return this.#repository.createOne(request).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Contacto creado exitosamente',
          });
        }),

        catchError(err => this.#errorHandler.handle<never>(err)),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
