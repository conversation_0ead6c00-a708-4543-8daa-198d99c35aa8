import { Injectable, inject } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, take, tap, throwError } from 'rxjs';
import {
  ContactUI,
  RetrievedContactResponse,
  fromUIToUpdateRepository,
} from '../dtos/contact.dto';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';

@Injectable()
export class UpdateOneContactUseCase
  implements BaseUsecase<ContactUI, Observable<RetrievedContactResponse>>
{
  readonly #repository = inject(MerchantContactsRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: ContactUI): Observable<RetrievedContactResponse> {
    const idLoader = this.#loader.show();

    try {
      if (!args.name || args.name.trim() === '') {
        throw new Error('El nombre es requerido');
      }

      const request = fromUIToUpdateRepository(args);

      return this.#repository.updateOne(request).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Contacto actualizado exitosamente',
          });
        }),

        catchError(err => this.#errorHandler.handle<never>(err)),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
