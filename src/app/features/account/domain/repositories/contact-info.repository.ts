import { Observable } from 'rxjs';
import {
  RetrievedContactResponse,
  NewContactDto,
  UpdateContactDto,
} from '../../application/dtos/contact.dto';

export abstract class MerchantContactsRepository {
  abstract getInfo(): Observable<RetrievedContactResponse[]>;
  abstract createOne(
    request: NewContactDto
  ): Observable<RetrievedContactResponse>;
  abstract updateOne(
    request: UpdateContactDto
  ): Observable<RetrievedContactResponse>;
  abstract deleteOne(id: number): Observable<void>;
  abstract getCatalogs(): Observable<{ id: number; name: string }[]>;
}
