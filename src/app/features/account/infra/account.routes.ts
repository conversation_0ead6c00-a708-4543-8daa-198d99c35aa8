import { Route } from '@angular/router';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { provideCompanyRepositories } from './config/providers';

export default [
  {
    path: '',
    loadComponent: () =>
      import('./layout/account.component').then(stl => stl.AccountComponent),
    providers: [provideCompanyRepositories()],
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: DASH_ROUTES.profileMyCompany,
      },
      {
        path: DASH_ROUTES.profileMyCompany,
        loadComponent: () =>
          import('./pages/profile.component').then(stl => stl.ProfileComponent),
      },
      {
        path: DASH_ROUTES.profilePaymentDetails,
        loadComponent: () =>
          import('./pages/banking-info.component').then(
            stl => stl.BankingInfoComponent
          ),
      },
      {
        path: DASH_ROUTES.profileChangePassword,
        loadComponent: () =>
          import('./pages/change-password.component').then(
            stl => stl.ChangePasswordComponent
          ),
      },
      {
        path: DASH_ROUTES.profileContacts,
        loadComponent: () =>
          import('./pages/contacts/contacts.component').then(
            stl => stl.ContactsComponent
          ),
      },
    ],
  },
] satisfies Route[];
