import { Component, inject } from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { DialogRef } from '@ngneat/dialog';

@Component({
  selector: 'app-contact-delete-confirm',
  templateUrl: './contact-delete-confirm.component.html',
  imports: [AplazoCardComponent, AplazoButtonComponent],
})
export class ContactDeleteConfirmComponent {
  readonly #dialogRef = inject(
    DialogRef<{ contactName?: string; contactId: number }, boolean>
  );
  readonly #notifier = inject(NotifierService);

  readonly data = this.#dialogRef.data;

  confirm(): void {
    this.#dialogRef.close(true);
  }

  cancel(): void {
    this.#notifier.info({ title: 'Eliminación cancelada' });
    this.#dialogRef.close(false);
  }
}
