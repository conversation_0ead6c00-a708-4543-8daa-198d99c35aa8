<div class="h-full">
  <aplz-ui-card size="sm" class="h-full min-h-fit overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h1 class="font-semibold text-lg text-dark-secondary">
        {{ isEditMode ? 'Editar Contacto' : 'Agregar Nuevo Contacto' }}
      </h1>
    </div>

    <form
      [formGroup]="form"
      class="flex flex-wrap gap-4 pt-4"
      (ngSubmit)="submit()">
      @if (duplicateRoleError) {
        <div
          class="w-full p-3 rounded-md mb-2 bg-alert-critical-background text-special-danger border-l-4 border-alert-critical-stroke">
          {{ duplicateRoleError }}
        </div>
      }

      <div class="w-full">
        <p class="text-sm font-light text-dark-tertiary mb-2">
          Solo aparecerán roles que aun no han sido asignados
          {{ isEditMode ? ' o el rol actual del contacto' : '' }}
        </p>
        @if (filteredRoleOptions.length === 0) {
          <p
            class="text-sm mb-2 bg-alert-warning-background text-dark-secondary p-2 rounded border-l-4 border-alert-warning-stroke">
            No hay roles disponibles para asignar.
          </p>
        } @else if (isEditMode && filteredRoleOptions.length === 1) {
          <p
            class="text-sm mb-2 bg-alert-warning-background text-dark-secondary p-2 rounded border-l-4 border-alert-warning-stroke">
            Solo está disponible el rol actual. Todos los demás roles ya están
            asignados.
          </p>
        }
      </div>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label>Rol</aplz-ui-form-label>

        <select
          class="w-full p-3 text-dark-secondary"
          aplzFormSelect
          formControlName="role"
          title="Seleccionar Rol">
          <option value="" selected>Seleccione un rol</option>
          @for (option of filteredRoleOptions; track option.value) {
            <option [value]="option.value">
              {{ option.label }}
              {{ option.value === data?.role ? ' (Actual)' : '' }}
            </option>
          }
        </select>
        <ng-container aplzFormError>
          @if (role.touched && role.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label>Nombre</aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="name"
          placeholder="Nombre completo"
          aplazoTrimSpaces />
        <ng-container aplzFormError>
          @if (name.touched && name.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label>Correo Electrónico</aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="email"
          placeholder="<EMAIL>"
          aplazoTrimSpaces
          inputmode="email" />
        <ng-container aplzFormError>
          @if (email.touched && email.hasError('required')) {
            <p>Este campo es requerido</p>
          }
          @if (email.touched && email.hasError('email')) {
            <p>Email inválido. Ej. correo&#64;ejemplo.com</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label>Teléfono</aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="phone"
          placeholder="00 0000 0000"
          aplazoOnlyNumbers
          [aplazoTruncateLength]="10"
          aplazoTrimSpaces
          inputmode="tel" />
        <ng-container aplzFormError>
          @if (phone.touched && phone.hasError('required')) {
            <p>Este campo es requerido</p>
          }
          @if (
            phone.touched &&
            (phone.hasError('minlength') || phone.hasError('maxlength'))
          ) {
            <p>Asegúrese de que el teléfono sea de 10 dígitos</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <div class="flex items-center justify-end gap-4 flex-wrap w-full mt-6">
        <button
          aplzButton
          type="button"
          size="md"
          aplzAppearance="stroked"
          aplzColor="light"
          (click)="close()">
          Cancelar
        </button>
        <button
          aplzButton
          type="submit"
          size="md"
          aplzAppearance="solid"
          aplzColor="dark">
          {{ isEditMode ? 'Actualizar' : 'Crear' }}
        </button>
      </div>
    </form>
  </aplz-ui-card>
</div>
