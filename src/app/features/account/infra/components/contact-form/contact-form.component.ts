import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { Subject } from 'rxjs';
import {
  BusinessArea,
  ContactUI,
  BUSINESS_AREA,
} from '../../../application/dtos/contact.dto';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-contact-form',
  templateUrl: './contact-form.component.html',
  imports: [
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoTrimSpacesDirective,
    AplazoTruncateLengthDirective,
    OnlyNumbersDirective,
    ReactiveFormsModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContactFormComponent implements OnInit, OnDestroy {
  roleOptions = [
    { value: 'Mkt', label: 'Marketing' },
    { value: 'Support', label: 'Soporte' },
    { value: 'IT', label: 'TI (Tecnología de la Información)' },
    { value: 'Commercial', label: 'Comercial' },
    { value: 'Finance', label: 'Finanzas' },
  ];

  filteredRoleOptions: { value: string; label: string }[] = [];
  duplicateRoleError: string | null = null;

  readonly #dialogRef: DialogRef<
    | {
        id?: number;
        email?: string;
        name?: string;
        phone?: string;
        role?: string;
        businessArea?: BusinessArea;
        existingRoles?: string[];
      }
    | undefined,
    Partial<ContactUI> & { hasConfirmation: boolean }
  > = inject(DialogRef);

  readonly #destroy = new Subject<void>();
  readonly #merchantStore = inject(MerchantStoreService);

  readonly data = this.#dialogRef.data;
  readonly isEditMode = !!this.data?.id;

  readonly roles = Object.keys(BUSINESS_AREA).filter(Boolean);

  readonly id = new FormControl<number | null>({
    value: this.data?.id ?? null,
    disabled: true,
  });

  readonly role = new FormControl<string>(this.data?.role ?? '', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly businessArea = new FormControl<BusinessArea>(
    this.data?.businessArea ?? 'Support',
    {
      validators: [Validators.required],
    }
  );

  readonly email = new FormControl<string>(this.data?.email ?? '', {
    nonNullable: true,
    validators: [Validators.required, Validators.email],
  });

  readonly name = new FormControl<string>(this.data?.name ?? '', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly phone = new FormControl<string>(this.data?.phone ?? '', {
    validators: [Validators.minLength(10), Validators.maxLength(10)],
  });

  readonly form = new FormGroup({
    id: this.id,
    role: this.role,
    email: this.email,
    name: this.name,
    phone: this.phone,
    businessArea: this.businessArea,
  });

  ngOnInit(): void {
    this.filterAvailableRoles();

    this.role.valueChanges.subscribe(() => {
      this.duplicateRoleError = null;
    });
  }

  filterAvailableRoles(): void {
    const existingRoles =
      this.data?.existingRoles || this.#merchantStore.getExistingRoles();

    if (this.isEditMode && this.data?.role) {
      this.filteredRoleOptions = this.roleOptions.filter(
        option =>
          option.value === this.data?.role ||
          !existingRoles.includes(option.value)
      );

      if (this.filteredRoleOptions.length === 1) {
        this.duplicateRoleError =
          'No hay otros roles disponibles para asignar. Todos los roles ya están asignados a otros contactos.';
      }
    } else {
      this.filteredRoleOptions = this.roleOptions.filter(
        option => !existingRoles.includes(option.value)
      );
    }
  }

  close(): void {
    this.#dialogRef.close({ hasConfirmation: false });
  }

  submit(): void {
    this.form.markAllAsTouched();

    if (!this.form.valid) {
      return;
    }

    if (!this.isEditMode) {
      const existingRoles =
        this.data?.existingRoles || this.#merchantStore.getExistingRoles();

      if (existingRoles.includes(this.role.value)) {
        this.duplicateRoleError =
          'Ya existe un contacto con este rol. Para actualizar la información, edite el contacto existente.';
        return;
      }
    }

    this.id.enable();

    const dialogResult = {
      hasConfirmation: true,
      role: this.role.value,
      email: this.email.value,
      name: this.name.value,
      phone: this.phone.value ?? '',
      businessArea: this.businessArea.value,
      id: this.id.value ?? undefined,
    };

    this.#dialogRef.close(dialogResult);
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }
}
