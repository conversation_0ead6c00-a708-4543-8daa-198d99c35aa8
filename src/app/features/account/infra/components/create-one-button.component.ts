import { Component, inject, input, output } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconPlus } from '@aplazo/ui-icons';

@Component({
  selector: 'app-create-one-button',
  template: `
    <div
      class="flex flex-col gap-2 items-center justify-center w-full min-h-16 py-4">
      <p class="max-w-prose px-4 font-light text-dark-secondary">
        {{ title() }}
      </p>
      <div>
        <button
          [rounded]="true"
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          (click)="createOne.emit()">
          <aplz-ui-icon name="plus" size="sm"></aplz-ui-icon>
          <span class="ml-2"> {{ buttonLabel() }} </span>
        </button>
      </div>
    </div>
  `,
  imports: [AplazoButtonComponent, AplazoIconComponent],
})
export class CreateOneButtonComponent {
  readonly #iconRegistry = inject(AplazoIconRegistryService);

  readonly title = input.required<string>();

  readonly buttonLabel = input<string>('Agregar Nuevo Contacto');

  readonly createOne = output<void>();

  constructor() {
    this.#iconRegistry.registerIcons([iconPlus]);
  }
}
