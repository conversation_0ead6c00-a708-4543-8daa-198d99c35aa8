import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { MerchantBillingRepository } from '../../domain/repositories/merchant-billing.repository';
import { MerchantChangePasswordRepository } from '../../domain/repositories/merchant-change-password.repository';
import { MerchantWithoutBillingRepository } from '../../domain/repositories/merchant-without-billing.repository';
import { MerchantBillingInfoRepositoryImpl } from '../repositories/merchant-billing-info-impl.repository';
import { MerchantChangePasswordRepositoryImpl } from '../repositories/merchant-change-password-impl.repository';
import { MerchantWithoutBillingRepositoryImpl } from '../repositories/merchant-without-billing-repository.impl';
import { UpdateContactWithDialogFormService } from '../services/edit-contact-with-dialog-form.service';
import { DeleteContactWithDialogService } from '../services/delete-contact-with-dialog.service';
import { MerchantStoreService } from '../services/merchant-store.service';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';
import { MerchantContactsRepositoryImpl } from '../repositories/merchant-contacts-impl.repository';
import { UpdateOneContactUseCase } from '../../application/usecases/update-contact.usecase';
import { CreateContactWithDialogFormService } from '../services/create-contact-with-dialog.service';
import { GetContactInfoUseCase } from '../../application/usecases/get-contact.usecase';
import { CreateOneContactUseCase } from '../../application/usecases/new-contact.usecase';
import { DeleteContactUseCase } from '../../application/usecases/delete-contact.usecase';

export function provideCompanyRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: MerchantBillingRepository,
      useClass: MerchantBillingInfoRepositoryImpl,
    },
    {
      provide: MerchantChangePasswordRepository,
      useClass: MerchantChangePasswordRepositoryImpl,
    },
    {
      provide: MerchantWithoutBillingRepository,
      useClass: MerchantWithoutBillingRepositoryImpl,
    },
    UpdateContactWithDialogFormService,
    CreateContactWithDialogFormService,
    DeleteContactWithDialogService,
    GetContactInfoUseCase,
    MerchantStoreService,
    UpdateOneContactUseCase,
    CreateOneContactUseCase,
    DeleteContactUseCase,
    {
      provide: MerchantContactsRepository,
      useClass: MerchantContactsRepositoryImpl,
    },
  ]);
}
