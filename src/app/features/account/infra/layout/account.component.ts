import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { map, take } from 'rxjs';
import { DASH_ROUTES } from '../../../../config/app-route-core';
import { UserStoreService } from '../../../user/src/application/services/user-store.service';

export const accountTablabels = {
  'DATOS DEL COMERCIO': DASH_ROUTES.profileMyCompany,
  'DATOS BANCARIOS': DASH_ROUTES.profilePaymentDetails,
  'CAMBIAR CONTRASEÑA': DASH_ROUTES.profileChangePassword,
  'DATOS DE CONTACTOS': DASH_ROUTES.profileContacts,
} as const;

@Component({
  selector: 'app-account',
  template: `
    <div class="px-4 md:px-8 py-4 md:py-8">
      <aplz-ui-card>
        <div class="pb-4">
          <aplz-ui-tab-group (tabSelectionChange)="tabSelectionChange($event)">
            @for (label of labels$ | async; track label) {
              <aplz-ui-tab [label]="label"></aplz-ui-tab>
            }
          </aplz-ui-tab-group>
        </div>

        <router-outlet></router-outlet>
      </aplz-ui-card>
    </div>
  `,
  imports: [AplazoTabsComponents, AplazoCardComponent, AsyncPipe, RouterOutlet],
})
export class AccountComponent {
  readonly #userStore = inject(UserStoreService);
  readonly #router$ = inject(Router);
  readonly #route$ = inject(ActivatedRoute);

  labels$ = this.#userStore.role$.pipe(
    map(role => {
      if (role === 'ROLE_MERCHANT') {
        // ROLE_MERCHANT no tiene acceso a DATOS DE CONTACTOS
        return Object.keys(accountTablabels).filter(
          label => label !== 'DATOS DE CONTACTOS'
        );
      } else if (
        role === 'ROLE_PANEL_ADMIN' ||
        role === 'ROLE_ADMIN_INC' ||
        role === 'ROLE_PANEL_MANAGER'
      ) {
        // Estos roles tienen acceso a todas las pestañas
        return Object.keys(accountTablabels);
      } else {
        // Otros roles no tienen acceso a CAMBIAR CONTRASEÑA
        return Object.keys(accountTablabels).filter(
          label => label !== 'CAMBIAR CONTRASEÑA'
        );
      }
    }),
    take(1)
  );

  tabSelectionChange(event: { index: number }) {
    this.labels$
      .pipe(
        map(labels => labels[event.index]),
        take(1)
      )
      .subscribe(ls => {
        this.#router$.navigate([accountTablabels[ls]], {
          relativeTo: this.#route$,
        });
      });
  }
}
