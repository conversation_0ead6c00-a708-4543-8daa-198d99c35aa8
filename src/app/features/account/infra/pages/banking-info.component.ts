import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { I18NService } from '@aplazo/i18n';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconInfo } from '@aplazo/ui-icons';
import { take } from 'rxjs';
import { MerchantBillingInfoUseCase } from '../../application/usecases/merchant-billing-info.usecase';

export class IMerchantBillingInfoInjectedTextUI {
  rfc: {
    sectionTitle: string;
    rfcLabel: string;
    rfcValueDefault: string;
  };
  bankAccount: {
    sectionTitle: string;
    bankNameLabel: string;
    bankNameValueDefault: string;
    clabeLabel: string;
    clabeValueDefault: string;
  };
  message: {
    content: string;
    supportEmail: string;
    whatsappURL: string;
  };
}

@Component({
  selector: 'app-banking-info',
  template: `
    @if (textUI()) {
      <div class="mb-12">
        <h3 class="font-semibold mb-4">
          {{ textUI().rfc.sectionTitle }}
        </h3>

        <div class="flex items-center mb-2">
          <span class="flex-grow-0 flex-shrink-0 inline-block min-w-36 text-md">
            {{ textUI().rfc.rfcLabel }}
          </span>

          <p
            class="w-56 py-2 px-4 border rounded-md border-dark-background text-dark-tertiary">
            {{ billing()?.rfc }}
          </p>
        </div>
      </div>

      <div class="mb-12">
        <h3 class="font-semibold mb-4">
          {{ textUI().bankAccount.sectionTitle }}
        </h3>

        <div class="flex items-center mb-2">
          <span class="inline-block min-w-36 text-md">
            {{ textUI().bankAccount.bankNameLabel }}
          </span>

          <p
            class="w-56 py-2 px-4 border rounded-md border-dark-background text-dark-tertiary">
            {{ billing()?.bankName }}
          </p>
        </div>

        <div class="flex items-center mb-2">
          <span class="inline-block min-w-36 text-md">
            {{ textUI().bankAccount.clabeLabel }}
          </span>

          <p
            class="w-56 py-2 px-4 border rounded-md border-dark-background text-dark-tertiary">
            {{ billing()?.accountNumber }}
          </p>
        </div>
      </div>

      <h4
        class="flex items-center px-4 py-2 bg-dark-background rounded-md text-balance font-light">
        <aplz-ui-icon name="info"></aplz-ui-icon>

        <span class="ml-4">
          {{ textUI().message.content }}
          <a class="font-normal" [href]="textUI().message.whatsappURL">
            Whatsapp
          </a>
        </span>
      </h4>
    }
  `,
  imports: [AplazoIconComponent],
})
export class BankingInfoComponent {
  readonly #i18 = inject(I18NService);
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #usecase = inject(MerchantBillingInfoUseCase);
  readonly #scope = 'banking-info';

  readonly billing = toSignal(this.#usecase.execute().pipe(take(1)));

  readonly textUI = toSignal(
    this.#i18.getTranslateObjectByKey<IMerchantBillingInfoInjectedTextUI>({
      key: 'info',
      scope: this.#scope,
    })
  );

  constructor() {
    this.#iconRegistry.registerIcons([iconInfo]);
  }
}
