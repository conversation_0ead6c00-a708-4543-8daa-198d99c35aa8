import { Async<PERSON><PERSON><PERSON>, NgClass } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoPasswordControlComponent } from '@aplazo/shared-ui/merchant';
import {
  Observable,
  Subject,
  combineLatest,
  combineLatestWith,
  map,
  startWith,
  take,
  takeUntil,
} from 'rxjs';
import { compareTwoFormControlsByName } from '../../../auth/directives/compare-input-control.directive';
import { MerchantChangePasswordUseCase } from '../../application/usecases/merchant-change-password.usecase';

export interface IProfileChangePasswordInjectedTextUI {
  errors: {
    required: string;
    maxLength: string;
    pattern: string;
    comparePassword: string;
  };
  form: {
    oldPassword: {
      label: string;
      placeholder?: string;
    };
    newPassword: {
      label: string;
      placeholder?: string;
    };
    confirmPassword: {
      label: string;
      placeholder?: string;
      minLenghtDescription: string;
      digitDescription: string;
      uppercaseDescription: string;
    };
    submitButton: string;
  };
}

@Component({
  selector: 'app-change-password',
  template: `
    @if (changePasswordInjectedText$ | async; as textUI) {
      <form
        [formGroup]="form"
        (ngSubmit)="changePassword()"
        class="mt-8 max-w-xs">
        <div class="mb-12">
          <aplz-mui-password-control
            formControlName="oldPassword"
            [textUI]="{
              label: textUI.form.oldPassword.label,
              placeholder: 'Escribe tu contraseña',
              requiredError: textUI.errors.required,
              minLengthError: textUI.errors.maxLength,
              patternError: textUI.errors.pattern
            }">
          </aplz-mui-password-control>
        </div>
        <div class="mb-12">
          <aplz-mui-password-control
            formControlName="newPassword"
            [textUI]="{
              label: textUI.form.newPassword.label,
              placeholder: 'Escribe tu contraseña',
              requiredError: textUI.errors.required,
              minLengthError: textUI.errors.maxLength,
              patternError: textUI.errors.pattern
            }">
          </aplz-mui-password-control>
        </div>
        <div class="mb-12">
          <aplz-mui-password-control
            formControlName="confirmPassword"
            [textUI]="{
              label: textUI.form.confirmPassword.label,
              placeholder: 'Escribe tu contraseña',
              requiredError: textUI.errors.required,
              minLengthError: textUI.errors.maxLength,
              patternError: textUI.errors.pattern,
              comparedWithOtherError: textUI.errors.comparePassword
            }">
          </aplz-mui-password-control>
        </div>
        @if (errorDescriptor$ | async; as descriptor) {
          <ul class="pb-8">
            <li
              class="relative pl-6 mb-2 before:absolute before:top-1/2 before:left-0 before:transform before:-translate-y-1/2 before:w-4 before:aspect-square before:rounded-full"
              [ngClass]="{
                'before:bg-aplazo-aplazo': descriptor.hasMinLength,
                'before:bg-dark-tertiary': !descriptor.hasMinLength
              }">
              {{ textUI.form.confirmPassword.minLenghtDescription }}
            </li>
            <li
              class="relative pl-6 mb-2 before:absolute before:top-1/2 before:left-0 before:transform before:-translate-y-1/2 before:w-4 before:aspect-square before:rounded-full"
              [ngClass]="{
                'before:bg-aplazo-aplazo': descriptor.hasDigit,
                'before:bg-dark-tertiary': !descriptor.hasDigit
              }">
              {{ textUI.form.confirmPassword.digitDescription }}
            </li>
            <li
              class="relative pl-6 mb-2 before:absolute before:top-1/2 before:left-0 before:transform before:-translate-y-1/2 before:w-4 before:aspect-square before:rounded-full"
              [ngClass]="{
                'before:bg-aplazo-aplazo': descriptor.hasUppercase,
                'before:bg-dark-tertiary': !descriptor.hasUppercase
              }">
              {{ textUI.form.confirmPassword.uppercaseDescription }}
            </li>
          </ul>
        }
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          [rounded]="true"
          [disabled]="hasError$ | async">
          {{ textUI.form.submitButton }}
        </button>
      </form>
    }
  `,
  imports: [
    NgClass,
    AsyncPipe,
    ReactiveFormsModule,
    AplazoPasswordControlComponent,
    AplazoButtonComponent,
  ],
})
export class ChangePasswordComponent implements OnDestroy {
  readonly #i18n = inject(I18NService);
  readonly #merchantChangePasswordUseCase = inject(
    MerchantChangePasswordUseCase
  );
  readonly #scope = 'change-password';
  readonly #destroy$ = new Subject<void>();

  oldPassword = new FormControl<string>('', [
    Validators.required,
    Validators.minLength(8),
  ]);

  newPassword = new FormControl<string>('', [
    Validators.required,
    Validators.minLength(8),
  ]);

  confirmPassword = new FormControl<string | null>('', {
    nonNullable: false,
    validators: [Validators.required, Validators.minLength(8)],
  });

  form = new FormGroup(
    {
      oldPassword: this.oldPassword,
      newPassword: this.newPassword,
      confirmPassword: this.confirmPassword,
    },
    {
      validators: compareTwoFormControlsByName({
        baseInputName: 'newPassword',
        toCompareInputName: 'confirmPassword',
      }),
    }
  );

  errorDescriptor$ = this.confirmPassword.valueChanges.pipe(
    takeUntil(this.#destroy$),
    map(value => {
      const hasDigit = /(?=.*\d)/g; // use positive look ahead to see if at least one digit exists
      const hasUppercase = /(?=.*[A-Z])/g; // use positive look ahead to see if at least one upper case letter exists

      return {
        hasMinLength: value?.length >= 8 || false,
        hasDigit: value?.match(hasDigit) !== null || false,
        hasUppercase: value?.match(hasUppercase) !== null || false,
      };
    }),
    startWith({
      hasMinLength: false,
      hasDigit: false,
      hasUppercase: false,
    })
  );

  hasError$ = this.form.statusChanges.pipe(
    startWith(this.form.status),
    combineLatestWith(this.errorDescriptor$),
    map(([formStatus, errorDescriptor]) => {
      return (
        formStatus === 'INVALID' ||
        Object.values(errorDescriptor).some(value => value === false)
      );
    })
  );

  changePasswordInjectedText$: Observable<IProfileChangePasswordInjectedTextUI> =
    combineLatest([
      this.#i18n.getTranslateObjectByKey<{
        required: string;
        maxLength: string;
        pattern: string;
        comparePassword: string;
      }>({
        key: 'errors',
        scope: this.#scope,
      }),
      this.#i18n.getTranslateObjectByKey<{
        oldPassword: {
          label: string;
          placeholder?: string;
        };
        newPassword: {
          label: string;
          placeholder?: string;
        };
        confirmPassword: {
          label: string;
          placeholder?: string;
          minLenghtDescription: string;
          digitDescription: string;
          uppercaseDescription: string;
        };
        submitButton: string;
      }>({
        key: 'form',
        scope: this.#scope,
      }),
    ]).pipe(map(([errors, form]) => ({ errors, form })));

  changePassword(): void {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.#merchantChangePasswordUseCase
        .execute({
          newPassword: this.newPassword.value,
          oldPassword: this.oldPassword.value,
          confirmNewPassword: this.confirmPassword.value,
        })
        .pipe(take(1))
        .subscribe();
    }
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
