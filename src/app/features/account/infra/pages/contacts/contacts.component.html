<aplz-ui-card class="p-6">
  <div class="flex flex-col gap-6">
    <div
      class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
      <div>
        <h2 class="font-semibold text-lg text-dark-secondary mb-1">
          Datos de Contactos
        </h2>
      </div>

      <app-create-one-button
        title=""
        buttonLabel="Agregar Nuevo Contacto"
        (createOne)="createNewContact()">
      </app-create-one-button>
    </div>

    @if (errorMessage()) {
      <div
        class="p-4 rounded mb-4 bg-alert-critical-background text-special-danger border-l-4 border-alert-critical-stroke">
        <p>{{ errorMessage() }}</p>
        <button
          type="button"
          aplzButton
          aplzColor="aplazo"
          size="xs"
          aplzAppearance="solid"
          (click)="loadContacts()">
          Reintentar
        </button>
      </div>
    }

    @if (contacts$ | async; as contacts) {
      @if (contacts.data.length === 0) {
        <aplz-ui-common-message
          [i18Text]="{
            title: 'No hay contactos registrados',
            description: ''
          }"
          imgName="emptyLoans">
        </aplz-ui-common-message>
      } @else {
        <div class="mb-4 text-sm text-dark-tertiary flex items-center">
          <aplz-ui-icon name="info" size="sm" class="mr-2"></aplz-ui-icon>
          <span>{{ getRolesAvailabilityMessage() }}</span>
        </div>

        <div class="overflow-x-auto">
          <table
            aplzSimpleTable
            aria-label="Lista de Contactos del Comercio"
            class="w-full">
            <tr aplzSimpleTableHeaderRow class="uppercase">
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center px-6 py-3 font-semibold text-dark-primary">
                Rol
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center px-6 py-3 font-semibold text-dark-primary">
                Nombre
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center px-6 py-3 font-semibold text-dark-primary">
                Correo Electrónico
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center px-6 py-3 font-semibold text-dark-primary">
                Teléfono
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center px-6 py-3 font-semibold text-dark-primary">
                Acciones
              </th>
            </tr>

            @for (item of contacts.data; track item.id) {
              <tr aplzSimpleTableBodyRow [striped]="true">
                <td
                  aplzSimpleTableBodyCell
                  class="text-center px-6 py-4 text-dark-secondary">
                  {{ item.role }}
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="text-center px-6 py-4 text-dark-secondary">
                  {{ item.name }}
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="text-center px-6 py-4 text-dark-secondary">
                  {{ item.email }}
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="text-center px-6 py-4 text-dark-secondary">
                  {{ item.phone || 'No proporcionado' }}
                </td>
                <td aplzSimpleTableBodyCell class="text-center px-8 py-4">
                  <div class="flex justify-center space-x-4">
                    <button
                      aplzButton
                      aplzAppearance="stroked"
                      size="xs"
                      aplzColor="light"
                      (click)="editContact(item)">
                      <aplz-ui-icon name="edit-square" size="sm"></aplz-ui-icon>
                    </button>
                    <button
                      aplzButton
                      aplzAppearance="stroked"
                      aplzColor="danger"
                      size="xs"
                      (click)="deleteContact(item)">
                      <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
                    </button>
                  </div>
                </td>
              </tr>
            }
          </table>
        </div>
      }
    }
  </div>
</aplz-ui-card>
