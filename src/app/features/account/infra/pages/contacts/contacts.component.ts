import { AsyncPipe } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { take } from 'rxjs';

import { CreateOneButtonComponent } from '../../components/create-one-button.component';
import { UserStoreService } from 'src/app/features/user/src/application/services/user-store.service';
import { ContactUI } from '../../../application/dtos/contact.dto';
import { UpdateContactWithDialogFormService } from '../../services/edit-contact-with-dialog-form.service';
import { CreateContactWithDialogFormService } from '../../services/create-contact-with-dialog.service';
import { DeleteContactWithDialogService } from '../../services/delete-contact-with-dialog.service';
import { GetContactInfoUseCase } from '../../../application/usecases/get-contact.usecase';
import { MerchantStoreService } from '../../services/merchant-store.service';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconXMark, iconEditSquare, iconInfo } from '@aplazo/ui-icons';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';

@Component({
  selector: 'app-contacts',
  templateUrl: './contacts.component.html',
  imports: [
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponent,
    AsyncPipe,
    CreateOneButtonComponent,
    AplazoIconComponent,
    AplazoButtonComponent,
  ],
})
export class ContactsComponent implements OnInit {
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #userStore = inject(UserStoreService);
  readonly #getContactsUseCase = inject(GetContactInfoUseCase);
  readonly #updateContactService = inject(UpdateContactWithDialogFormService);
  readonly #createContactService = inject(CreateContactWithDialogFormService);
  readonly #deleteContactService = inject(DeleteContactWithDialogService);
  readonly merchantStore = inject(MerchantStoreService);

  readonly errorMessage = signal<string | null>(null);

  readonly merchantId$ = this.#userStore.merchantId$;
  readonly hasAccess$ = this.#userStore.role$.pipe(takeUntilDestroyed());

  readonly contacts$ = this.merchantStore.merchantContacts$;

  ngOnInit(): void {
    this.loadContacts();
  }

  constructor() {
    this.#iconRegistry.registerIcons([iconXMark, iconEditSquare, iconInfo]);
  }

  loadContacts(): void {
    this.errorMessage.set(null);

    this.#getContactsUseCase
      .execute()
      .pipe(take(1))
      .subscribe({
        next: contacts => {
          this.merchantStore.setMerchantContacts(contacts);
        },
      });
  }

  editContact(contact: ContactUI): void {
    this.#updateContactService.execute(contact);
  }

  deleteContact(contact: ContactUI): void {
    this.#deleteContactService.execute(contact);
  }

  createNewContact(): void {
    this.#createContactService.execute();
  }

  getRolesAvailabilityMessage(): string {
    const availableRoles = this.merchantStore.getAvailableRoles();

    if (availableRoles.length === 0) {
      return 'No hay roles disponibles para asignar';
    }

    return `${availableRoles.length} ${
      availableRoles.length === 1 ? 'rol disponible' : 'roles disponibles'
    } para asignar`;
  }
}
