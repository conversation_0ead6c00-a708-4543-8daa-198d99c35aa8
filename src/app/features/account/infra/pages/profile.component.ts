import { Async<PERSON>ipe } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconInfo } from '@aplazo/ui-icons';
import { Observable, combineLatest, take } from 'rxjs';
import { UserStoreService } from '../../../user/src/application/services/user-store.service';
import { IMerchantWithoutBillingDto } from '../../application/dtos/merchant-without-billing.dto';
import { MerchantWithoutBillingInfoUseCase } from '../../application/usecases/merchant-without-billing.usecase';

export declare class IProfileFormInjectedTextUI {
  email: {
    title: string;
    inputLabel: string;
  };
  merchant: {
    title: string;
    inputNameLabel: string;
    inputWebsiteLabel: string;
    inputAddressLabel: string;
    idLabel: string;
  };
  representative: {
    title: string;
    inputNameLabel: string;
    inputRoleLabel: string;
    inputPhoneNumberLabel: string;
  };
  message: {
    content: string;
    supportEmail: string;
    whatsappURL: string;
  };
  errors: {
    required: string;
    maxNum: string;
    onlyNumbers: string;
    websitePattern: string;
  };
}

@Component({
  selector: 'app-profile',
  template: `
    @if (profileFormInjectedText$ | async; as textUI) {
      <form [formGroup]="profileForm" class="max-w-4xl">
        <div class="mb-4">
          <h3 class="font-semibold mb-4">
            {{ textUI.email.title }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.email.inputLabel }}
              </aplz-ui-form-label>
              <input type="text" aplzFormInput formControlName="email" />
            </aplz-ui-form-field>
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.merchant.idLabel || 'ID' }}
              </aplz-ui-form-label>
              <input type="text" aplzFormInput formControlName="merchantID" />
            </aplz-ui-form-field>
          </div>
        </div>
        <div class="mb-4">
          <h3 class="font-semibold mb-4">
            {{ textUI.merchant.title }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.merchant.inputNameLabel }}
              </aplz-ui-form-label>
              <input type="text" aplzFormInput formControlName="name" />
            </aplz-ui-form-field>
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.merchant.inputWebsiteLabel }}
              </aplz-ui-form-label>
              <input type="text" aplzFormInput formControlName="website" />
            </aplz-ui-form-field>
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.merchant.inputAddressLabel }}
              </aplz-ui-form-label>
              <input type="text" aplzFormInput formControlName="address" />
            </aplz-ui-form-field>
          </div>
        </div>
        <div class="mb-4">
          <h3 class="font-semibold mb-4">
            {{ textUI.representative.title }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.representative.inputNameLabel }}
              </aplz-ui-form-label>
              <input
                type="text"
                aplzFormInput
                formControlName="representativeName" />
            </aplz-ui-form-field>
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.representative.inputRoleLabel }}
              </aplz-ui-form-label>
              <input
                type="text"
                aplzFormInput
                formControlName="representativeRole" />
            </aplz-ui-form-field>
            <aplz-ui-form-field>
              <aplz-ui-form-label>
                {{ textUI.representative.inputPhoneNumberLabel }}
              </aplz-ui-form-label>
              <input
                type="text"
                aplzFormInput
                formControlName="representativePhoneNumber" />
            </aplz-ui-form-field>
          </div>
        </div>
      </form>
      <h4
        class="flex items-center px-4 py-2 bg-dark-background rounded-md text-balance font-light">
        <aplz-ui-icon name="info"></aplz-ui-icon>
        <span class="ml-4">
          {{ textUI.message.content }}
          <a class="font-normal" [href]="textUI.message.whatsappURL">
            Whatsapp
          </a>
        </span>
      </h4>
    }
  `,
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    AplazoIconComponent,
    AplazoFormFieldDirectives,
  ],
})
export class ProfileComponent implements OnInit {
  readonly #i18n = inject(I18NService);
  readonly #scope = 'profile';
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #userStore = inject(UserStoreService);
  readonly #merchantWithoutBillingUseCase = inject(
    MerchantWithoutBillingInfoUseCase
  );

  email = new FormControl<string>('');
  name = new FormControl<string>('');
  website = new FormControl<string>('');
  address = new FormControl<string>('');
  representativeName = new FormControl<string>('');
  representativeRole = new FormControl<string>('');
  representativePhoneNumber = new FormControl<string>('');
  merchantID = new FormControl<number>(0);

  profileForm = new FormGroup({
    email: this.email,
    name: this.name,
    website: this.website,
    address: this.address,
    representativeName: this.representativeName,
    representativeRole: this.representativeRole,
    representativePhoneNumber: this.representativePhoneNumber,
    merchantID: this.merchantID,
  });

  profile$ = this.#merchantWithoutBillingUseCase.execute().pipe(take(1));

  profileFormInjectedText$: Observable<IProfileFormInjectedTextUI> =
    this.#i18n.getTranslateObjectByKey<IProfileFormInjectedTextUI>({
      key: 'form',
      scope: this.#scope,
    });

  constructor() {
    this.#iconRegistry.registerIcons([iconInfo]);
  }

  ngOnInit(): void {
    combineLatest([this.#userStore.merchantId$, this.profile$])
      .pipe(take(1))
      .subscribe(data => {
        this.#fillForm(data);
      });
  }

  #fillForm([merchantID, profile]: [
    merchantID: number,
    profile: IMerchantWithoutBillingDto,
  ]): void {
    this.profileForm.setValue({
      email: profile.email,
      name: profile.name,
      website: profile.website,
      address: profile.address,
      representativeName: profile.representativeName,
      representativeRole: profile.representativeRole,
      representativePhoneNumber: profile.representativePhoneNumber,
      merchantID: merchantID,
    });
    this.profileForm.disable();
  }
}
