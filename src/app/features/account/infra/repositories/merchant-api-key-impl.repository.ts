import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { MerchantApiKeyRepository } from '../../domain/repositories/merchant-api-key.repository';

@Injectable({
  providedIn: 'root',
})
export class MerchantApiKeyRepositoryImpl
  implements MerchantApiKeyRepository<Observable<string>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getApiKey(): Observable<string> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/api-token`,
      {
        responseType: 'text',
      }
    );
  }
}
