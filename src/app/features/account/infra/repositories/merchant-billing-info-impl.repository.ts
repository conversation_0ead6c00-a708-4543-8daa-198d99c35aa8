import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { MerchantBillingInfo } from '../../domain/entities/merchant-billing-info';
import { MerchantBillingRepository } from '../../domain/repositories/merchant-billing.repository';

@Injectable({
  providedIn: 'root',
})
export class MerchantBillingInfoRepositoryImpl
  implements MerchantBillingRepository<Observable<MerchantBillingInfo>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getMerchantBilling(): Observable<MerchantBillingInfo> {
    return this.#http.get<MerchantBillingInfo>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v2/merchant/billing-info`
    );
  }
}
