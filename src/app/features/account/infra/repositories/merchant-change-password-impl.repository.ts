import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IChangePasswordDto } from '../../application/dtos/merchant-change-password.dto';
import { MerchantChangePasswordRepository } from '../../domain/repositories/merchant-change-password.repository';

@Injectable({
  providedIn: 'root',
})
export class MerchantChangePasswordRepositoryImpl
  implements
    MerchantChangePasswordRepository<IChangePasswordDto, Observable<void>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  changePassword(args: IChangePasswordDto): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.apiBaseUrl}merchant/change-password`,
      { ...args }
    );
  }
}
