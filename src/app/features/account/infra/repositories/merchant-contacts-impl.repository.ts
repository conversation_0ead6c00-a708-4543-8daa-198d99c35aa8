import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';
import {
  RetrievedContactResponse,
  NewContactDto,
  UpdateContactDto,
} from '../../application/dtos/contact.dto';

@Injectable()
export class MerchantContactsRepositoryImpl
  implements MerchantContactsRepository
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiMicroserviceBaseUrl;

  getInfo(): Observable<RetrievedContactResponse[]> {
    return this.#http.get<RetrievedContactResponse[]>(
      `${this.#apiUrl}api/v1/merchant-contact-info`
    );
  }

  createOne(request: NewContactDto): Observable<RetrievedContactResponse> {
    return this.#http.post<RetrievedContactResponse>(
      `${this.#apiUrl}api/v1/merchant-contact-info`,
      request
    );
  }

  updateOne(request: UpdateContactDto): Observable<RetrievedContactResponse> {
    return this.#http.put<RetrievedContactResponse>(
      `${this.#apiUrl}api/v1/merchant-contact-info/${request.id}`,
      request
    );
  }

  deleteOne(id: number): Observable<void> {
    return this.#http.delete<void>(
      `${this.#apiUrl}api/v1/merchant-contact-info/${id}`
    );
  }

  getCatalogs(): Observable<{ id: number; name: string }[]> {
    return this.#http.get<{ id: number; name: string }[]>(
      `${this.#apiUrl}api/v1/merchant-contact-info/business-area-catalog`
    );
  }
}
