import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IMerchantWithoutBillingDto } from '../../application/dtos/merchant-without-billing.dto';
import { MerchantWithoutBillingRepository } from '../../domain/repositories/merchant-without-billing.repository';

@Injectable({
  providedIn: 'root',
})
export class MerchantWithoutBillingRepositoryImpl
  implements
    MerchantWithoutBillingRepository<Observable<IMerchantWithoutBillingDto>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getMerchantWithoutBilling(): Observable<IMerchantWithoutBillingDto> {
    return this.#http.get<IMerchantWithoutBillingDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/company`
    );
  }
}
