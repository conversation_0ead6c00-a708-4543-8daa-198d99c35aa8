import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IMerchantWithoutBillingDto } from '../../application/dtos/merchant-without-billing.dto';
import { UpdateMerchantWithoutBillingRepository } from '../../domain/repositories/update-merchant-without-billing.repository';

@Injectable({
  providedIn: 'root',
})
export class UpdateMerchantWihtoutBillingRepositoryImpl
  implements
    UpdateMerchantWithoutBillingRepository<
      IMerchantWithoutBillingDto,
      Observable<void>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  updateMerchantWithoutBilling(
    args: IMerchantWithoutBillingDto
  ): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.apiBaseUrl}merchant/company-info`,
      args
    );
  }
}
