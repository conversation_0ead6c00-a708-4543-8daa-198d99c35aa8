import { inject, Injectable } from '@angular/core';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { DialogConfig, DialogRef, DialogService } from '@ngneat/dialog';
import { lastValueFrom, of, take } from 'rxjs';
import { CreateOneContactUseCase } from '../../application/usecases/new-contact.usecase';

import { ContactFormComponent } from '../components/contact-form/contact-form.component';
import { MerchantStoreService } from './merchant-store.service';
import {
  ContactUI,
  serializeRetrievedContactResponse,
} from '../../application/dtos/contact.dto';

@Injectable()
export class CreateContactWithDialogFormService {
  readonly #dialog = inject(DialogService);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #createUseCase = inject(CreateOneContactUseCase);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  async execute(): Promise<void> {
    try {
      const existingRoles = this.#merchantStore.getExistingRoles();

      if (existingRoles.length >= 5) {
        this.#notifier.warning({
          title: 'No hay roles disponibles',
          message:
            'Todos los roles ya han sido asignados. Para cambiar un contacto, edita uno existente.',
        });
        return;
      }

      const config: Partial<DialogConfig> = {
        enableClose: false,
        width: '320px',
        data: {
          existingRoles: existingRoles,
        },
      };

      const dialogRef = this.#dialog.open(
        ContactFormComponent,
        config
      ) as DialogRef<any, Partial<ContactUI & { hasConfirmation: boolean }>>;

      const dialogResult = await lastValueFrom(
        dialogRef.afterClosed$.pipe(take(1))
      );

      if (!dialogResult?.hasConfirmation) {
        this.#notifier.info({ title: 'Creación cancelada' });
        return;
      }

      this.#validateContactData(dialogResult, existingRoles);

      const contactData: ContactUI = {
        email: dialogResult.email!,
        phone: dialogResult.phone || '',
        name: dialogResult.name!,
        role: dialogResult.role!,
        businessArea: dialogResult.businessArea!,
      };

      const result = await lastValueFrom(
        this.#createUseCase.execute(contactData).pipe(take(1))
      );

      const serialized = serializeRetrievedContactResponse(result);
      this.#updateStore(serialized);
    } catch (error) {
      await lastValueFrom(
        this.#errorHandler.handle(error, of(null)).pipe(take(1))
      );
    }
  }

  #validateContactData(
    dialogResult: Partial<ContactUI & { hasConfirmation: boolean }>,
    existingRoles: string[]
  ): void {
    const email = dialogResult.email?.trim();
    if (!email) {
      throw new RuntimeMerchantError(
        'El correo electrónico no puede estar vacío',
        'CreateContactWithDialogFormService::validation::emptyEmail'
      );
    }

    const name = dialogResult.name?.trim();
    if (!name) {
      throw new RuntimeMerchantError(
        'El nombre no puede estar vacío',
        'CreateContactWithDialogFormService::validation::emptyName'
      );
    }

    const role = dialogResult.role;
    if (!role) {
      throw new RuntimeMerchantError(
        'El rol no puede estar vacío',
        'CreateContactWithDialogFormService::validation::emptyRole'
      );
    }

    if (existingRoles.includes(role)) {
      throw new RuntimeMerchantError(
        'Ya existe un contacto con este rol. Para actualizar la información, edite el contacto existente.',
        'CreateContactWithDialogFormService::validation::duplicateRole'
      );
    }

    const businessArea = dialogResult.businessArea;
    if (!businessArea) {
      throw new RuntimeMerchantError(
        'El área de negocio no puede estar vacía',
        'CreateContactWithDialogFormService::validation::emptyBusinessArea'
      );
    }
  }

  #updateStore(newContact: ContactUI): void {
    const storedContacts = this.#merchantStore.contacts();

    if (storedContacts.length === 0) {
      this.#merchantStore.setMerchantContacts([newContact]);
      return;
    }

    const newList = [...storedContacts, newContact];
    this.#merchantStore.setMerchantContacts(newList);
  }
}
