import { inject, Injectable } from '@angular/core';
import {
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { DialogConfig, DialogRef, DialogService } from '@ngneat/dialog';
import { lastValueFrom, of, take } from 'rxjs';
import { DeleteContactUseCase } from '../../application/usecases/delete-contact.usecase';
import { ContactUI } from '../../application/dtos/contact.dto';
import { MerchantStoreService } from './merchant-store.service';
import { ContactDeleteConfirmComponent } from '../components/contact-delete-confirm/contact-delete-confirm.component';

@Injectable()
export class DeleteContactWithDialogService {
  readonly #dialog = inject(DialogService);
  readonly #deleteUseCase = inject(DeleteContactUseCase);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #merchantStore = inject(MerchantStoreService);

  async execute(contact: ContactUI): Promise<boolean> {
    try {
      if (!contact || !contact.id) {
        throw new RuntimeMerchantError(
          'No se puede eliminar un contacto sin identificador',
          'DeleteContactWithDialogService::validation::emptyContactId'
        );
      }

      const config: Partial<DialogConfig> = {
        enableClose: false,
        width: '320px',
        data: {
          contactName: contact.name,
          contactId: contact.id,
        },
      };

      const dialogRef = this.#dialog.open(
        ContactDeleteConfirmComponent,
        config
      ) as DialogRef<any, boolean>;

      const confirmed = await lastValueFrom(
        dialogRef.afterClosed$.pipe(take(1))
      );

      if (!confirmed) {
        return false;
      }

      await lastValueFrom(
        this.#deleteUseCase.execute(contact.id).pipe(take(1))
      );

      this.#updateStore(contact.id);

      return true;
    } catch (error) {
      await lastValueFrom(
        this.#errorHandler.handle(error, of(false)).pipe(take(1))
      );
      return false;
    }
  }

  #updateStore(contactId: number): void {
    const storedContacts = this.#merchantStore.contacts();
    const updatedContacts = storedContacts.filter(
      contact => contact.id !== contactId
    );
    this.#merchantStore.setMerchantContacts(updatedContacts);
  }
}
