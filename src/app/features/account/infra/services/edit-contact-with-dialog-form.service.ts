import { inject, Injectable } from '@angular/core';
import { NotifierService, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { DialogConfig, DialogRef, DialogService } from '@ngneat/dialog';
import { lastValueFrom, take } from 'rxjs';
import { UpdateOneContactUseCase } from '../../application/usecases/update-contact.usecase';
import {
  ContactUI,
  serializeRetrievedContactResponse,
} from '../../application/dtos/contact.dto';
import { ContactFormComponent } from '../components/contact-form/contact-form.component';
import { MerchantStoreService } from './merchant-store.service';

@Injectable()
export class UpdateContactWithDialogFormService {
  readonly #dialog = inject(DialogService);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #editUseCase = inject(UpdateOneContactUseCase);

  async execute(contactToEdit: ContactUI): Promise<void> {
    try {
      const dialogResult = await this.#getDialogResult(contactToEdit);

      const cancelTitle = 'Edición cancelada';

      if (!dialogResult?.hasConfirmation) {
        this.#notifier.info({ title: cancelTitle });
        return;
      }

      this.#simpleFieldsValidation(dialogResult);

      const hasChanges = this.#hasChanges({
        contactFromDialog: dialogResult,
        contactToEdit,
      });

      if (!hasChanges) {
        this.#notifier.warning({
          title: 'No hay cambios para guardar',
        });
        return;
      }

      // Validación de rol duplicado para edición
      if (contactToEdit.role !== dialogResult.role) {
        const existingRoles =
          this.#getExistingRolesExcludingCurrent(contactToEdit);
        if (existingRoles.includes(dialogResult.role as string)) {
          this.#notifier.error({
            title: 'Error de validación',
            message:
              'Ya existe un contacto con este rol. Para actualizar la información, edite el contacto existente.',
          });
          return;
        }
      }

      const request: ContactUI = {
        phone: dialogResult.phone as string,
        email: dialogResult.email as string,
        name: dialogResult.name as string,
        role: dialogResult.role as string,
        id: dialogResult.id,
        businessArea: dialogResult.businessArea as
          | 'Finance'
          | 'Marketing'
          | 'Support'
          | 'IT'
          | 'Commercial',
      };

      const response = await lastValueFrom(
        this.#editUseCase.execute(request).pipe(take(1))
      );

      const serializedResponse = serializeRetrievedContactResponse(response);

      const storedContacts = await lastValueFrom(
        this.#merchantStore.merchantContacts$.pipe(take(1))
      );

      if (
        !storedContacts ||
        !storedContacts.data ||
        !storedContacts.data.length
      ) {
        const contacts = [serializedResponse];

        this.#merchantStore.setMerchantContacts(contacts);
        return;
      }

      const newList = JSON.parse(
        JSON.stringify(storedContacts.data)
      ) as ContactUI[];

      const index = newList.findIndex(c => c.id === serializedResponse.id);

      if (index === -1) {
        newList.push(serializedResponse);
      } else {
        newList[index] = serializedResponse;
      }

      this.#merchantStore.setMerchantContacts(newList);
    } catch (error) {
      this.#notifier.error({
        title: 'Error al actualizar el contacto',
        message:
          error instanceof RuntimeMerchantError
            ? error.message
            : 'Hubo un problema al procesar tu solicitud. Por favor, verifica los datos e intenta nuevamente.',
      });
    }
  }

  async #getDialogResult(
    contactToEdit: ContactUI
  ): Promise<(Partial<ContactUI> & { hasConfirmation: boolean }) | undefined> {
    const existingRoles = this.#getExistingRolesExcludingCurrent(contactToEdit);

    const data = {
      ...contactToEdit,
      existingRoles,
    };

    const config: Partial<DialogConfig> = {
      enableClose: false,
      width: '320px',
      data,
    };

    const dialogRef = this.#dialog.open(
      ContactFormComponent,
      config
    ) as DialogRef<any, Partial<ContactUI> & { hasConfirmation: boolean }>;

    const dialogResult = await lastValueFrom(
      dialogRef.afterClosed$.pipe(take(1))
    );

    return dialogResult;
  }

  #simpleFieldsValidation(
    contactFromDialog:
      | (Partial<ContactUI> & { hasConfirmation: boolean })
      | undefined
  ): void | never {
    const email = contactFromDialog?.email?.trim() ?? null;

    if (!email) {
      throw new RuntimeMerchantError(
        'El campo email es requerido',
        'UpdateContactWithDialogFormService::simpleFieldsValidation::emptyEmail'
      );
    }

    const name = contactFromDialog?.name?.trim() ?? null;

    if (!name) {
      throw new RuntimeMerchantError(
        'El campo nombre es requerido',
        'UpdateContactWithDialogFormService::simpleFieldsValidation::emptyName'
      );
    }

    const role = contactFromDialog?.role?.trim() ?? null;

    if (!role) {
      throw new RuntimeMerchantError(
        'El campo rol es requerido',
        'UpdateContactWithDialogFormService::simpleFieldsValidation::emptyRole'
      );
    }

    const businessArea = contactFromDialog?.businessArea;

    if (!businessArea) {
      throw new RuntimeMerchantError(
        'El campo área de negocio es requerido',
        'UpdateContactWithDialogFormService::simpleFieldsValidation::emptyBusinessArea'
      );
    }

    const id = contactFromDialog?.id;

    if (!id) {
      throw new RuntimeMerchantError(
        'El campo id es requerido',
        'UpdateContactWithDialogFormService::simpleFieldsValidation::emptyId'
      );
    }
  }

  #hasChanges(args: {
    contactToEdit: ContactUI;
    contactFromDialog: Partial<ContactUI> & { hasConfirmation: boolean };
  }): boolean {
    const contactToEdit = args.contactToEdit;
    const contactFromDialog = args.contactFromDialog;

    const hasChanges =
      contactToEdit.email !== contactFromDialog.email ||
      contactToEdit.role !== contactFromDialog.role ||
      contactToEdit.name !== contactFromDialog.name ||
      contactToEdit.phone !== contactFromDialog.phone ||
      contactToEdit.businessArea !== contactFromDialog.businessArea;

    return hasChanges;
  }

  #getExistingRolesExcludingCurrent(contactToEdit: ContactUI): string[] {
    return this.#merchantStore
      .getExistingRoles()
      .filter(role => role !== contactToEdit.role);
  }
}
