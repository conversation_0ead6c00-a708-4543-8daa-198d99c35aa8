import { Injectable, signal } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { ContactUI } from '../../application/dtos/contact.dto';

@Injectable({ providedIn: 'root' })
export class MerchantStoreService {
  readonly #merchantContacts$ = new BehaviorSubject<{
    data: ContactUI[];
  }>({
    data: [],
  });

  readonly contacts = signal<ContactUI[]>([]);

  merchantContacts$ = this.#merchantContacts$.asObservable();

  setMerchantContacts(contacts: ContactUI[] | null) {
    if (!contacts || contacts.length === 0) {
      this.#merchantContacts$.next({
        data: [],
      });
      this.contacts.set([]);
      return;
    }

    const isSameContacts =
      this.#merchantContacts$.value.data.length === contacts.length &&
      this.#merchantContacts$.value.data.every((contact, idx) => {
        return (
          contact.email === contacts[idx].email &&
          contact.id === contacts[idx].id &&
          contact.role === contacts[idx].role &&
          contact.businessArea === contacts[idx].businessArea &&
          contact.phone === contacts[idx].phone &&
          contact.name === contacts[idx].name
        );
      });

    if (isSameContacts) {
      return;
    }

    this.#merchantContacts$.next({
      data: [...contacts],
    });
    this.contacts.set([...contacts]);
  }

  getAvailableRoles(): string[] {
    const allRoles = ['Mkt', 'Support', 'IT', 'Commercial', 'Finance'];
    const existingRoles = this.getExistingRoles();

    return allRoles.filter(role => !existingRoles.includes(role));
  }

  getExistingRoles(): string[] {
    return this.#merchantContacts$.value.data.map(contact => contact.role);
  }

  clearAll() {
    this.#merchantContacts$.next({ data: [] });
    this.contacts.set([]);
  }
}
