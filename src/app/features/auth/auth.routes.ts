import { Route } from '@angular/router';
import { DASH_ROUTES } from '../../config/app-route-core';

export default [
  {
    path: '',
    redirectTo: DASH_ROUTES.login,
    pathMatch: 'full',
  },
  {
    path: DASH_ROUTES.login,
    loadComponent: () =>
      import('./pages/login.component').then(stl => stl.LoginComponent),
  },
  {
    path: DASH_ROUTES.forgotPass,
    loadComponent: () =>
      import('./pages/forgot-password.component').then(
        stl => stl.ForgotPasswordComponent
      ),
  },
  {
    path: DASH_ROUTES.enterNewPassword,
    loadComponent: () =>
      import('./pages/enter-new-password.component').then(
        stl => stl.EnterNewPasswordComponent
      ),
  },
] satisfies Route[];
