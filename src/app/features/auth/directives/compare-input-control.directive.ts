import { FormGroup, ValidatorFn } from '@angular/forms';

export const compareTwoFormControlsByName: (req: {
  baseInputName: string;
  toCompareInputName: string;
}) => ValidatorFn =
  ({ baseInputName, toCompareInputName }) =>
  (control: FormGroup) => {
    const basePassword = control.get(baseInputName);
    const confirmPassword = control.get(toCompareInputName);

    if (
      basePassword &&
      confirmPassword &&
      basePassword.value === confirmPassword.value
    ) {
      if (confirmPassword.hasError('verifyPassword')) {
        const errs = { ...confirmPassword?.errors };

        delete errs.verifiedPassword;

        confirmPassword.setErrors(JSON.parse(JSON.stringify(errs)));
        confirmPassword.updateValueAndValidity();
      }

      return null;
    }

    confirmPassword?.setErrors({
      ...confirmPassword?.errors,
      verifyPassword: true,
    });

    return {
      verifyPassword: true,
    };
  };
