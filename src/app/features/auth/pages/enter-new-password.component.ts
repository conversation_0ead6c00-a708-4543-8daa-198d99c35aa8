import { Component, OnInit, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NotifierService, RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { AplazoPasswordControlComponent } from '@aplazo/shared-ui/merchant';
import { BehaviorSubject, of, switchMap, take } from 'rxjs';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { UserForgottenPasswordVerificationTokenUseCase } from '../../user/src/application/usecases/forgotten-password-verification-token.usecase';
import { UserSetNewPasswordUseCase } from '../../user/src/application/usecases/set-new-password.usecase';
import { compareTwoFormControlsByName } from '../directives/compare-input-control.directive';

@Component({
  selector: 'app-enter-new-password',
  imports: [
    AplazoLogoComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
    AplazoPasswordControlComponent,
  ],
  template: `
    <section
      class="h-svh min-h-fit w-full max-w-xs mx-auto pt-16 md:pt-32 text-center">
      <div class="h-fit">
        <aplz-ui-logo size="md"></aplz-ui-logo>
      </div>

      @if (!isValidToken) {
        <h4 class="text-xl my-10">Token inválido</h4>
        <p class="mb-12">
          Es necesario que solicites nuevamente el cambio de contraseña
        </p>
        <button
          class="mt-8"
          type="button"
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToForgotPassword()">
          Solicitar cambio de contraseña
        </button>
      } @else {
        <h4 class="text-xl my-10">Ingresa tu nueva contraseña</h4>
        <p class="mb-12">
          La contraseña debe contener un mínimo de 8 caracteres, al menos una
          mayúscula, letras minúsculas, números y un carácter
          (~&#64;#$%^&*+=:;!.?"-)
        </p>
        <form [formGroup]="form" (ngSubmit)="setNewPassword()">
          <aplz-mui-password-control
            formControlName="basePassword"
            [textUI]="{
              label: 'Contraseña',
              placeholder: 'Escribe tu contraseña',
              requiredError: 'Este campo es obligatorio',
              minLengthError: 'Ingresa información válida. Mínimo 8 caracteres',
              patternError:
                'Ingresa información válida. Mínimo 1 letra mayúscula, 1 letra minúscula, 1 número.'
            }">
          </aplz-mui-password-control>
          <aplz-mui-password-control
            formControlName="confirmPassword"
            [textUI]="{
              label: 'Confirma Contraseña',
              placeholder: 'Confirma tu contraseña',
              requiredError: 'Este campo es obligatorio',
              minLengthError: 'Ingresa información válida. Mínimo 8 caracteres',
              patternError:
                'Ingresa información válida. Mínimo 1 letra mayúscula, 1 letra minúscula, 1 número.',
              comparedWithOtherError:
                'Verifique que las contraseñas sean iguales'
            }">
          </aplz-mui-password-control>
          <button
            class="mt-10"
            type="submit"
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            [rounded]="true"
            [fullWidth]="true">
            Enviar instrucciones
          </button>
          <button
            class="mt-8"
            type="button"
            aplzButton
            aplzAppeatance="basic"
            size="md"
            [rounded]="true"
            [fullWidth]="true"
            (click)="goToLoginRoute()">
            Iniciar sesión como comerciante
          </button>
        </form>
      }
    </section>
  `,
})
export class EnterNewPasswordComponent implements OnInit {
  readonly #redirecter = inject(RedirectionService);
  readonly #notifier = inject(NotifierService);
  readonly #route = inject(ActivatedRoute);

  readonly #verificationTokenUseCase = inject(
    UserForgottenPasswordVerificationTokenUseCase
  );
  readonly #setNewPasswortUseCase = inject(UserSetNewPasswordUseCase);

  readonly #isValidToken = new BehaviorSubject<boolean>(false);

  basePassword = new FormControl<string>('', {
    validators: [Validators.required, Validators.minLength(8)],
    nonNullable: true,
  });

  confirmPassword = new FormControl<string>('', {
    validators: [Validators.required, Validators.minLength(8)],
    nonNullable: true,
  });

  form = new FormGroup(
    {
      basePassword: this.basePassword,
      confirmPassword: this.confirmPassword,
    },
    {
      validators: [
        compareTwoFormControlsByName({
          baseInputName: 'basePassword',
          toCompareInputName: 'confirmPassword',
        }),
      ],
    }
  );

  get hasComparedError(): boolean {
    return (
      (this.form.dirty || this.form.touched) &&
      this.form.hasError('verifyPassword')
    );
  }

  get isValidToken(): boolean {
    return this.#isValidToken.value;
  }

  goToLoginRoute(): void {
    this.#redirecter.internalNavigation([
      DASH_ROUTES.authentication,
      DASH_ROUTES.login,
    ]);
  }
  goToForgotPassword(): void {
    this.#redirecter.internalNavigation([
      DASH_ROUTES.authentication,
      DASH_ROUTES.forgotPass,
    ]);
  }

  setNewPassword(): void {
    this.form.markAllAsTouched();

    if (this.form.invalid) {
      return;
    }

    if (!this.isValidToken) {
      this.#notifier.warning({
        title: 'Token inválido',
        message:
          'Es necesario que solicites nuevamente el cambio de contraseña',
      });
      return;
    }

    this.#route.queryParamMap
      .pipe(
        switchMap(params =>
          this.#setNewPasswortUseCase.execute({
            token: params.get('token') ?? '',
            password: this.basePassword.value,
            newRoles: params.get('source') === 'ACS',
          })
        ),
        take(1)
      )
      .subscribe(() => {
        this.goToLoginRoute();
      });
  }

  ngOnInit(): void {
    this.#route.queryParamMap
      .pipe(
        switchMap(params => {
          const newServiceIdentifier = 'ACS';
          const tokenOrNull = params.get('token');

          if (
            params.get('source') === newServiceIdentifier &&
            tokenOrNull != null &&
            tokenOrNull !== ''
          ) {
            return of({
              status: true,
            });
          }

          return this.#verificationTokenUseCase.execute({
            token: tokenOrNull ?? '',
          });
        }),
        take(1)
      )
      .subscribe(resp => {
        this.#isValidToken.next(resp.status);
      });
  }
}
