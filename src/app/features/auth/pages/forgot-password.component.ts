import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { finalize, take } from 'rxjs';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { ForgotPasswordUseCase } from '../../user/src/application/usecases/forgot-password.usecase';

@Component({
  selector: 'app-forgot-password',
  imports: [
    AplazoLogoComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
  ],
  template: `
    <section
      class="h-svh min-h-fit w-full max-w-xs mx-auto pt-16 md:pt-32 text-center">
      <div class="h-fit">
        <aplz-ui-logo size="md"></aplz-ui-logo>
      </div>

      <h4 class="text-xl my-10">¿Olvidaste tu contraseña?</h4>
      <p class="mb-12">
        Ingresa el correo electrónico asociado a tu cuenta para recibir los
        pasos para reestablecer tu contraseña.
      </p>

      <form [formGroup]="form" (ngSubmit)="sendEmail()">
        <aplz-ui-form-field>
          <aplz-ui-form-label>Correo electrónico</aplz-ui-form-label>
          <input
            type="text"
            inputmode="email"
            placeholder="Escribe tu correo electrónico"
            aplzFormInput
            formControlName="email" />

          <ng-container aplzFormError>
            @if (email.touched && email.hasError('required')) {
              <p>Este campo es obligatorio</p>
            }
          </ng-container>

          <ng-container aplzFormError>
            @if (
              email.touched &&
              !email.hasError('required') &&
              email.hasError('email')
            ) {
              <p class="text-left text-pretty">
                Ingresa un correo electrónico válido. Ej. ejemplo&#64;gmail.com
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <button
          class="mt-10"
          type="submit"
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          [rounded]="true"
          [fullWidth]="true">
          Enviar instrucciones
        </button>
        <button
          class="mt-8"
          type="button"
          aplzButton
          aplzAppearance="basic"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToLoginRoute()">
          Iniciar sesión como comerciante
        </button>
      </form>
    </section>
  `,
})
export class ForgotPasswordComponent {
  readonly #redirectionService = inject(RedirectionService);
  readonly #forgotPasswordUseCase = inject(ForgotPasswordUseCase);

  email = new FormControl<string>('', {
    validators: [Validators.required, Validators.email],
    nonNullable: true,
  });

  form = new FormGroup({
    email: this.email,
  });

  sendEmail(): void {
    this.form.markAllAsTouched();

    if (this.form.invalid) {
      return;
    }
    const email = this.email.value;

    this.#forgotPasswordUseCase
      .execute({ email })
      .pipe(
        take(1),
        finalize(() => {
          this.goToLoginRoute();
        })
      )
      .subscribe();
  }

  goToLoginRoute(): void {
    this.#redirectionService.internalNavigation([
      DASH_ROUTES.authentication,
      DASH_ROUTES.login,
    ]);
  }
}
