import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import {
  LoginFormComponent,
  LoginFormTextUI,
} from '@aplazo/shared-ui/merchant';
import { StatsigService } from '@statsig/angular-bindings';
import { lastValueFrom, map } from 'rxjs';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../config/merchant-core.environment';
import { LoginCentralizedUsecase } from '../../user/src/application/usecases/login-centralized.usecase';

interface ILoginFormTextUIResponse {
  email: {
    label: string;
    placeholder: string;
    requiredError: string;
    emailPatternError: string;
    hideRequiredMark: boolean;
  };
  password: {
    label: string;
    placeholder: string;
    requiredError: string;
    hideRequiredMark: boolean;
  };
  submitButton: string;
}

@Component({
  selector: 'app-login',
  imports: [
    AplazoLogoComponent,
    LoginFormComponent,
    AplazoButtonComponent,
    AsyncPipe,
  ],
  template: `
    @if (textTemplate$ | async; as text) {
      <div
        class="flex flex-col justify-start gap-y-12 max-w-xs mx-auto min-h-svh text-center py-20">
        <aplz-ui-logo></aplz-ui-logo>
        <h1 class="font-medium text-2xl">
          {{ text.header.title }}
        </h1>
        <aplz-mui-login-form
          (login)="login($event)"
          [textUI]="text.form"></aplz-mui-login-form>
        <button
          class="-mt-8"
          aplzButton
          aplzAppearance="basic"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToForgotPasswordRoute()">
          {{ text.actions.forgotPasswordButton }}
        </button>
        <h2 class="text-lg">
          {{ text.actions.title }}
        </h2>
        <button
          aplzButton
          aplzAppearance="stroked"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToRegisterMerchantRoute()">
          {{ text.actions.registerButton }}
        </button>
        <button
          aplzButton
          aplzAppeatance="basic"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToCustomerLoginRoute()">
          {{ text.actions.customerLoginButton }}
        </button>
      </div>
    }
  `,
})
export class LoginComponent {
  readonly #merchantEnv = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #appRoutes = DASH_ROUTES;
  readonly #redirecter = inject(RedirectionService);
  readonly #i18n = inject(I18NService);
  readonly #featureFlagsService = inject(StatsigService);
  readonly #loginCentralizedUseCase = inject(LoginCentralizedUsecase);
  readonly #scope = 'login';

  textTemplate$ = this.#i18n
    .getTranslateObjectByKey<{
      header: {
        title: string;
      };
      form: ILoginFormTextUIResponse;
      actions: {
        title: string;
        registerButton: string;
        customerLoginButton: string;
        forgotPasswordButton?: string;
      };
    }>({
      key: 'template',
      scope: this.#scope,
    })
    .pipe(
      map<
        {
          header: {
            title: string;
          };
          form: ILoginFormTextUIResponse;
          actions: {
            title: string;
            registerButton: string;
            customerLoginButton: string;
            forgotPasswordButton?: string;
          };
        },
        {
          header: {
            title: string;
          };
          form: LoginFormTextUI;
          actions: {
            title: string;
            registerButton: string;
            customerLoginButton: string;
            forgotPasswordButton?: string;
          };
        }
      >(resp => ({
        actions: resp.actions,
        header: resp.header,
        form: {
          username: {
            label: resp.form.email.label,
            placeholder: resp.form.email.placeholder,
            requiredError: resp.form.email.requiredError,
            emailPatternError: resp.form.email.emailPatternError,
            hideRequiredMarker: resp.form.email.hideRequiredMark,
          },
          password: {
            label: resp.form.password.label,
            placeholder: resp.form.password.placeholder,
            requiredError: resp.form.password.requiredError,
            hideRequiredMarker: resp.form.password.hideRequiredMark,
          },
          submitButton: { label: resp.form.submitButton },
        },
      }))
    );

  async login(credentials: {
    username: string;
    password: string;
  }): Promise<void> {
    try {
      const user = await lastValueFrom(
        this.#loginCentralizedUseCase.execute({
          merchantUsername: credentials.username,
          merchantPassword: credentials.password,
          authType: 'LOGIN_RETRY',
        })
      );

      if (user?.isLoggedIn) {
        await this.#featureFlagsService.updateUserAsync({
          ...this.#featureFlagsService.getClient()?.getContext().user,
          custom: {
            ...this.#featureFlagsService.getClient()?.getContext().user.custom,
            merchantId: String(user.merchantId),
            role: user.role,
            username: user.username,
            lastLogin: user.lastLogin.getTime(),
          },
          userID: String(user.merchantId),
          email: user.email,
        });

        this.#featureFlagsService.logEvent('panel_front_login', Date.now(), {
          merchantId: String(user.merchantId),
          role: user.role,
          username: user.username,
          lastLogin: user.lastLogin.getTime().toString(),
        });

        await this.#redirecter.internalNavigation([this.#appRoutes.rootApp]);
      }
    } catch (error) {
      console.warn(error.message);

      return void 0;
    }
  }

  goToRegisterMerchantRoute(): void {
    this.#redirecter.externalNavigation(
      this.#merchantEnv.redirectionsMerchantRegisterPage,
      '_blank'
    );
  }

  goToCustomerLoginRoute(): void {
    this.#redirecter.externalNavigation(
      this.#merchantEnv.redirectionsCustomerLoginPage,
      '_blank'
    );
  }

  goToForgotPasswordRoute(): void {
    this.#redirecter.internalNavigation([
      '/',
      this.#appRoutes.authentication,
      this.#appRoutes.forgotPass,
    ]);
  }
}
