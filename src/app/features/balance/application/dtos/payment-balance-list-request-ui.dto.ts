import {
  RawDateDayFirst,
  RawDateYearFirstWithHyphen,
} from '@aplazo/merchant/shared';
import { TransactionStatusId } from '../../domain/entities/payment-balance.status';

export interface IPaymentBalanceListRequestUIDto {
  status: TransactionStatusId;
  date: RawDateDayFirst;
  pageNum: number;
  pageSize: number;
  dynamicInitialDateToBalances: RawDateYearFirstWithHyphen | null;
  initialDateToBalances?: Date;
}
