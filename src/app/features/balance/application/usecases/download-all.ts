import { HttpErrorResponse } from '@angular/common/http';
import { Directive, inject } from '@angular/core';
import {
  Guard,
  NotifierService,
  RawDateDayFirst,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  EMPTY,
  finalize,
  MonoTypeOperatorFunction,
  Observable,
  tap,
  throwError,
} from 'rxjs';
import {
  TRANSACTION_STATUS,
  TransactionStatusId,
} from '../../domain/entities/payment-balance.status';
import { DownloadAllRepository } from '../../domain/repositories/balance-download.repository';

export interface DownloadAllUIRequest {
  date: RawDateDayFirst;
  merchantId: number;
  status?: TransactionStatusId;
  [key: string]: any;
}

/**
 * This abstraction provides us with a base usecase to download all the items of a specific entity.
 * It is intended to be extended by usecases that need to download all the items of a specific entity.
 *
 * As a first approach, this abstraction is intended to be used by the following entities:
 * - Payment Balance Receipts
 * - Payment Balance Invoices
 * - Payment Balance Summary Report
 *
 * note: Directive decorator is used to avoid injecting services in the constructor
 */
@Directive()
export abstract class DownloadAllBaseUsecase {
  abstract getRepository(): DownloadAllRepository;
  abstract fromUIToRepositoryRequest(args: DownloadAllUIRequest): any;
  abstract getNameOfTheItemsToDownload(): string;
  abstract handleResponseAndDownload(args: {
    date: RawDateDayFirst;
    merchantId: number;
  }): MonoTypeOperatorFunction<any>;

  readonly #notifier: NotifierService<number> = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #temporal = inject(TemporalService);

  readonly #processesQueue = new Set<string>();

  addActiveProcess(date: string): void {
    this.#processesQueue.add(date);
  }

  activeProcessesCount(): number {
    return this.#processesQueue.size;
  }

  execute(args: DownloadAllUIRequest): Observable<any> {
    try {
      if (
        !Guard.againstNullOrUndefined({ date: args.date }, 'date').succeeded
      ) {
        throw new RuntimeMerchantError(
          'No se proporcionó una fecha para descargar ' +
            this.getNameOfTheItemsToDownload(),
          this.#getEntityNameIdentifier() + '::execute::nullDate'
        );
      }

      if (!Guard.againstInvalidRawDateDayFirst(args.date).succeeded) {
        throw new RuntimeMerchantError(
          'Fecha inválida',
          this.#getEntityNameIdentifier() + '::execute::invalidDate'
        );
      }

      Guard.againstInvalidDate({
        date: this.#temporal.fromStringToDate(args.date),
        origin: this.#getEntityNameIdentifier(),
        errorMessage:
          'Recibimos una fecha inválida para descargar ' +
          this.getNameOfTheItemsToDownload(),
      });

      if (
        Object.hasOwnProperty.call(args, 'status') &&
        TRANSACTION_STATUS[args.status] == null
      ) {
        throw new RuntimeMerchantError(
          'El estatus no es válido',
          this.#getEntityNameIdentifier() + '::execute::invalidStatus'
        );
      }

      if (this.#processesQueue.has(args.date)) {
        this.#notifier.warning({
          title: 'Descarga en proceso',
          message: `${this.getNameOfTheItemsToDownload()} se están descargando, por favor espere a que termine la descarga.`,
        });

        return EMPTY;
      }

      this.addActiveProcess(args.date);

      const infoNotifierId = this.#notifier.info({
        title: `Se está procesando la descarga de ${this.getNameOfTheItemsToDownload()}; por favor espere un momento`,
      });

      return this.getRepository()
        .byDate(this.fromUIToRepositoryRequest(args))
        .pipe(
          this.handleResponseAndDownload({
            date: args.date,
            merchantId: args.merchantId,
          }),

          tap(() => {
            this.#notifier.closeById(infoNotifierId);

            this.#notifier.success({
              title: 'Documento generado',
              message: `${this.getNameOfTheItemsToDownload()} se han generado correctamente y la descarga comenzará pronto`,
            });
          }),

          catchError(async err => {
            const text = await err?.error?.text();
            const errorDescriptor = text ? JSON.parse(text) : null;
            this.#notifier.closeById(infoNotifierId);

            if (
              err instanceof HttpErrorResponse &&
              err.status === 404 &&
              (errorDescriptor?.error.endsWith('No files found') ||
                errorDescriptor?.error.includes('not found'))
            ) {
              this.#notifier.warning({
                title: 'Detectamos un error al procesar la descarga',
                message: `No se encontraron ${this.getNameOfTheItemsToDownload()} en la fecha ${
                  args.date
                }. Por favor, intente más tarde o contacte al soporte`,
              });

              throw new RuntimeMerchantError(
                `No se encontraron ${this.getNameOfTheItemsToDownload()} en la fecha ${
                  args.date
                }. Por favor, intente más tarde o contacte al soporte`,
                this.#getEntityNameIdentifier() + '::execute::notFound'
              );
            }
            return this.#errorHandler.handle<any>(err);
          }),

          finalize(() => {
            this.#processesQueue.delete(args.date);
          })
        );
    } catch (error) {
      this.#processesQueue.delete(args.date);
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
  #getEntityNameIdentifier(): string {
    return this.constructor.name ?? 'DownloadAllBaseUsecase';
  }
}
