import { HttpErrorResponse } from '@angular/common/http';
import { Directive, inject } from '@angular/core';
import {
  DateCalculator,
  Guard,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { parse } from 'date-fns';
import {
  catchError,
  EMPTY,
  finalize,
  MonoTypeOperatorFunction,
  Observable,
  tap,
  throwError,
} from 'rxjs';

export interface DownloadOneUIRequest {
  merchantId: number;
  date: string;
  balanceId: number;
  initialDateToBalances?: Date;
  [key: string]: any;
}

@Directive()
export abstract class DownloadOneBaseUsecase {
  abstract getRepository(): any;
  abstract fromUIToRepositoryRequest(args: DownloadOneUIRequest): any;
  abstract getNameOfTheItemToDownload(): string;
  abstract handleResponseAndDownload(args: {
    merchantId: number;
    date: string;
    balanceId: number;
  }): MonoTypeOperatorFunction<any>;

  readonly #notifier: NotifierService<number> = inject(NotifierService);
  readonly #dateCalculator = inject(DateCalculator);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  readonly #processesQueue = new Set<number>();

  addActiveProcess(id: number): void {
    this.#processesQueue.add(id);
  }

  hasActiveProcess(id: number): boolean {
    return this.#processesQueue.has(id);
  }

  activeProcessesCount(): number {
    return this.#processesQueue.size;
  }

  execute(args: DownloadOneUIRequest): Observable<any> {
    const mappedErrors = ['merchantId', 'date', 'balanceId']
      .map(key => {
        const guard = Guard.againstNullOrUndefined(args, key);

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    try {
      if (mappedErrors.length > 0) {
        throw new RuntimeMerchantError(
          mappedErrors.join('; '),
          `${this.#getEntityNameIdentifier()}::execute::invalidArgs`
        );
      }

      Guard.againstInvalidNumbers(
        args.balanceId,
        this.#getEntityNameIdentifier(),
        'El ID de estado de cuenta debe ser un número entero mayor a 0'
      );

      Guard.againstInvalidDate({
        date: parse(args.date, 'yyyy-MM-dd', new Date()),
        origin: this.#getEntityNameIdentifier(),
        errorMessage: `Recibimos una fecha inválida para descargar ${this.getNameOfTheItemToDownload()}`,
      });

      if (args.initialDateToBalances) {
        Guard.againstInvalidDate({
          date: args.initialDateToBalances,
          errorMessage: `La fecha para consultar ${this.getNameOfTheItemToDownload()} no es válida`,
        });

        const todayIsEqualOrAfterControlDate =
          this.#dateCalculator.isSameOrAfterControlDate({
            baseDate: new Date(),
            date: args.initialDateToBalances,
          });

        if (!todayIsEqualOrAfterControlDate) {
          throw new RuntimeMerchantError(
            `La fecha inicial del reporte no puede ser menor a ${args.initialDateToBalances.toLocaleDateString()}`,
            `${this.#getEntityNameIdentifier()}::execute::invalidInitialDate`
          );
        }
      }

      if (this.hasActiveProcess(args.balanceId)) {
        this.#notifier.warning({
          title: 'Descarga en proceso',
          message: `${this.getNameOfTheItemToDownload()} ${
            args.balanceId
          } se está descargando, por favor espere a que termine la descarga.`,
        });

        return EMPTY;
      }

      this.addActiveProcess(args.balanceId);

      const infoNotifierId = this.#notifier.info({
        title: `Se está procesando la descarga de ${this.getNameOfTheItemToDownload()}; por favor espere un momento`,
      });

      return this.getRepository()
        .byId(this.fromUIToRepositoryRequest(args))
        .pipe(
          this.handleResponseAndDownload({
            date: args.date,
            merchantId: args.merchantId,
            balanceId: args.balanceId,
          }),

          tap(() => {
            this.#notifier.closeById(infoNotifierId);
            this.#notifier.success({
              title: 'Documento generado',
              message: `${this.getNameOfTheItemToDownload()} se ha generado correctamente y la descarga comenzará pronto`,
            });
          }),

          catchError(async err => {
            const text = await err?.error?.text();
            const errorDescriptor = text ? JSON.parse(text) : null;
            this.#notifier.closeById(infoNotifierId);

            if (
              err instanceof HttpErrorResponse &&
              (err.status === 400 || err.status === 404) &&
              (errorDescriptor?.error.endsWith('not found') ||
                errorDescriptor?.error.includes('not found'))
            ) {
              this.#notifier.warning({
                title: `Detectamos un error al descargar ${this.getNameOfTheItemToDownload()}`,
                message: `Hemos presentado un problema descargando ${this.getNameOfTheItemToDownload()}. Intenta más tarde.`,
              });

              throw new RuntimeMerchantError(
                `No se encontró ${this.getNameOfTheItemToDownload()} asociada al ID ${
                  args.balanceId
                }. Por favor, intente más tarde o contacte al soporte`,
                `${this.#getEntityNameIdentifier()}::execute::notFound`
              );
            }

            return this.#errorHandler.handle(err);
          }),

          finalize(() => {
            this.#processesQueue.delete(args.balanceId);
          })
        );
    } catch (error) {
      this.#processesQueue.delete(args.balanceId);
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }

  #getEntityNameIdentifier(): string {
    return this.constructor.name ?? 'DownloadOneBaseUsecase';
  }
}
