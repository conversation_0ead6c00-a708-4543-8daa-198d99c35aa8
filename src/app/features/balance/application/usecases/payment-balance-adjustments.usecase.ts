import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, of, take } from 'rxjs';
import { PaymentBalanceAdjustmentsRepository } from '../../domain/repositories/payment-balance-adjustments.repository';
import { IPaymentBalanceAdjustmentsParamsDto } from '../dtos/payment-balance-adjustments-params.dto';
import { IPaymentBalanceAdjustmentsResponseDto } from '../dtos/payment-balance-adjustments-response.dto';

export const defaultPaymentBalanceAdjustmentsResponse: IPaymentBalanceAdjustmentsResponseDto =
  {
    content: [],
    first: true,
    last: false,
    number: 0,
    numberOfElements: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
    hasContent: false,
  };

@Injectable()
export class PaymentBalanceAdjustmentUsecase
  implements
    BaseUsecase<
      IPaymentBalanceAdjustmentsParamsDto,
      Observable<IPaymentBalanceAdjustmentsResponseDto>
    >
{
  constructor(
    private repository: PaymentBalanceAdjustmentsRepository<
      IPaymentBalanceAdjustmentsParamsDto,
      Observable<IPaymentBalanceAdjustmentsResponseDto>
    >,
    private loader: LoaderService,
    private usecaseErrorHandler: UseCaseErrorHandler
  ) {}

  execute(
    args: IPaymentBalanceAdjustmentsParamsDto
  ): Observable<IPaymentBalanceAdjustmentsResponseDto> {
    const idLoader = this.loader.show();

    try {
      if (
        !Guard.againstNullOrUndefined(args, 'adjustmentBalanceId').succeeded
      ) {
        throw new RuntimeMerchantError(
          'El id del balance de pago no puede ser nulo o indefinido',
          'PaymentBalanceAdjustmentsUseCase::execute::InvalidBalanceId'
        );
      }

      Guard.againstInvalidNumbers(args?.pageNum);
      Guard.againstInvalidNumbers(args?.pageSize);
      Guard.againstInvalidNumbers(Number(args?.adjustmentBalanceId));

      if (args?.pageNum < 0) {
        throw new RuntimeMerchantError(
          'El número de página no puede ser menor a 0',
          'PaymentBalanceAdjustmentsUseCase::execute::InvalidPageNumber'
        );
      }

      if (args?.pageSize < 0) {
        throw new RuntimeMerchantError(
          'El número de elementos a mostrar no puede ser menor a 0',
          'PaymentBalanceAdjustmentsUseCase::execute::InvalidPageSize'
        );
      }
      if (Number(args?.adjustmentBalanceId) < 0) {
        throw new RuntimeMerchantError(
          'El ID de reporte no puede ser menor a 0',
          'PaymentBalanceAdjustmentsUseCase::execute::balanceIdLessThanZero'
        );
      }

      return this.repository.getList(args).pipe(
        catchError(error =>
          this.usecaseErrorHandler.handle(
            error,
            of(defaultPaymentBalanceAdjustmentsResponse)
          )
        ),
        take(1),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.loader.hide(idLoader);
      return this.usecaseErrorHandler.handle(
        error,
        of(defaultPaymentBalanceAdjustmentsResponse)
      );
    }
  }
}
