import { inject, Injectable } from '@angular/core';
import {
  RawDateDayFirst,
  RawYearMonthDateWithHyphen,
} from '@aplazo/merchant/shared';
import { FileSaverService } from '@aplazo/merchant/shared-dash';
import { MonoTypeOperatorFunction, pipe, tap } from 'rxjs';
import { DownloadAllRepository } from '../../domain/repositories/balance-download.repository';
import { PaymentBalanceInvoiceRepository } from '../../domain/repositories/payment-balance-invoice.repository';
import { DownloadAllBaseUsecase, DownloadAllUIRequest } from './download-all';

@Injectable()
export class PaymentBalanceAllInvoicesDownloadUsecase extends DownloadAllBaseUsecase {
  readonly #repository = inject(PaymentBalanceInvoiceRepository);
  readonly #fileSaver = inject(FileSaverService);

  getRepository(): DownloadAllRepository {
    return this.#repository;
  }

  handleResponseAndDownload(args: {
    date: RawDateDayFirst;
    merchantId: number;
  }): MonoTypeOperatorFunction<any> {
    return pipe(
      tap(response => {
        const filename = `[INVOICES]_M${args.merchantId}_${args.date}.zip`;

        this.#fileSaver.saveFile(response, filename);
      })
    );
  }

  fromUIToRepositoryRequest(args: DownloadAllUIRequest): {
    date: RawYearMonthDateWithHyphen;
  } {
    const yearAndMonth = args.date
      .split('/')
      .slice(1)
      .reverse()
      .join('-') as RawYearMonthDateWithHyphen;

    return {
      date: yearAndMonth,
    };
  }
  getNameOfTheItemsToDownload(): string {
    return 'Las facturas';
  }
}
