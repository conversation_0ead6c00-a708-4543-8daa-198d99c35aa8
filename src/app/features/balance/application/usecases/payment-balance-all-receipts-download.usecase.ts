import { inject, Injectable } from '@angular/core';
import {
  RawDateDayFirst,
  RawYearMonthDateWithHyphen,
} from '@aplazo/merchant/shared';
import { FileSaverService } from '@aplazo/merchant/shared-dash';
import { MonoTypeOperatorFunction, pipe, tap } from 'rxjs';
import { DownloadAllRepository } from '../../domain/repositories/balance-download.repository';
import { PaymentBalanceReceiptsRepository } from '../../domain/repositories/payment-balance-receipts.repository';
import { DownloadAllBaseUsecase, DownloadAllUIRequest } from './download-all';

@Injectable()
export class PaymentBalanceAllReceiptsDownloadUseCase extends DownloadAllBaseUsecase {
  readonly #repository = inject(PaymentBalanceReceiptsRepository);
  readonly #fileSaver = inject(FileSaverService);

  getRepository(): DownloadAllRepository {
    return this.#repository;
  }

  handleResponseAndDownload(args: {
    date: RawDateDayFirst;
    merchantId: number;
  }): MonoTypeOperatorFunction<any> {
    return pipe(
      tap(response => {
        const filename = `[RECEIPTS]_M${args.merchantId}_${args.date}.zip`;

        this.#fileSaver.saveFile(response, filename);
      })
    );
  }

  fromUIToRepositoryRequest(args: DownloadAllUIRequest): {
    date: RawYearMonthDateWithHyphen;
  } {
    const yearAndMonth = args.date
      .split('/')
      .slice(1)
      .reverse()
      .join('-') as RawYearMonthDateWithHyphen;

    return {
      date: yearAndMonth,
    };
  }
  getNameOfTheItemsToDownload(): string {
    return 'Los recibos de pago';
  }
}
