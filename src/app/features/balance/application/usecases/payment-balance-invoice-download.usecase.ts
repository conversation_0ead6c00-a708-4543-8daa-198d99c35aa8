import { inject, Injectable } from '@angular/core';
import { FileSaverService } from '@aplazo/merchant/shared-dash';
import { MonoTypeOperatorFunction, pipe, tap } from 'rxjs';
import { PaymentBalanceInvoiceRepository } from '../../domain/repositories/payment-balance-invoice.repository';
import { DownloadOneBaseUsecase, DownloadOneUIRequest } from './download-one';

@Injectable()
export class PaymentBalanceInvoiceDownloadByIdUseCase extends DownloadOneBaseUsecase {
  readonly #repository = inject(PaymentBalanceInvoiceRepository);
  readonly #fileSaver = inject(FileSaverService);

  getRepository() {
    return this.#repository;
  }

  fromUIToRepositoryRequest(args: DownloadOneUIRequest) {
    return {
      balanceId: args.balanceId,
    };
  }

  getNameOfTheItemToDownload(): string {
    return 'La factura';
  }

  handleResponseAndDownload(args: {
    merchantId: number;
    date: string;
    balanceId: number;
  }): MonoTypeOperatorFunction<any> {
    return pipe(
      tap(response => {
        const filename = `[INVOICE]_M${args.merchantId}_P${args.balanceId}_${args.date}.zip`;

        this.#fileSaver.saveFile(response, filename);
      })
    );
  }
}
