import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  DateCalculator,
  Guard,
  LoaderService,
  RawDateDayFirst,
  RawDateYearFirstWithHyphen,
  RawMonthYearDate,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, of, take } from 'rxjs';
import { TRANSACTION_STATUS } from '../../domain/entities/payment-balance.status';
import { PaymentBalanceListRepository } from '../../domain/repositories/payment-balance-list.repository';
import { IPaymentBalanceListRepositoryParamsDto } from '../dtos/payment-balance-list-repository-params.dto';
import { IPaymentBalanceListRequestUIDto } from '../dtos/payment-balance-list-request-ui.dto';
import { PaymentBalanceListResponseUIDto } from '../dtos/payment-balance-list-response-ui.dto';

export const responseWithEmptyContent: PaymentBalanceListResponseUIDto = {
  content: [],
  number: 0,
  size: 10,
  totalElements: 0,
  totalPages: 0,
  hasContent: false,
  numberOfElements: 0,
  first: true,
  last: true,
};

export const badInitialControlDateError =
  'La fecha inicial para consulta de balances de pago no es válida.';

export const badDynamicInitialDateToBalancesError =
  'La fecha dinámica para consulta de balances de pago no es válida.';

export const invalidStatusIdToRetrievePaymentBalanceListError =
  'Debe seleccionar un status válido para consultar el listado de balances de pago.';

export const notAllowToConsultPaymentBalanceListError =
  'El comercio esta en lista de espera para consultar el listado de balances de pago o la fecha solicitada no tiene información para el comercio.';

@Injectable()
export class PaymentBalanceListUsecase
  implements
    BaseUsecase<
      IPaymentBalanceListRequestUIDto,
      Observable<PaymentBalanceListResponseUIDto>
    >
{
  constructor(
    private repository: PaymentBalanceListRepository<
      IPaymentBalanceListRepositoryParamsDto,
      Observable<PaymentBalanceListResponseUIDto>
    >,
    private loader: LoaderService,
    private dateCalculator: DateCalculator,
    private temporalService: TemporalService,
    private usecaseError: UseCaseErrorHandler
  ) {}

  execute(
    args: IPaymentBalanceListRequestUIDto
  ): Observable<PaymentBalanceListResponseUIDto> {
    const idLoader = this.loader.show();

    try {
      this.#validateRawDate(args.date);

      const initialDate = this.#validateInitialDate(args.initialDateToBalances);

      const dynamicInitialDate = this.#validateRawDynamicDate(
        args.dynamicInitialDateToBalances
      );

      this.#validateRequestedDateAgainstInitialAndDynamic(
        args.date,
        initialDate,
        dynamicInitialDate
      );

      if (TRANSACTION_STATUS[args.status] == null) {
        throw new RuntimeMerchantError(
          invalidStatusIdToRetrievePaymentBalanceListError,
          'PaymentBalanceListUsecase::invalidStatus',
          'get-balance-list-error'
        );
      }

      const monthYear = args.date
        .split('/')
        .splice(1)
        .join('/') as RawMonthYearDate;

      Guard.againstInvalidNumbers(args.pageNum, 'payment-balance-list-usecase');
      Guard.againstInvalidNumbers(
        args.pageSize,
        'payment-balance-list-usecase'
      );

      return this.repository
        .getList({
          status: TRANSACTION_STATUS[args.status],
          monthYear,
          pageNum: args.pageNum,
          pageSize: args.pageSize,
        })
        .pipe(
          take(1),
          catchError(error => {
            return this.usecaseError.handle(
              error,
              of(responseWithEmptyContent)
            );
          }),
          finalize(() => this.loader.hide(idLoader))
        );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.usecaseError.handle(error, of(responseWithEmptyContent));
    }
  }

  #validateRawDate(date: RawDateDayFirst): void | never {
    if (
      !Guard.againstNullOrUndefined({ date }, 'date', 'get-balance-list-error')
        .succeeded
    ) {
      throw new RuntimeMerchantError(
        'La fecha es nula o indefinida',
        'PaymentBalanceListUsecase::date::invalidDate',
        'get-balance-list-error'
      );
    }

    if (!Guard.againstInvalidRawDateDayFirst(date).succeeded) {
      throw new RuntimeMerchantError(
        notAllowToConsultPaymentBalanceListError,
        'PaymentBalanceListUsecase::date::invalidRawDate',
        'get-balance-list-error'
      );
    }
  }

  #validateInitialDate(date: Date | undefined): Date {
    if (
      !Guard.againstNullOrUndefined({ date }, 'date', 'get-balance-list-error')
        .succeeded
    ) {
      throw new RuntimeMerchantError(
        'La fecha es nula o indefinida',
        'PaymentBalanceListUsecase::initialDate::notDefined',
        'get-balance-list-error'
      );
    }

    Guard.againstInvalidDate({
      date: date,
      origin: 'get-balance-list-error',
      errorMessage: badInitialControlDateError,
    });

    const isTodayBeforeInitialDate =
      !this.dateCalculator.isSameOrAfterControlDate({
        baseDate: new Date(),
        date: date,
      });

    if (isTodayBeforeInitialDate) {
      throw new RuntimeMerchantError(
        notAllowToConsultPaymentBalanceListError,
        'PaymentBalanceListUsecase::todayBefore::outdateToGetBalances',
        'get-balance-list-error'
      );
    }

    return date as Date;
  }

  #validateRawDynamicDate(date: RawDateYearFirstWithHyphen): Date {
    if (!date) {
      throw new RuntimeMerchantError(
        notAllowToConsultPaymentBalanceListError,
        'PaymentBalanceListUsecase::dynamicInitial::outdateToGetBalances',
        'get-balance-list-error'
      );
    }

    if (!date.includes('-')) {
      throw new RuntimeMerchantError(
        badDynamicInitialDateToBalancesError,
        'PaymentBalanceListUsecase::dynamicInitial::invalidDate',
        'get-balance-list-error'
      );
    }

    return this.temporalService.fromStringToDate(date);
  }

  #validateRequestedDateAgainstInitialAndDynamic(
    requestedDate: RawDateDayFirst,
    initialDate: Date,
    dynamicInitialDate: Date
  ): void {
    const isTodayBeforeDynamicDate =
      !this.dateCalculator.isSameOrAfterControlDate({
        baseDate: new Date(),
        date: dynamicInitialDate,
      });

    if (isTodayBeforeDynamicDate) {
      throw new RuntimeMerchantError(
        notAllowToConsultPaymentBalanceListError,
        'PaymentBalanceListUsecase::todayBeforeDynamic::outdateToGetBalances',
        'get-balance-list-error'
      );
    }

    const formatedDate = this.temporalService.fromStringToDate(requestedDate);

    const isRequestedDateSameMonthInitialDate =
      this.dateCalculator.isSameOrAfterMonth({
        baseDate: formatedDate,
        date: initialDate,
      });

    if (!isRequestedDateSameMonthInitialDate) {
      throw new RuntimeMerchantError(
        notAllowToConsultPaymentBalanceListError,
        'PaymentBalanceListUsecase::beforeInitialMonth::outdateToGetBalances',
        'get-balance-list-error'
      );
    }

    const isRequestedDateSameMonthThanDynamicDate =
      this.dateCalculator.isSameOrAfterMonth({
        baseDate: formatedDate,
        date: dynamicInitialDate,
      });

    if (!isRequestedDateSameMonthThanDynamicDate) {
      throw new RuntimeMerchantError(
        notAllowToConsultPaymentBalanceListError,
        'PaymentBalanceListUsecase::beforeDynamicMonth::outdateToGetBalances',
        'get-balance-list-error'
      );
    }
  }
}
