import { inject, Injectable } from '@angular/core';
import { FileSaverService } from '@aplazo/merchant/shared-dash';
import { MonoTypeOperatorFunction, pipe, tap } from 'rxjs';
import { PaymentBalanceReceiptsRepository } from '../../domain/repositories/payment-balance-receipts.repository';
import { DownloadOneBaseUsecase, DownloadOneUIRequest } from './download-one';

@Injectable()
export class PaymentBalanceReceiptDownloadUseCase extends DownloadOneBaseUsecase {
  readonly #repository = inject(PaymentBalanceReceiptsRepository);
  readonly #fileSaver = inject(FileSaverService);

  getRepository() {
    return this.#repository;
  }

  fromUIToRepositoryRequest(args: DownloadOneUIRequest) {
    return {
      balanceId: args.balanceId,
    };
  }

  getNameOfTheItemToDownload(): string {
    return 'El recibo de pago';
  }

  handleResponseAndDownload(args: {
    merchantId: number;
    date: string;
    balanceId: number;
  }): MonoTypeOperatorFunction<any> {
    return pipe(
      tap(response => {
        const filename = `[RECEIPT]_M${args.merchantId}_P${args.balanceId}_${args.date}.pdf`;

        this.#fileSaver.saveFile(response, filename);
      })
    );
  }
}
