import { inject, Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import { defer, map, MonoTypeOperatorFunction, pipe, switchMap } from 'rxjs';
import { PaymentBalanceTransctionsRepository } from '../../domain/repositories/payment-balance-transactions.repository';
import { DownloadOneBaseUsecase, DownloadOneUIRequest } from './download-one';

export const emptyReportIdDefaultMsg =
  'La descarga del reporte no se pudo realizar debido a que no se ha especificado un ID de reporte';
export const notValidNumberReportIdDefaultMsg =
  'La descarga del reporte no se pudo realizar debido a que se requiere un número entero positivo para el ID de reporte';

@Injectable()
export class PaymentBalanceReportUseCase extends DownloadOneBaseUsecase {
  readonly #repository = inject(PaymentBalanceTransctionsRepository);
  readonly #fileGenerator = inject(FileGeneratorService<Promise<string[][]>>);
  readonly #reportResponseTransformService = inject(CsvMapperService);

  getRepository() {
    return this.#repository;
  }
  fromUIToRepositoryRequest(args: DownloadOneUIRequest) {
    return {
      balanceId: args.balanceId,
    };
  }
  getNameOfTheItemToDownload(): string {
    return 'El reporte de transacciones';
  }
  handleResponseAndDownload(args: {
    merchantId: number;
    date: string;
    balanceId: number;
  }): MonoTypeOperatorFunction<any> {
    return pipe(
      map(response => response ?? ''),
      map(response => this.#reportResponseTransformService.transform(response)),

      switchMap(content => {
        if (!content || content.length === 0) {
          throw new RuntimeMerchantError(
            'No existen datos para el rango de fechas seleccionado',
            'DownloadOneBaseUsecase::execute::emptyReport'
          );
        }

        const filename = `[TRANSACTION]_M${args.merchantId}_${args.date}`;

        return defer(() =>
          this.#fileGenerator.generateFileAndDownload(content, filename)
        );
      })
    );
  }
}
