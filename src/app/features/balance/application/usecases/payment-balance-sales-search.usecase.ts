import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RawLoanStatus,
  RuntimeMerchantError,
  Transaction,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { EMPTY, Observable, catchError, finalize, of, take } from 'rxjs';
import { PaymentBalanceSalesSearchRepository } from '../../domain/repositories/payment-balance-sales-search.repository';
import { IPaymentBalanceSalesSearchRepositoryParams } from '../dtos/payment-balance-sales-search-repository-params.dto';
import { IPaymentBalanceSalesSearchRequestUiDto } from '../dtos/payment-balance-sales-search-request-ui.dto';

export const invalidRawDateBalanceSalesSearchErrorMessage =
  'La fecha no se encuentra en un formato válido para la búsqueda.';
export const emptyDefaultResponseBalanceSalesSearch =
  [] satisfies Transaction[];

@Injectable()
export class PaymentBalanceSalesSearchUsecase
  implements
    BaseUsecase<
      IPaymentBalanceSalesSearchRequestUiDto,
      Observable<Transaction[]>
    >
{
  constructor(
    private repository: PaymentBalanceSalesSearchRepository<
      IPaymentBalanceSalesSearchRepositoryParams,
      Observable<Transaction[]>
    >,
    private loader: LoaderService,
    private usecaseError: UseCaseErrorHandler
  ) {}

  execute(
    args: IPaymentBalanceSalesSearchRequestUiDto
  ): Observable<Transaction[]> {
    const idLoader = this.loader.show();

    try {
      if (!args) {
        throw new RuntimeMerchantError(
          'No se recibieron datos para la búsqueda',
          'PaymentBalanceSalesSearchUsecase::noDataReceived',
          'payment-balance-sales-search-usecase'
        );
      }

      if (
        !Object.hasOwn(args, 'dateRange') ||
        !Guard.againstNullOrUndefined(args.dateRange, 'startDate').succeeded ||
        !Guard.againstNullOrUndefined(args.dateRange, 'endDate').succeeded
      ) {
        throw new RuntimeMerchantError(
          'La fecha de inicio y fin son requeridas para la búsqueda',
          'PaymentBalanceSalesSearchUsecase::someDateMissing'
        );
      }

      if (
        !Guard.againstInvalidRawDateDayFirst(args.dateRange.startDate)
          .succeeded ||
        !Guard.againstInvalidRawDateDayFirst(args.dateRange.endDate).succeeded
      ) {
        throw new RuntimeMerchantError(
          invalidRawDateBalanceSalesSearchErrorMessage,
          'PaymentBalanceSalesSearchUsecase::invalidDateRange',
          'payment-balance-sales-search-usecase'
        );
      }

      const status = RawLoanStatus.create('all').status;

      const request = {
        dateRange: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
        },
        search: args.search,
        status,
      } satisfies IPaymentBalanceSalesSearchRepositoryParams;

      return this.repository.search(request).pipe(
        catchError(err =>
          this.usecaseError.handle(
            err,
            of(emptyDefaultResponseBalanceSalesSearch)
          )
        ),
        finalize(() => this.loader.hide(idLoader)),
        take(1)
      );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.usecaseError.handle(error, EMPTY);
    }
  }
}
