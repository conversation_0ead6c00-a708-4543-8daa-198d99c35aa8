import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { EMPTY, Observable, catchError, finalize, of, take } from 'rxjs';
import { PaymentBalanceSalesRepository } from '../../domain/repositories/payment-balance-sales.repository';
import { PaymentBalanceSalesUIDto } from '../dtos/payment-balance-sales-params.dto';
import { IPaymentBalanceSalesResponseDto } from '../dtos/payment-balance-sales-response.dto';

export const defaultBalanceSalesResponse = {
  content: [],
  first: false,
  hasContent: false,
  last: false,
  numberOfElements: 0,
  number: 0,
  size: 0,
  totalElements: 0,
  totalPages: 0,
} satisfies IPaymentBalanceSalesResponseDto;

@Injectable()
export class PaymentBalanceSalesUsecase
  implements
    BaseUsecase<
      PaymentBalanceSalesUIDto,
      Observable<IPaymentBalanceSalesResponseDto>
    >
{
  constructor(
    private repository: PaymentBalanceSalesRepository<
      PaymentBalanceSalesUIDto,
      Observable<IPaymentBalanceSalesResponseDto>
    >,
    private loader: LoaderService,
    private usecaseError: UseCaseErrorHandler
  ) {}

  execute(
    args: PaymentBalanceSalesUIDto
  ): Observable<IPaymentBalanceSalesResponseDto> {
    const idLoader = this.loader.show();

    try {
      const errors = ['salesBalanceId', 'pageNum', 'pageSize']
        .map(key => {
          const result = Guard.againstNullOrUndefined(args, key);

          return result.succeeded ? null : result.message;
        })
        .filter(Boolean);

      if (errors.length > 0) {
        throw new RuntimeMerchantError(
          `Los siguientes campos son requeridos: ${errors.join(', ')}`,
          'PaymentBalanceSalesUsecase::execute::emptyFields'
        );
      }

      Guard.againstInvalidNumbers(
        args?.pageNum,
        'payment-balance-sales-usecase',
        'El número de página debe ser un número entero positivo'
      );
      Guard.againstInvalidNumbers(
        args?.pageSize,
        'payment-balance-sales-usecase',
        'El número de páginas debe ser un número entero positivo'
      );

      return this.repository.getList(args).pipe(
        take(1),
        catchError(error =>
          this.usecaseError.handle(error, of(defaultBalanceSalesResponse))
        ),
        finalize(() => this.loader.hide(idLoader))
      );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.usecaseError.handle(error, EMPTY);
    }
  }
}
