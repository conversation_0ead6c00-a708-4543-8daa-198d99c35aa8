import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { EMPTY, Observable, catchError, finalize, map, of, take } from 'rxjs';
import { PaymentBalance } from '../../domain/entities/payment-balance';
import { PaymentBalanceSearchRepository } from '../../domain/repositories/payment-balance-search.repository';

export const defaultEmptySearchResponse = [];

@Injectable({ providedIn: 'any' })
export class PaymentBalanceSearchUsecase
  implements BaseUsecase<number, Observable<PaymentBalance[]>>
{
  /** decimal regExp pattern from https://www.geeksforgeeks.org/how-to-validate-decimal-numbers-in-javascript/ */
  readonly #decimalPattern = /^\d*\.\d+$/;

  constructor(
    private repository: PaymentBalanceSearchRepository<
      Observable<PaymentBalance>
    >,
    private loader: LoaderService,
    private usecaseError: UseCaseErrorHandler
  ) {}

  execute(id: number): Observable<PaymentBalance[]> {
    const idLoader = this.loader.show();

    try {
      Guard.againstInvalidNumbers(
        id,
        'PaymentBalanceSearchUsecase::execute::emptyId',
        'El ID debe ser un número'
      );

      if (id < 0) {
        throw new RuntimeMerchantError(
          'El ID debe ser un número positivo',
          'PaymentBalanceSearchUsecase::execute::negativeId'
        );
      }

      if (String(id).match(this.#decimalPattern) != null) {
        throw new RuntimeMerchantError(
          'El ID debe ser un número entero positivo',
          'PaymentBalanceSearchUsecase::execute::decimalId'
        );
      }

      return this.repository.getPaymentById(id).pipe(
        map(response => {
          if (!response) {
            return defaultEmptySearchResponse;
          }
          return [response];
        }),

        catchError(error =>
          this.usecaseError.handle(error, of(defaultEmptySearchResponse))
        ),

        finalize(() => this.loader.hide(idLoader)),

        take(1)
      );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.usecaseError.handle(error, EMPTY);
    }
  }
}
