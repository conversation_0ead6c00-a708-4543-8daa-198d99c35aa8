import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  DateCalculator,
  Guard,
  LoaderService,
  RawDateYearFirstWithHyphen,
  RawMonthYearDate,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import { PaymentBalanceStats } from '../../domain/entities/payment-balance-stats';
import { TRANSACTION_STATUS } from '../../domain/entities/payment-balance.status';
import { PaymentBalanceStatsRepository } from '../../domain/repositories/payment-balance-stats.repository';
import { IPaymentBalanceStatsRepositoryParamsDto } from '../dtos/payment-balance-stats-repository-params.dto';
import { IPaymentBalanceStatsRequestUIDto } from '../dtos/payment-balance-stats-request.dto';

export const emptyBalanceStats: PaymentBalanceStats = {
  totalSales: 0,
  totalAdjustment: 0,
  totalPay: 0,
  totalFee: 0,
};

export const badInitialControlDateToBalancesStatsError =
  'La fecha inicial para consulta de estadísticas no es válida.';

export const badDynamicInitialDateToBalancesStatsError =
  'La fecha dinámica para consulta de estadísticas no es válida.';

export const notAllowToGetStatsError =
  'El comercio esta en lista de espera para consultar estadísticas de los balances de pago.';

export const genericNotificationTitle =
  'Hemos detectado un error al consultar las estadísticas de los balances de pago';

@Injectable()
export class PaymentBalanceStatsUseCase
  implements
    BaseUsecase<
      IPaymentBalanceStatsRequestUIDto,
      Observable<PaymentBalanceStats>
    >
{
  private readonly defaultStatsResponse = emptyBalanceStats;

  constructor(
    private repository: PaymentBalanceStatsRepository<
      IPaymentBalanceStatsRepositoryParamsDto,
      Observable<PaymentBalanceStats>
    >,
    private loader: LoaderService,
    private dateCalculator: DateCalculator,
    private usecaseError: UseCaseErrorHandler
  ) {}

  execute(
    args: IPaymentBalanceStatsRequestUIDto
  ): Observable<PaymentBalanceStats> {
    const idLoader = this.loader.show();

    try {
      this.runDateValidationForBeforeDates(
        args?.initialDateToBalances,
        new Date(),
        badInitialControlDateToBalancesStatsError
      );

      if (args?.dynamicInitialDateToBalances == null) {
        throw new RuntimeMerchantError(
          notAllowToGetStatsError,
          'PaymentBalanceStatsUsecase::outdateToGetStats::emptyDynamicDate',
          'get-balance-stats-error'
        );
      }

      const dynamicInitialDate = this.fromStringDateToDate(
        args?.dynamicInitialDateToBalances
      );

      this.runDateValidationForBeforeDates(
        dynamicInitialDate,
        new Date(),
        badDynamicInitialDateToBalancesStatsError
      );

      if (!Guard.againstInvalidRawDateDayFirst(args.date).succeeded) {
        throw new RuntimeMerchantError(
          notAllowToGetStatsError,
          'PaymentBalanceStatsUsecase::invalidDate::requestedDate',
          'get-balance-stats-error'
        );
      }

      const mappedDate = args.date.split('/').reverse().join('/');
      const formatedDate = new Date(mappedDate);

      this.runDateValidationByMonths(
        args.initialDateToBalances,
        formatedDate,
        notAllowToGetStatsError,
        'initialDateToBalances'
      );

      this.runDateValidationByMonths(
        dynamicInitialDate,
        formatedDate,
        notAllowToGetStatsError,
        'dynamicInitialDateToBalances'
      );

      if (TRANSACTION_STATUS[args.status] == null) {
        throw new RuntimeMerchantError(
          notAllowToGetStatsError,
          'PaymentBalanceStatsUsecase::invalidStatus',
          'get-balance-stats-error'
        );
      }

      const monthYear = args.date
        .split('/')
        .splice(1)
        .join('/') as RawMonthYearDate;

      return this.repository
        .getStats({
          status: TRANSACTION_STATUS[args.status],
          monthYear,
        })
        .pipe(
          map(stats => stats ?? this.defaultStatsResponse),
          catchError(error =>
            this.usecaseError.handle(error, of(this.defaultStatsResponse))
          ),
          finalize(() => {
            this.loader.hide(idLoader);
          }),
          take(1)
        );
    } catch (error) {
      this.loader.hide(idLoader);
      return this.usecaseError.handle(error, of(this.defaultStatsResponse));
    }
  }

  private runDateValidationForBeforeDates(
    date: Date | null | undefined,
    dateToCompare: Date,
    errorMessage?: string
  ): void | never {
    const origin = 'get-balance-stats-error';

    Guard.againstInvalidDate({
      date,
      origin,
      errorMessage,
    });

    const isTodayBeforeDate = !this.dateCalculator.isSameOrAfterControlDate({
      baseDate: dateToCompare,
      date: date as Date,
    });

    if (isTodayBeforeDate) {
      throw new RuntimeMerchantError(
        notAllowToGetStatsError,
        'PaymentBalanceStatsUsecase::outdateToGetStats::beforeInitialDate',
        origin
      );
    }
  }
  private runDateValidationByMonths(
    date: Date | null | undefined,
    dateToCompare: Date,
    errorMessage?: string,
    diferenciator?: string | null
  ): void | never {
    const origin = 'get-balance-stats-error';
    Guard.againstInvalidDate({
      date,
      origin,
      errorMessage,
    });

    const isDateSameOrAfter = this.dateCalculator.isSameOrAfterMonth({
      baseDate: dateToCompare,
      date: date as Date,
    });

    if (!isDateSameOrAfter) {
      throw new RuntimeMerchantError(
        errorMessage ?? notAllowToGetStatsError,
        'PaymentBalanceStatsUsecase::outdateToGetStats' +
          (diferenciator ? `::${diferenciator}` : ''),
        origin
      );
    }
  }
  private fromStringDateToDate(date: RawDateYearFirstWithHyphen): Date {
    const dynamicDate = date?.split('-') ?? [];

    if (dynamicDate.length !== 3) {
      throw new RuntimeMerchantError(
        badDynamicInitialDateToBalancesStatsError,
        'PaymentBalanceStatsUsecase::invalidDynamicDate',
        'get-balance-stats-error'
      );
    }

    const dynamicYear = Number(dynamicDate[0]);
    const dynamicMonth = Number(dynamicDate[1]) - 1;
    const dynamicDay = Number(dynamicDate[2]);

    const dynamicInitialDate = new Date(dynamicYear, dynamicMonth, dynamicDay);

    return dynamicInitialDate;
  }
}
