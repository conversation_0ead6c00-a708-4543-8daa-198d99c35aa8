import { inject, Injectable } from '@angular/core';
import {
  RawDateDayFirst,
  RawMonthYearDate,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import { map, MonoTypeOperatorFunction, pipe, tap } from 'rxjs';
import {
  TRANSACTION_STATUS,
  TransactionStatus,
} from '../../domain/entities/payment-balance.status';
import { DownloadAllRepository } from '../../domain/repositories/balance-download.repository';
import { PaymentBalanceTransctionsRepository } from '../../domain/repositories/payment-balance-transactions.repository';
import { DownloadAllBaseUsecase, DownloadAllUIRequest } from './download-all';

@Injectable()
export class PaymentBalanceSummaryReportUsecase extends DownloadAllBaseUsecase {
  readonly #repository = inject(PaymentBalanceTransctionsRepository);
  readonly #csvTrasnformer = inject(CsvMapperService);
  readonly #generateFile = inject(FileGeneratorService);

  getRepository(): DownloadAllRepository {
    return this.#repository;
  }
  handleResponseAndDownload(args: {
    date: RawDateDayFirst;
    merchantId: number;
  }): MonoTypeOperatorFunction<any> {
    return pipe(
      map(response => response ?? ''),
      map(response => this.#csvTrasnformer.transform(response)),
      tap(content => {
        if (content == null || content.length === 0) {
          throw new RuntimeMerchantError(
            'No existen datos para el rango de fechas seleccionado',
            'DownloadAllBaseUsecase::EmptyReport'
          );
        }

        const filename = `[TRANSACTIONS]_M${args.merchantId}_${args.date}`;
        this.#generateFile.generateFileAndDownload(content, filename);
      })
    );
  }
  fromUIToRepositoryRequest(args: DownloadAllUIRequest): {
    status: TransactionStatus;
    date: RawMonthYearDate;
  } {
    const monthYear = args.date
      .split('/')
      .splice(1)
      .join('/') as RawMonthYearDate;

    const status = TRANSACTION_STATUS[args.status];

    return {
      status,
      date: monthYear,
    };
  }
  getNameOfTheItemsToDownload(): string {
    return 'Las transacciones';
  }
}
