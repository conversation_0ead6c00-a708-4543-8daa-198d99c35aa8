import { Route } from '@angular/router';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { hasSelectedBalance } from './guards/has-selected-balance.guard';
import { getDynamicConfig } from './resolvers/dynamic-config.resolver';
import { CriteriaBalanceDetailsService } from './services/criteria-balance-details.service';
import { CriteriaBalanceService } from './services/criteria-balance.service';

export default [
  {
    path: '',
    providers: [CriteriaBalanceService, CriteriaBalanceDetailsService],
    resolve: { dynamicBalanceConfig: getDynamicConfig },
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/balance/balance.component').then(
            stl => stl.BalanceComponent
          ),
      },
      {
        path: DASH_ROUTES.balanceDetails,
        canActivate: [hasSelectedBalance],
        loadComponent: () =>
          import('./pages/balance-details/balance-details.component').then(
            stl => stl.BalanceDetailsComponent
          ),
      },
    ],
  },
] satisfies Route[];
