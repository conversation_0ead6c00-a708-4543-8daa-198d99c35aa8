import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { PaymentBalanceAdjustmentUsecase } from '../../application/usecases/payment-balance-adjustments.usecase';
import { PaymentBalanceAllInvoicesDownloadUsecase } from '../../application/usecases/payment-balance-all-invoices-download.usecase';
import { PaymentBalanceAllReceiptsDownloadUseCase } from '../../application/usecases/payment-balance-all-receipts-download.usecase';
import { PaymentBalanceInvoiceDownloadByIdUseCase } from '../../application/usecases/payment-balance-invoice-download.usecase';
import { PaymentBalanceListUsecase } from '../../application/usecases/payment-balance-list.usecase';
import { PaymentBalanceReceiptDownloadUseCase } from '../../application/usecases/payment-balance-receipt-download.usecase';
import { PaymentBalanceReportUseCase } from '../../application/usecases/payment-balance-report.usecase';
import { PaymentBalanceSalesSearchUsecase } from '../../application/usecases/payment-balance-sales-search.usecase';
import { PaymentBalanceSalesUsecase } from '../../application/usecases/payment-balance-sales.usecase';
import { PaymentBalanceSearchUsecase } from '../../application/usecases/payment-balance-search.usecase';
import { PaymentBalanceStatsUseCase } from '../../application/usecases/payment-balance-stats.usecase';
import { PaymentBalanceSummaryReportUsecase } from '../../application/usecases/payment-balance-summary-report.usecase';
import { PaymentBalanceAdjustmentsRepository } from '../../domain/repositories/payment-balance-adjustments.repository';
import { PaymentBalanceDynamicConfigRepository } from '../../domain/repositories/payment-balance-dynamic-config.repository';
import { PaymentBalanceInvoiceRepository } from '../../domain/repositories/payment-balance-invoice.repository';
import { PaymentBalanceListRepository } from '../../domain/repositories/payment-balance-list.repository';
import { PaymentBalanceReceiptsRepository } from '../../domain/repositories/payment-balance-receipts.repository';
import { PaymentBalanceSalesSearchRepository } from '../../domain/repositories/payment-balance-sales-search.repository';
import { PaymentBalanceSalesRepository } from '../../domain/repositories/payment-balance-sales.repository';
import { PaymentBalanceSearchRepository } from '../../domain/repositories/payment-balance-search.repository';
import { PaymentBalanceStatsRepository } from '../../domain/repositories/payment-balance-stats.repository';
import { PaymentBalanceTransctionsRepository } from '../../domain/repositories/payment-balance-transactions.repository';
import { PaymentBalanceAdjustmentsRepositoryImpl } from '../repositories/payment-balance-adjustments-impl.repository';
import { PaymentBalanceDynamicConfigRepositoryImp } from '../repositories/payment-balance-dynamic-config-impl.repository';
import { PaymentBalanceInvoiceWithHttp } from '../repositories/payment-balance-invoice-with-http.repository';
import { PaymentBalanceListRepositoryImpl } from '../repositories/payment-balance-list-impl.repository';
import { PaymentBalanceReceiptsWithHttp } from '../repositories/payment-balance-receipts-with-http.repository';
import { PaymentBalanceSalesRepositoryImpl } from '../repositories/payment-balance-sales-impl.repository';
import { PaymentBalanceSalesSearchRepositoryImpl } from '../repositories/payment-balance-sales-search-impl.repository';
import { PaymentBalanceSearchRepositoryImpl } from '../repositories/payment-balance-search-imp.repository';
import { PaymentBalanceStatsRepositoryImpl } from '../repositories/payment-balance-stats.repository';
import { PaymentBalanceTransactionsWithHttp } from '../repositories/payment-balance-transactions-with-http.repository';

export function provideBalanceTools(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: PaymentBalanceAdjustmentsRepository,
      useClass: PaymentBalanceAdjustmentsRepositoryImpl,
    },
    {
      provide: PaymentBalanceDynamicConfigRepository,
      useClass: PaymentBalanceDynamicConfigRepositoryImp,
    },
    {
      provide: PaymentBalanceListRepository,
      useClass: PaymentBalanceListRepositoryImpl,
    },
    {
      provide: PaymentBalanceSalesRepository,
      useClass: PaymentBalanceSalesRepositoryImpl,
    },
    {
      provide: PaymentBalanceSalesSearchRepository,
      useClass: PaymentBalanceSalesSearchRepositoryImpl,
    },
    {
      provide: PaymentBalanceSearchRepository,
      useClass: PaymentBalanceSearchRepositoryImpl,
    },
    {
      provide: PaymentBalanceStatsRepository,
      useClass: PaymentBalanceStatsRepositoryImpl,
    },
    {
      provide: PaymentBalanceTransctionsRepository,
      useClass: PaymentBalanceTransactionsWithHttp,
    },
    {
      provide: PaymentBalanceReceiptsRepository,
      useClass: PaymentBalanceReceiptsWithHttp,
    },
    {
      provide: PaymentBalanceInvoiceRepository,
      useClass: PaymentBalanceInvoiceWithHttp,
    },
    PaymentBalanceReceiptDownloadUseCase,
    PaymentBalanceAdjustmentUsecase,
    PaymentBalanceListUsecase,
    PaymentBalanceAllReceiptsDownloadUseCase,
    PaymentBalanceSearchUsecase,
    PaymentBalanceReportUseCase,
    PaymentBalanceSalesSearchUsecase,
    PaymentBalanceSalesUsecase,
    PaymentBalanceInvoiceDownloadByIdUseCase,
    PaymentBalanceStatsUseCase,
    PaymentBalanceSummaryReportUsecase,
    PaymentBalanceAllInvoicesDownloadUsecase,
  ]);
}
