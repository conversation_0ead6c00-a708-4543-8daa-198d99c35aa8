import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map, take } from 'rxjs';
import { DASH_ROUTES } from '../../../../config/app-route-core';
import { CriteriaBalanceDetailsService } from '../../infra/services/criteria-balance-details.service';

export const hasSelectedBalance: CanActivateFn = () => {
  const criteria = inject(CriteriaBalanceDetailsService);
  const router = inject(Router);

  return criteria.hasSelectedBalance$().pipe(
    map(hasSelected => {
      if (!hasSelected) {
        return router.parseUrl(
          '/' + [DASH_ROUTES.rootApp, DASH_ROUTES.balance].join('/')
        );
      }

      return true;
    }),
    take(1)
  );
};
