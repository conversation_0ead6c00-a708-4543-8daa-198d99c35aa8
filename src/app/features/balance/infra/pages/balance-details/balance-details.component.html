@if (vm$ | async; as context) {
  <div class="min-h-fit pb-20">
    <section
      class="px-4 md:px-8 flex justify-center md:justify-between items-end">
      @if (context.balanceActionsTextTemplate; as balanceActionsTextTemplate) {
        <div>
          <button
            aplzButton
            aplzAppearance="basic"
            aplzColor="light"
            size="md"
            (click)="goBackRoute()">
            <aplz-ui-icon name="arrow-left" size="sm"></aplz-ui-icon>
            <span>
              {{ balanceActionsTextTemplate.backButtonLabel }}
            </span>
          </button>
          <h2 class="text-lg mt-2 font-medium">
            <span class="me-2">
              {{ balanceActionsTextTemplate.title }}
            </span>
            {{ context.selectedBalanceId }}
          </h2>
        </div>
        @if (context.downloadReceiptByIdTextUI; as downloadReceipt) {
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="sm"
            class="font-semibold"
            [rounded]="true"
            [aplzDropdownTriggerFor]="downloadOptions">
            <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
          </button>
          <aplz-ui-dropdown #downloadOptions>
            <aplz-ui-dropdown-item>
              <button class="px-4 py-2" (click)="downloadReportByReportId()">
                {{ downloadReceipt.paymentsLabel }}
              </button>
            </aplz-ui-dropdown-item>
            <aplz-ui-dropdown-item>
              <button class="px-4 py-2" (click)="downloadReceiptByReportId()">
                {{ downloadReceipt.receiptsLabel }}
              </button>
            </aplz-ui-dropdown-item>
          </aplz-ui-dropdown>
        } @else {
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="sm"
            [rounded]="true"
            (click)="downloadReportByReportId()">
            <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
            <span> Descargar </span>
          </button>
        }
      }
    </section>

    <section class="pt-4 px-4 md:px-8">
      <aplz-ui-stats
        [stats]="context.balanceStats"
        appearence="shadow"
        [columnRule]="true"></aplz-ui-stats>
    </section>

    <section class="px-4 md:px-8 mt-12">
      <h3 class="text-lg my-2 font-medium">
        {{ context.balanceSalesTitleTextTemplate.title }}
      </h3>

      @if (context.hasSales) {
        <div
          class="bg-light pt-2 pb-4 rounded-lg shadow-md max-h-96 overflow-auto">
          <table aplzSimpleTable aria-label="Balance Sales List">
            <tr aplzSimpleTableHeaderRow>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceSalesTextTemplate.id }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceSalesTextTemplate.date }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col">
                {{ context.balanceSalesTextTemplate.sales }}
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="max-w-44 text-pretty text-center">
                {{ context.balanceSalesTextTemplate.feeRevenue }}
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="max-w-44 text-center text-balance">
                {{ context.balanceSalesTextTemplate.payMerchant }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceSalesTextTemplate.status }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceSalesTextTemplate.cartId }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceSalesTextTemplate.branch }}
              </th>
            </tr>

            @for (item of sales$ | async; track item) {
              <tr aplzSimpleTableBodyRow>
                <td aplzSimpleTableBodyCell class="text-center font-semibold">
                  {{ item.id }}
                </td>
                <td aplzSimpleTableBodyCell class="text-center">
                  <p>
                    {{ item.creationDate | aplzDynamicPipe: 'date' }}
                  </p>
                </td>
                <td aplzSimpleTableBodyCell class="tabular-nums text-right">
                  <p class="pe-2">
                    {{ item.salesAmount | aplzDynamicPipe: 'currency' }}
                  </p>
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="max-w-44 tabular-nums text-right">
                  <p class="pe-4">
                    {{ item.feeRevenue | aplzDynamicPipe: 'currency' }}
                  </p>
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="max-w-44 tabular-nums text-right">
                  <p class="pe-4">
                    {{ item.payMerchantAmount | aplzDynamicPipe: 'currency' }}
                  </p>
                </td>
                <td aplzSimpleTableBodyCell class="text-center">
                  <span
                    class="font-semibold inline-block px-5 py-3 rounded-lg whitespace-nowrap"
                    [class.bg-special-success]="
                      item.status.toLowerCase() === 'aprobado'
                    "
                    [class.bg-special-warning]="
                      ['cancelado', 'no completado'].includes(
                        item.status.toLowerCase()
                      )
                    ">
                    {{ item.status }}
                  </span>
                </td>
                <td aplzSimpleTableBodyCell class="text-center">
                  {{ item.cartId }}
                </td>
                <td aplzSimpleTableBodyCell class="text-center">
                  {{ item.branch }}
                </td>
              </tr>
            }
          </table>
        </div>
        <div class="mt-3">
          <aplz-ui-pagination
            [totalPages]="context.pagesBySalesCounting"
            [currentPage]="context.salesPageNum"
            (selectedPage)="changeSalesPage($event)">
          </aplz-ui-pagination>
        </div>
      } @else {
        <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
          <aplz-ui-common-message
            [i18Text]="{
              title: context.emptySalesTextTemplate.title,
              description: context.emptySalesTextTemplate.description
            }"
            imgName="emptyLoans">
          </aplz-ui-common-message>
        </div>
      }
    </section>

    <section class="px-4 md:px-8 mt-12">
      <h3 class="text-lg my-2 font-medium">
        {{ context.balanceAdjustmentsTitleTextTemplate.title }}
      </h3>

      @if (context.hasAdjustments) {
        <div
          class="bg-light pt-2 pb-4 rounded-lg shadow-md max-h-96 overflow-auto">
          <table aplzSimpleTable aria-label="Balance Adjustments List">
            <tr aplzSimpleTableHeaderRow>
              <th aplzSimpleTableHeaderCell class="text-center" scope="col">
                {{ context.balanceAdjustmentsTextTemplate.id }}
              </th>
              <th aplzSimpleTableHeaderCell class="text-center" scope="col">
                {{ context.balanceAdjustmentsTextTemplate.loanId }}
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center max-w-36">
                {{ context.balanceAdjustmentsTextTemplate.created }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceAdjustmentsTextTemplate.amount }}
              </th>
              <th aplzSimpleTableHeaderCell scope="col" class="text-center">
                {{ context.balanceAdjustmentsTextTemplate.type }}
              </th>
              <th
                aplzSimpleTableHeaderCell
                scope="col"
                class="text-center max-w-44">
                {{ context.balanceAdjustmentsTextTemplate.comment }}
              </th>
            </tr>

            @for (item of adjustments$ | async; track item) {
              <tr aplzSimpleTableBodyRow>
                <td
                  aplzSimpleTableBodyCell
                  class="text-center font-semibold tabular-nums">
                  {{ item.id }}
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="text-center font-semibold tabular-nums">
                  {{ item.loanId }}
                </td>
                <td aplzSimpleTableBodyCell class="text-center max-w-36">
                  {{ item.created | aplzDynamicPipe: 'date' }}
                </td>
                <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                  {{ item.amount | aplzDynamicPipe: 'currency' }}
                </td>
                <td aplzSimpleTableBodyCell class="text-center font-semibold">
                  {{ item.type }}
                </td>
                <td
                  aplzSimpleTableBodyCell
                  class="text-center max-w-44 text-balance">
                  {{ item.comment }}
                </td>
              </tr>
            }
          </table>
        </div>
        <div class="mt-3">
          <aplz-ui-pagination
            [totalPages]="context.pagesByAdjustmentsCounting"
            [currentPage]="context.adjustemntsPageNum"
            (selectedPage)="changeAdjustmentsPage($event)">
          </aplz-ui-pagination>
        </div>
      } @else {
        <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
          <aplz-ui-common-message
            [i18Text]="{
              title: context.emptyAdjustmentsTextTemplate.title,
              description: context.emptyAdjustmentsTextTemplate.description
            }"
            imgName="emptyLoans">
          </aplz-ui-common-message>
        </div>
      }
    </section>
  </div>
}
