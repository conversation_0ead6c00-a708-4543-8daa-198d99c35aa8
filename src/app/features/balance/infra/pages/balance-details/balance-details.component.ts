import { AsyncPipe } from '@angular/common';
import { Component, OnDestroy, inject } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoStatsComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { iconArrowLeft, iconDownloadTray } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  EMPTY,
  Subject,
  combineLatest,
  combineLatestWith,
  map,
  shareReplay,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { DASH_ROUTES } from '../../../../../config/app-route-core';
import { UserStoreService } from '../../../../user/src/application/services/user-store.service';
import { PaymentBalanceAdjustmentUsecase } from '../../../application/usecases/payment-balance-adjustments.usecase';
import { PaymentBalanceReceiptDownloadUseCase } from '../../../application/usecases/payment-balance-receipt-download.usecase';
import { PaymentBalanceReportUseCase } from '../../../application/usecases/payment-balance-report.usecase';
import { PaymentBalanceSalesSearchUsecase } from '../../../application/usecases/payment-balance-sales-search.usecase';
import { PaymentBalanceSalesUsecase } from '../../../application/usecases/payment-balance-sales.usecase';
import { CriteriaBalanceDetailsService } from '../../services/criteria-balance-details.service';

interface IBalanceAdjustmentsInjectedTextUI {
  id: string;
  loanId: string;
  type: string;
  comment: string;
  created: string;
  amount: string;
}

interface IBalanceSalesInjectedTextUI {
  id: string;
  date: string;
  sales: string;
  feeRevenue: string;
  payMerchant: string;
  status: string;
  statusPayment: string;
  cartId: string;
  branch: string;
}

interface IBalanceStatsInjectedTextUI {
  totalAdjustment: IStatCardInjectedTextUI;
  totalFee: IStatCardInjectedTextUI;
  totalPay: IStatCardInjectedTextUI;
  totalSales: IStatCardInjectedTextUI;
}

interface IStatCardInjectedTextUI {
  statCardTitle: string;
  helpTooltip?: string;
}

@Component({
  selector: 'app-balance-details',
  templateUrl: './balance-details.component.html',
  imports: [
    AsyncPipe,
    AplazoDynamicPipe,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoPaginationComponent,
    AplazoStatsComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoDropdownComponents,
  ],
})
export class BalanceDetailsComponent implements OnDestroy {
  readonly #userStore = inject(UserStoreService);
  readonly #criteria = inject(CriteriaBalanceDetailsService);
  readonly #redirecter = inject(RedirectionService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #i18n = inject(I18NService);
  readonly #adjustmentUseCase = inject(PaymentBalanceAdjustmentUsecase);
  readonly #balanceReportUseCase = inject(PaymentBalanceReportUseCase);
  readonly #balanceSalesSearchUseCase = inject(
    PaymentBalanceSalesSearchUsecase
  );
  readonly #salesUseCase = inject(PaymentBalanceSalesUsecase);
  readonly #receiptDownloadByIdUseCase = inject(
    PaymentBalanceReceiptDownloadUseCase
  );
  readonly #scope = 'balance-details';

  readonly #destroy$ = new Subject<void>();

  readonly balanceStatsTextTemplate$ =
    this.#i18n.getTranslateObjectByKey<IBalanceStatsInjectedTextUI>({
      key: 'stats',
      scope: this.#scope,
    });

  readonly balanceActionsTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    backButtonLabel: string;
  }>({
    key: 'actions',
    scope: this.#scope,
  });

  readonly balanceSalesTextTemplate$ =
    this.#i18n.getTranslateObjectByKey<IBalanceSalesInjectedTextUI>({
      key: 'salesTable',
      scope: this.#scope,
    });

  readonly balanceSalesTitleTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
  }>({
    key: 'salesTitle',
    scope: this.#scope,
  });

  readonly balanceAdjustmentsTitleTextTemplate$ =
    this.#i18n.getTranslateObjectByKey<{
      title: string;
    }>({
      key: 'adjustmentsTitle',
      scope: this.#scope,
    });

  readonly balanceAdjustmentsTextTemplate$ =
    this.#i18n.getTranslateObjectByKey<IBalanceAdjustmentsInjectedTextUI>({
      key: 'adjustmentsTable',
      scope: this.#scope,
    });

  readonly emptySalesTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button?: {
      label: string;
    };
  }>({
    key: 'emptySales',
    scope: this.#scope,
  });

  readonly emptyAdjustmentsTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button?: {
      label: string;
    };
  }>({
    key: 'emptyAdjustments',
    scope: this.#scope,
  });

  downloadReceiptByIdTextUI$ = this.#i18n
    .getTranslateObjectByKey<{
      paymentsLabel: string;
      receiptsLabel: string;
    }>({
      key: 'downloadReceipt',
      scope: this.#scope,
    })
    .pipe(
      map(content => {
        if (!content || !content.paymentsLabel || !content.receiptsLabel) {
          return null;
        }

        return {
          paymentsLabel: content.paymentsLabel,
          receiptsLabel: content.receiptsLabel,
        };
      })
    );

  readonly #statCardKeys = [
    'totalSales',
    'totalFee',
    'totalAdjustment',
    'totalPay',
  ];

  balanceStats$ = this.#criteria.selectedBalance$.pipe(
    map(item => ({
      totalSales: item.saleAmount,
      totalFee: item.feeAmount,
      totalAdjustment: item.adjustment,
      totalPay: item.finalAmount,
    })),
    combineLatestWith(this.balanceStatsTextTemplate$),
    map(([stat, i18nText]) => {
      return this.#statCardKeys.map(key => {
        const pipeName: ValidDynamicPipesNames = 'currency';

        return {
          isDarkMode: false,
          statCardKey: key,
          statCardTitle: i18nText[key].statCardTitle,
          value: String(stat[key]),
          helpTooltip: i18nText[key].helpTooltip,
          tooltipSpaceActive: i18nText[key].tooltipSpaceActive ?? false,
          pipeName,
        };
      });
    }),
    takeUntil(this.#destroy$)
  );

  selectedBalanceId$ = this.#criteria.selectedBalance$.pipe(
    map(item => item.id)
  );

  salesPageNum$ = this.#criteria.getSalesPageNum$();

  salesPageSize$ = this.#criteria.getSalesPageSize$();

  adjustemntsPageNum$ = this.#criteria.getAdjustmentsPageNum$();

  adjustmentsPageSize$ = this.#criteria.getAdjustmentsPageSize$();

  searchbarSalesValue$ = this.#criteria.getSalesSearch$();

  isSearchSalesEnabled$ = this.#criteria.hasSalesActiveSearch$();

  readonly #salesCounting$ = new BehaviorSubject<number>(0);
  salesCounting$ = this.#salesCounting$.pipe(takeUntil(this.#destroy$));
  pagesBySalesCounting$ = this.#salesCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  sales$ = this.#criteria.salesCriteria$.pipe(
    combineLatestWith(
      this.selectedBalanceId$,
      this.#criteria.getSalesDateRange$()
    ),

    switchMap(([criteria, balanceId, salesDateRange]) => {
      if (criteria.search) {
        return this.#balanceSalesSearchUseCase
          .execute({
            search: criteria.search,
            dateRange: salesDateRange,
          })
          .pipe(
            tap(response => {
              this.#salesCounting$.next(response.length);
            })
          );
      }

      return this.#salesUseCase
        .execute({
          salesBalanceId: balanceId,
          pageNum: criteria.pageNum,
          pageSize: criteria.pageSize,
        })
        .pipe(
          tap(response => {
            this.#salesCounting$.next(response.totalElements);
          }),

          map(response => response.content)
        );
    }),

    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  readonly #adjustmentsCounting$ = new BehaviorSubject<number>(0);
  adjustmentsCounting$ = this.#adjustmentsCounting$.pipe(
    takeUntil(this.#destroy$)
  );
  pagesByAdjustmentsCounting$ = this.#adjustmentsCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  adjustments$ = this.#criteria.adjustmentsCriteria$.pipe(
    withLatestFrom(this.selectedBalanceId$),

    switchMap(([criteria, balanceId]) => {
      return this.#adjustmentUseCase.execute({
        adjustmentBalanceId: String(balanceId),
        pageNum: criteria.pageNum,
        pageSize: criteria.pageSize,
      });
    }),

    tap(response => {
      this.#adjustmentsCounting$.next(response.totalElements);
    }),

    map(response => response.content),

    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  hasSales$ = this.sales$.pipe(
    map(sales => sales.length > 0),
    takeUntil(this.#destroy$)
  );

  hasAdjustments$ = this.adjustments$.pipe(
    map(adjustments => adjustments.length > 0),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    selectedBalanceId: this.selectedBalanceId$,
    balanceStats: this.balanceStats$,
    hasSales: this.hasSales$,
    balanceSalesTextTemplate: this.balanceSalesTextTemplate$,
    balanceSalesTitleTextTemplate: this.balanceSalesTitleTextTemplate$,
    sales: this.sales$,
    adjustments: this.adjustments$,
    hasAdjustments: this.hasAdjustments$,
    balanceAdjustmentsTextTemplate: this.balanceAdjustmentsTextTemplate$,
    balanceAdjustmentsTitleTextTemplate:
      this.balanceAdjustmentsTitleTextTemplate$,
    downloadReceiptByIdTextUI: this.downloadReceiptByIdTextUI$,
    balanceStatsTextTemplate: this.balanceStatsTextTemplate$,
    balanceActionsTextTemplate: this.balanceActionsTextTemplate$,
    emptySalesTextTemplate: this.emptySalesTextTemplate$,
    emptyAdjustmentsTextTemplate: this.emptyAdjustmentsTextTemplate$,
    salesPageNum: this.salesPageNum$,
    salesPageSize: this.salesPageSize$,
    adjustemntsPageNum: this.adjustemntsPageNum$,
    salesCounting: this.salesCounting$,
    adjustmentsCounting: this.adjustmentsCounting$,
    pagesBySalesCounting: this.pagesBySalesCounting$,
    pagesByAdjustmentsCounting: this.pagesByAdjustmentsCounting$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconDownloadTray, iconArrowLeft]);
  }

  goBackRoute(): void {
    this.#redirecter.internalNavigation([
      DASH_ROUTES.rootApp,
      DASH_ROUTES.balance,
    ]);
  }

  searchBalanceSales(search: string): void {
    this.#criteria.setSearchSalesCriteria(search);
  }

  downloadReportByReportId(): void {
    combineLatest([
      this.#userStore.merchantId$,
      this.#criteria.selectedBalance$,
    ])
      .pipe(
        switchMap(([merchantId, selectedBalance]) => {
          if (!merchantId || !selectedBalance.id) {
            return EMPTY;
          }

          return this.#balanceReportUseCase.execute({
            merchantId: merchantId,
            balanceId: selectedBalance.id,
            date: selectedBalance.paymentDate,
          });
        }),
        take(1)
      )
      .subscribe();
  }

  downloadReceiptByReportId(): void {
    combineLatest({
      merchantId: this.#userStore.merchantId$,
      selectedBalance: this.#criteria.selectedBalance$,
    })
      .pipe(
        switchMap(({ merchantId, selectedBalance }) =>
          this.#receiptDownloadByIdUseCase.execute({
            merchantId,
            date: selectedBalance.paymentDate,
            balanceId: selectedBalance.id,
          })
        ),
        take(1)
      )
      .subscribe();
  }

  changeSalesPage(page: number): void {
    this.#criteria.setPageNumSalesCriteria(page);
  }

  changeAdjustmentsPage(page: number): void {
    this.#criteria.setPageNumAdjustmentsCriteria(page);
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
