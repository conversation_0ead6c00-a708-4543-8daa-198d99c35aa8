@if (vm$ | async; as context) {
  <section class="px-4 md:px-8">
    <div
      class="flex flex-col md:flex-row flex-wrap gap-x-3 items-center pt-4 pb-2">
      <aplz-ui-form-datepicker
        [monthView]="true"
        [formControl]="dateControl"
        [centerText]="true"
        [maxDate]="todayRawDateDayFirst"
        [minDate]="minDate"></aplz-ui-form-datepicker>

      <aplz-ui-select [formControl]="balanceStatusControl" label="Estado">
        @for (opt of context.balanceStatus; track opt) {
          <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
        }
      </aplz-ui-select>

      <div
        class="mb-8 flex-grow flex-shrink-0 flex justify-center md:justify-end items-center">
        @if (context.downloadAllReceiptsTextUI; as monthOptionsTextUI) {
          @if (context.hasPaymentBalance) {
            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="sm"
              class="font-semibold"
              [rounded]="true"
              [aplzDropdownTriggerFor]="downloadAllOptions"
              (click)="openReportsByMonthDownloadDropdown()">
              <span class="mr-1">
                <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
              </span>
              Descargar
            </button>
          }
          <aplz-ui-dropdown #downloadAllOptions>
            <aplz-ui-dropdown-item>
              <button class="px-4 py-2" (click)="downloadSummaryReport()">
                {{ monthOptionsTextUI.paymentsLabel }}
              </button>
            </aplz-ui-dropdown-item>
            <aplz-ui-dropdown-item>
              <button class="px-4 py-2" (click)="downloadAllReceipts()">
                {{ monthOptionsTextUI.receiptsLabel }}
              </button>
            </aplz-ui-dropdown-item>
            <aplz-ui-dropdown-item>
              <button class="px-4 py-2" (click)="downloadAllInvoices()">
                {{ monthOptionsTextUI.invoiceLabel }}
              </button>
            </aplz-ui-dropdown-item>
          </aplz-ui-dropdown>
        } @else {
          @if ((hasPaymentBalance$ | async) === true) {
            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="sm"
              [rounded]="true"
              (click)="downloadSummaryReport()">
              <span class="mr-1">
                <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
              </span>
              Descargar
            </button>
          }
        }
      </div>
    </div>
  </section>

  <section
    class="-mt-4 pt-8 md:pt-0 px-4 md:px-8 grid gap-4 grid-cols-1 md:grid-cols-2 2xl:grid-cols-4">
    @for (stat of context.balanceStats; track stat) {
      <aplz-ui-metric-card class="min-h-auto">
        <aplz-ui-metric-card-header>
          <div
            class="aplazo-metric-card__label"
            [class.tooltip--with-space]="
              stat.helpTooltip && stat.tooltipSpaceActive
            ">
            <span
              class="aplazo-metric-card__label-title text-base first-letter:uppercase">
              {{ stat.statCardTitle }}
            </span>
            @if (stat.helpTooltip) {
              <aplz-ui-icon
                name="question-mark-circle"
                size="xs"
                [aplzTooltip]="stat.helpTooltip ?? ''"></aplz-ui-icon>
            }
          </div>
        </aplz-ui-metric-card-header>
        <aplz-ui-metric-card-content>
          <div class="text-2xl font-semibold mb-1">
            {{ stat.value | aplzDynamicPipe: stat.pipeName }}
          </div>
        </aplz-ui-metric-card-content>
      </aplz-ui-metric-card>
    }
  </section>

  <section
    class="px-4 md:px-8 mt-4 md:mt-0 min-h-28 flex flex-col md:flex-row items-center justify-center md:justify-between">
    <div class="text-center lg:text-left">
      @if (context.counterLabelTextTemplate; as counterLabelTextTemplate) {
        <span class="whitespace-nowrap font-medium text-lg">
          {{ context.itemsCounting | i18nPlural: counterLabelTextTemplate }}
        </span>
      }
    </div>

    <div class="md:mt-8 max-w-48">
      <aplz-ui-search
        [textUI]="context.searchbarInjectedText"
        [minLength]="minLength"
        [formControl]="searchControl"
        [onlyNumbers]="true"
        [hidden]="context.searchBarConfig.isShown === false"></aplz-ui-search>
    </div>
  </section>

  <section class="px-4 md:px-8 -mt-4 pb-16">
    @if (context.hasPaymentBalance) {
      <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
        <table aplzSimpleTable aria-label="Balance List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.balanceListTextTemplate.id }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.balanceListTextTemplate.paymentDate }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.balanceListTextTemplate.saleAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.balanceListTextTemplate.adjustment }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center text-pretty max-w-16">
              {{ context.balanceListTextTemplate.feeAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.balanceListTextTemplate.payAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.balanceListTextTemplate.actions }}
            </th>
          </tr>

          @for (item of context.balanceList; track item) {
            <tr aplzSimpleTableBodyRow>
              <td aplzSimpleTableBodyCell class="font-semibold">
                <button
                  class="mx-auto"
                  aplzButton
                  aplzAppearance="basic"
                  aplzColor="light"
                  size="md"
                  (click)="goToDetails(item)">
                  {{ item.id }}
                </button>
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                <p>
                  {{ item.paymentDate | aplzDynamicPipe: 'shortDate' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                <p class="pe-2">
                  {{ item.saleAmount | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                <p class="pe-2">
                  {{ item.adjustment | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                <p class="pe-2">
                  {{ item.feeAmount | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td
                aplzSimpleTableBodyCell
                class="font-semibold tabular-nums text-center">
                <p class="pe-2">
                  {{ item.finalAmount | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell>
                <div class="mx-auto w-fit">
                  @if (downloadReceiptByIdTextUI$ | async; as downloadReceipt) {
                    <button
                      aplzButton
                      aplzAppearance="solid"
                      aplzColor="dark"
                      size="sm"
                      class="font-semibold"
                      [rounded]="true"
                      [aplzDropdownTriggerFor]="downloadOptions"
                      (click)="openReportByIdDownloadDropdown()">
                      <aplz-ui-icon
                        name="download-tray"
                        size="xs"></aplz-ui-icon>
                    </button>
                    <aplz-ui-dropdown #downloadOptions>
                      <aplz-ui-dropdown-item>
                        <button
                          class="px-4 py-2"
                          (click)="
                            downloadPaymentsByReportId(
                              item.id,
                              item.paymentDate
                            )
                          ">
                          {{ downloadReceipt.paymentsLabel }}
                        </button>
                      </aplz-ui-dropdown-item>
                      <aplz-ui-dropdown-item>
                        @if (item.statusReceipt === 'AVAILABLE') {
                          <button
                            class="px-4 py-2"
                            (click)="
                              downloadReceiptByReportId(
                                item.id,
                                item.paymentDate
                              )
                            ">
                            {{ downloadReceipt.receiptsLabel }}
                          </button>
                        }
                      </aplz-ui-dropdown-item>
                      <aplz-ui-dropdown-item>
                        @if (item.statusInvoice === 'INVOICED') {
                          <button
                            class="px-4 py-2"
                            (click)="
                              downloadInvoiceByReportId(
                                item.id,
                                item.paymentDate
                              )
                            ">
                            {{ downloadReceipt.invoiceLabel }}
                          </button>
                        }
                      </aplz-ui-dropdown-item>
                    </aplz-ui-dropdown>
                  } @else {
                    <button
                      aplzButton
                      aplzAppearance="solid"
                      aplzColor="dark"
                      size="sm"
                      class="font-semibold"
                      [rounded]="true"
                      (click)="
                        downloadPaymentsByReportId(item.id, item.paymentDate)
                      ">
                      <aplz-ui-icon
                        name="download-tray"
                        size="xs"></aplz-ui-icon>
                    </button>
                  }
                </div>
              </td>
            </tr>
          }
        </table>
      </div>
      <div class="mt-3">
        @if (context.hasSearch) {
          <aplz-ui-pagination
            [totalPages]="context.pagesByItemsCounting"
            [currentPage]="context.currentPage"
            (selectedPage)="changePage($event)">
          </aplz-ui-pagination>
        }
      </div>
    } @else {
      @if (context.emptyLoansInjectedText; as emptyMessageUIText) {
        <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
          <aplz-ui-common-message
            [i18Text]="{
              title: context.emptyLoansInjectedText.title,
              description: context.emptyLoansInjectedText.description
            }"
            imgName="emptyLoans">
          </aplz-ui-common-message>
        </div>
      }
    }
  </section>
}
