import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Data } from '@angular/router';
import { I18NService } from '@aplazo/i18n';
import {
  DateCalculator,
  RawDateYearFirstWithHyphen,
  RedirectionService,
  TemporalService,
} from '@aplazo/merchant/shared';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { iconDownloadTray } from '@aplazo/ui-icons';
import { isBefore } from 'date-fns';
import {
  BehaviorSubject,
  EMPTY,
  MonoTypeOperatorFunction,
  Observable,
  Subject,
  catchError,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  filter,
  map,
  pipe,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
  zip,
} from 'rxjs';
import { DASH_ROUTES } from '../../../../../config/app-route-core';
import { EventManagerService } from '../../../../../services/event-manger.service';
import { UserStoreService } from '../../../../user/src/application/services/user-store.service';
import { PaymentBalanceAllInvoicesDownloadUsecase } from '../../../application/usecases/payment-balance-all-invoices-download.usecase';
import { PaymentBalanceAllReceiptsDownloadUseCase } from '../../../application/usecases/payment-balance-all-receipts-download.usecase';
import { PaymentBalanceInvoiceDownloadByIdUseCase } from '../../../application/usecases/payment-balance-invoice-download.usecase';
import { PaymentBalanceListUsecase } from '../../../application/usecases/payment-balance-list.usecase';
import { PaymentBalanceReceiptDownloadUseCase } from '../../../application/usecases/payment-balance-receipt-download.usecase';
import { PaymentBalanceReportUseCase } from '../../../application/usecases/payment-balance-report.usecase';
import { PaymentBalanceSearchUsecase } from '../../../application/usecases/payment-balance-search.usecase';
import { PaymentBalanceStatsUseCase } from '../../../application/usecases/payment-balance-stats.usecase';
import { PaymentBalanceSummaryReportUsecase } from '../../../application/usecases/payment-balance-summary-report.usecase';
import { PaymentBalance } from '../../../domain/entities/payment-balance';
import { TransactionStatusId } from '../../../domain/entities/payment-balance.status';
import { CriteriaBalanceDetailsService } from '../../services/criteria-balance-details.service';
import {
  CriteriaBalanceService,
  IBalanceCriteriaDto,
} from '../../services/criteria-balance.service';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';

interface IBalanceListInjectedTextUI {
  id: string;
  paymentDate: string;
  saleAmount: string;
  adjustment: string;
  feeAmount: string;
  status: string;
  payAmount: string;
  actions: string;
}

interface IBalanceStatsInjectedTextUI {
  totalAdjustment: IStatCardInjectedTextUI;
  totalFee: IStatCardInjectedTextUI;
  totalPay: IStatCardInjectedTextUI;
  totalSales: IStatCardInjectedTextUI;
}

interface IStatCardInjectedTextUI {
  statCardTitle: string;
  helpTooltip?: string;
}

const BALANCE_STATUS: Readonly<Record<string, TransactionStatusId>> = {
  'Sin pedidos': 'empty',
  'En proceso': 'process',
  Creado: 'created',
} as const;

@Component({
  selector: 'app-balance',
  templateUrl: './balance.component.html',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    I18nPluralPipe,
    AplazoDynamicPipe,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoPaginationComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoDropdownComponents,
    AplazoSearchInputComponent,
    AplazoFormDatepickerComponent,
    AplazoMetricCardComponents,
    AplazoTooltipDirective,
  ],
})
export class BalanceComponent implements OnInit, OnDestroy {
  readonly #chageDetector = inject(ChangeDetectorRef);
  readonly #balanceCriteria = inject(CriteriaBalanceService);
  readonly #userStore = inject(UserStoreService);
  readonly #dateCalculator = inject(DateCalculator);
  readonly #balanceDetailsService = inject(CriteriaBalanceDetailsService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #i18n = inject(I18NService);
  readonly #redirecter = inject(RedirectionService);
  readonly #route = inject(ActivatedRoute);
  readonly #scope = 'balance';
  readonly #temporal = inject(TemporalService);
  readonly #tagService = inject(EventManagerService);
  readonly #balanceListUsecase = inject(PaymentBalanceListUsecase);
  readonly #downloadAllReceipts = inject(
    PaymentBalanceAllReceiptsDownloadUseCase
  );
  readonly #receiptDownloadByIdUseCase = inject(
    PaymentBalanceReceiptDownloadUseCase
  );
  readonly #balanceSearchUsecase = inject(PaymentBalanceSearchUsecase);
  readonly #balanceReportUseCase = inject(PaymentBalanceReportUseCase);
  readonly #balanceInvoiceByIdUsecase = inject(
    PaymentBalanceInvoiceDownloadByIdUseCase
  );
  readonly #balanceStatsUsecase = inject(PaymentBalanceStatsUseCase);
  readonly #balanceSummaryReportUsecase = inject(
    PaymentBalanceSummaryReportUsecase
  );
  readonly #downloadAllInvoicesUseCase = inject(
    PaymentBalanceAllInvoicesDownloadUsecase
  );
  readonly #destroy$ = new Subject<void>();

  readonly minLength = 3;

  searchControl = new FormControl<string>('');
  readonly #searchControlFac = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });
  hasSearch$ = this.searchControl.valueChanges.pipe(
    this.#searchControlFac.hasActiveSearch(),
    takeUntil(this.#destroy$)
  );
  readonly dateControl = new FormControl<Date | null>(
    this.#temporal.fromStringToDate(this.#balanceCriteria.getDateSync()),
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  balanceStatsTextTemplate$ =
    this.#i18n.getTranslateObjectByKey<IBalanceStatsInjectedTextUI>({
      key: 'stats',
      scope: this.#scope,
    });
  balanceListTextTemplate$ =
    this.#i18n.getTranslateObjectByKey<IBalanceListInjectedTextUI>({
      key: 'list',
      scope: this.#scope,
    });
  searchbarInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: this.#scope,
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  emptyLoansInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptyLoans',
    scope: this.#scope,
  });
  emptySearchInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: this.#scope,
  });
  counterLabelTextTemplate$ = this.#i18n
    .getTranslateObjectByKey<Record<string, string>>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      })),
      takeUntil(this.#destroy$)
    );
  downloadReceiptByIdTextUI$ = this.#i18n
    .getTranslateObjectByKey<{
      paymentsLabel: string;
      receiptsLabel: string;
      invoiceLabel: string;
    }>({
      key: 'downloadReceipt',
      scope: this.#scope,
    })
    .pipe(
      map(content => {
        if (
          !content ||
          !content.paymentsLabel ||
          !content.receiptsLabel ||
          !content.invoiceLabel
        ) {
          return null;
        }

        return content;
      })
    );
  downloadAllReceiptsTextUI$ = this.#i18n
    .getTranslateObjectByKey<{
      paymentsLabel: string;
      receiptsLabel: string;
      invoiceLabel: string;
    }>({
      key: 'downloadAllReceipts',
      scope: this.#scope,
    })
    .pipe(
      map(content => {
        if (!content || !content.paymentsLabel || !content.receiptsLabel) {
          return null;
        }

        return {
          paymentsLabel: content.paymentsLabel,
          receiptsLabel: content.receiptsLabel,
          invoiceLabel: content.invoiceLabel,
        };
      })
    );

  initialDateToBalance = this.#temporal.fromStringToDate('01/07/2023');
  readonly minDate = this.#temporal.formatRawDateDayFirst(
    this.initialDateToBalance
  );

  dynamicDateToGetBalances$: Observable<RawDateYearFirstWithHyphen | null> =
    this.#route.data.pipe(
      map<Data, RawDateYearFirstWithHyphen | null>(
        d => d.dynamicBalanceConfig.paymentBalanceDateStart
      ),
      tap(newInitialDate => {
        if (newInitialDate) {
          this.initialDateToBalance =
            this.#temporal.fromStringToDate(newInitialDate);
          this.#chageDetector.detectChanges();
        }
      }),
      takeUntil(this.#destroy$)
    );

  readonly #criteria$ = this.#balanceCriteria.getBalanceCriteria$();

  readonly #itemsCounting$ = new BehaviorSubject<number>(0);

  itemsCounting$ = this.#itemsCounting$.pipe(takeUntil(this.#destroy$));
  pagesByItemsCounting$ = this.#itemsCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;

  readonly #balanceStatus$ = new BehaviorSubject<string[]>(
    Object.keys(BALANCE_STATUS)
  );

  balanceStatus$ = this.#balanceStatus$.pipe(takeUntil(this.#destroy$));

  balanceStatusControl = new FormControl<string>('', {
    nonNullable: true,
  });

  currentPage$ = this.#balanceCriteria.getPageNum$();

  readonly #statCardKeys = [
    'totalSales',
    'totalFee',
    'totalAdjustment',
    'totalPay',
  ];

  balanceStats$ = this.#criteria$.pipe(
    takeUntil(this.#destroy$),
    this.#filterSearchStream(),
    combineLatestWith(this.dynamicDateToGetBalances$),
    switchMap(([criteria, dynamicDate]) =>
      this.#balanceStatsUsecase.execute({
        ...criteria,
        initialDateToBalances: this.initialDateToBalance,
        dynamicInitialDateToBalances: dynamicDate,
      })
    ),
    combineLatestWith(this.balanceStatsTextTemplate$),
    map(([stats, i18nText]) => {
      return this.#statCardKeys.map(key => {
        const pipeName: ValidDynamicPipesNames = 'currency';

        return {
          isDarkMode: false,
          statCardKey: key,
          statCardTitle: i18nText[key].statCardTitle,
          value: String(stats[key]),
          helpTooltip: i18nText[key].helpTooltip,
          tooltipSpaceActive: i18nText[key].tooltipSpaceActive ?? false,
          pipeName,
        };
      });
    }),
    takeUntil(this.#destroy$)
  );

  balanceList$ = this.#criteria$.pipe(
    takeUntil(this.#destroy$),
    this.#filterSearchStream(),
    withLatestFrom(this.dynamicDateToGetBalances$),
    switchMap(([criteria, dynamicDate]) => {
      if (criteria.search) {
        return this.#balanceSearchUsecase.execute(+criteria.search).pipe(
          take(1),
          tap(payments => {
            this.#itemsCounting$.next(payments.length);
          })
        );
      }

      return this.#balanceListUsecase
        .execute({
          ...criteria,
          initialDateToBalances: this.initialDateToBalance,
          dynamicInitialDateToBalances: dynamicDate,
        })
        .pipe(
          take(1),
          tap(response => {
            this.#itemsCounting$.next(response.totalElements);
          }),
          map(response => response.content)
        );
    }),

    map(items => {
      return [...items].sort((a, b) => b.id - a.id);
    }),

    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  hasPaymentBalance$ = this.balanceList$.pipe(
    map(paymentBalanceList => paymentBalanceList.length > 0)
  );

  searchBarConfig$: Observable<{ isShown: boolean }> = combineLatest([
    this.#balanceCriteria.getDate$(),
    this.dynamicDateToGetBalances$,
    this.searchbarInjectedText$,
  ]).pipe(
    map(([date, dynamicDate, injectedText]) => {
      if (injectedText == null) {
        return {
          isShown: false,
        };
      }

      if (dynamicDate == null) {
        return {
          isShown: false,
        };
      }

      const today = new Date();

      const requestedDate = this.#temporal.fromStringToDate(date);

      const dynDate = this.#temporal.fromStringToDate(dynamicDate);

      if (isBefore(today, this.initialDateToBalance)) {
        return {
          isShown: false,
        };
      }

      if (isBefore(today, dynDate)) {
        return {
          isShown: false,
        };
      }

      if (
        !this.#dateCalculator.isSameOrAfterMonth({
          baseDate: requestedDate,
          date: dynDate,
        })
      ) {
        return {
          isShown: false,
        };
      }

      return {
        isShown: true,
      };
    })
  );

  readonly vm$ = combineLatest({
    balanceStats: this.balanceStats$,
    balanceList: this.balanceList$,
    hasPaymentBalance: this.hasPaymentBalance$,
    searchBarConfig: this.searchBarConfig$,
    counterLabelTextTemplate: this.counterLabelTextTemplate$,
    downloadReceiptByIdTextUI: this.downloadReceiptByIdTextUI$,
    downloadAllReceiptsTextUI: this.downloadAllReceiptsTextUI$,
    itemsCounting: this.itemsCounting$,
    pagesByItemsCounting: this.pagesByItemsCounting$,
    currentPage: this.currentPage$,
    balanceStatsTextTemplate: this.balanceStatsTextTemplate$,
    balanceListTextTemplate: this.balanceListTextTemplate$,
    searchbarInjectedText: this.searchbarInjectedText$,
    emptyLoansInjectedText: this.emptyLoansInjectedText$,
    emptySearchInjectedText: this.emptySearchInjectedText$,
    hasSearch: this.hasSearch$,
    balanceStatus: this.balanceStatus$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconDownloadTray]);
  }

  changePage(page: number): void {
    this.#balanceCriteria.setPageNum(page);
  }

  openReportByIdDownloadDropdown(): void {
    this.#tagService.sendTrackEvent('customClick', {
      buttonName: 'openDownloadDropdown',
      category: 'download_interaction',
      label: 'download_button',
      url: window.location.href,
      timestamp: Date.now(),
    });
  }

  openReportsByMonthDownloadDropdown(): void {
    this.#tagService.sendTrackEvent('customClick', {
      buttonName: 'openAllByMonthDownloadDropdown',
      category: 'download_interaction',
      label: 'download_button',
      url: window.location.href,
      timestamp: Date.now(),
    });
  }

  downloadPaymentsByReportId(
    reportId: number,
    date: RawDateYearFirstWithHyphen
  ): void {
    this.#userStore.merchantId$
      .pipe(
        switchMap(merchantId => {
          return this.#balanceReportUseCase.execute({
            merchantId: merchantId,
            balanceId: reportId,
            date: date,
            initialDateToBalances: this.initialDateToBalance,
          });
        }),
        take(1)
      )
      .subscribe(() => {
        this.#tagService.sendTrackEvent('customClick', {
          buttonName: 'downloadPaymentsByReportId',
          category: 'download_interaction',
          label: 'download_button',
          url: window.location.href,
          timestamp: Date.now(),
        });
      });
  }

  downloadReceiptByReportId(
    receiptId: number,
    date: RawDateYearFirstWithHyphen
  ): void {
    this.#tagService.sendTrackEvent('customClick', {
      buttonName: 'downloadReceiptsByReportId',
      category: 'receipt_interaction',
      label: 'receipt_download_button',
      url: window.location.href,
      timestamp: Date.now(),
    });

    this.#userStore.merchantId$
      .pipe(
        take(1),
        switchMap(merchantId => {
          const filename = `M${merchantId}_P${receiptId}_${date}.pdf`;

          return this.#receiptDownloadByIdUseCase
            .execute({
              merchantId,
              date,
              balanceId: receiptId,
            })
            .pipe(
              tap(() => {
                this.#tagService.sendTrackEvent('downloadSuccess', {
                  buttonName: 'downloadReceiptsByReportId',
                  category: 'receipt_interaction',
                  label: 'receipt_download',
                  url: window.location.href,
                  timestamp: Date.now(),
                  genericInfo: filename,
                });
              }),
              catchError(e => {
                this.#tagService.sendTrackEvent('downloadFailure', {
                  buttonName: 'downloadReceiptsByReportId',
                  category: 'receipt_interaction',
                  label: 'receipt_download',
                  url: window.location.href,
                  timestamp: Date.now(),
                  genericInfo: filename,
                  status: e.message,
                });
                return EMPTY;
              })
            );
        }),
        take(1)
      )
      .subscribe();
  }

  downloadInvoiceByReportId(
    reportId: number,
    date: RawDateYearFirstWithHyphen
  ): void {
    this.#userStore.merchantId$
      .pipe(
        tap(() => {
          this.#tagService.sendTrackEvent('customClick', {
            buttonName: 'singleInvoiceDownload',
            category: 'invoice_interaction',
            label: 'invoice_download_button',
            url: window.location.href,
            timestamp: Date.now(),
          });
        }),
        switchMap(merchantId => {
          const filename = `M${merchantId}_P${reportId}_${date}.zip`;

          return this.#balanceInvoiceByIdUsecase
            .execute({
              merchantId,
              date,
              balanceId: reportId,
            })
            .pipe(
              tap(() => {
                this.#tagService.sendTrackEvent('downloadSuccess', {
                  buttonName: 'singleInvoiceDownloadSuccess',
                  category: 'invoice_interaction',
                  label: 'invoice_download',
                  url: window.location.href,
                  timestamp: Date.now(),
                  genericInfo: filename,
                });
              }),
              catchError(e => {
                this.#tagService.sendTrackEvent('downloadFailure', {
                  buttonName: 'singleInvoiceDownloadError',
                  category: 'invoice_interaction',
                  label: 'invoice_download',
                  url: window.location.href,
                  timestamp: Date.now(),
                  genericInfo: filename,
                  status: e.message,
                });
                return EMPTY;
              })
            );
        }),
        take(1)
      )
      .subscribe();
  }

  downloadSummaryReport(): void {
    zip([
      this.#userStore.merchantId$,
      this.#balanceCriteria.getDate$(),
      this.#balanceCriteria.getStatus$(),
    ])
      .pipe(
        switchMap(([merchantId, date, status]) =>
          this.#balanceSummaryReportUsecase.execute({
            merchantId,
            date,
            status: status,
          })
        ),
        take(1)
      )
      .subscribe();
  }

  downloadAllReceipts(): void {
    zip([this.#userStore.merchantId$, this.#balanceCriteria.getDate$()])
      .pipe(
        switchMap(([merchantId, date]) =>
          this.#downloadAllReceipts.execute({
            date,
            merchantId,
          })
        ),
        take(1),
        catchError(() => {
          return EMPTY;
        })
      )
      .subscribe();
  }

  downloadAllInvoices(): void {
    zip([this.#userStore.merchantId$, this.#balanceCriteria.getDate$()])
      .pipe(
        switchMap(([merchantId, date]) =>
          this.#downloadAllInvoicesUseCase.execute({
            date,
            merchantId,
          })
        ),
        take(1),
        tap(() => {
          this.#tagService.sendTrackEvent('customClick', {
            buttonName: 'allInvoicesDownload',
            category: 'invoice_interaction',
            label: 'invoice_general_download_button',
            url: window.location.href,
            timestamp: Date.now(),
          });
        }),
        catchError(() => {
          return EMPTY;
        })
      )
      .subscribe();
  }

  goToDetails(paymentBalance: PaymentBalance): void {
    this.#balanceDetailsService.setSelectedBalance(paymentBalance);

    this.#redirecter.internalNavigation([
      DASH_ROUTES.rootApp,
      DASH_ROUTES.balance,
      DASH_ROUTES.balanceDetails,
    ]);
  }

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        startWith(this.searchControl.value),
        this.#searchControlFac.debouncedValue(),
        takeUntil(this.#destroy$)
      )
      .subscribe(value => {
        this.#balanceCriteria.setSearch(value);
      });

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(value => {
          if (value) {
            const dateToSet = this.#temporal.formatRawDateDayFirst(value);
            this.#balanceCriteria.setDate(dateToSet);
            this.searchControl.reset();
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();

    this.balanceStatusControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(statusId => {
        const status = BALANCE_STATUS[statusId];
        this.#balanceCriteria.setBalanceStatus(status);
      });

    this.#balanceCriteria
      .getStatus$()
      .pipe(take(1))
      .subscribe(status => {
        const key = Object.entries(BALANCE_STATUS).find(
          ([, value]) => value === status
        )[0];

        this.balanceStatusControl.setValue(key);

        if (this.#balanceStatus$.value.length <= 1) {
          this.balanceStatusControl.disable();
        } else {
          this.balanceStatusControl.enable();
        }
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #filterSearchStream(): MonoTypeOperatorFunction<IBalanceCriteriaDto> {
    return pipe(
      filter(
        criteria =>
          criteria.search != null &&
          (criteria.search.length === 0 ||
            criteria.search.length >= this.minLength)
      )
    );
  }
}
