import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IPaymentBalanceAdjustmentsParamsDto } from '../../application/dtos/payment-balance-adjustments-params.dto';
import { IPaymentBalanceAdjustmentsResponseDto } from '../../application/dtos/payment-balance-adjustments-response.dto';
import { PaymentBalanceAdjustmentsRepository } from '../../domain/repositories/payment-balance-adjustments.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceAdjustmentsRepositoryImpl
  implements
    PaymentBalanceAdjustmentsRepository<
      IPaymentBalanceAdjustmentsParamsDto,
      Observable<IPaymentBalanceAdjustmentsResponseDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getList(
    args: IPaymentBalanceAdjustmentsParamsDto
  ): Observable<IPaymentBalanceAdjustmentsResponseDto> {
    return this.#http.get<IPaymentBalanceAdjustmentsResponseDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/adjustment/merchantPayment/${args.adjustmentBalanceId}/page`,
      {
        params: {
          pageNum: args.pageNum,
          pageSize: args.pageSize,
        },
      }
    );
  }
}
