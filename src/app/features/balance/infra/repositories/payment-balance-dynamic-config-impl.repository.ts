import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { RawDateYearFirstWithHyphen } from '@aplazo/merchant/shared';
import { Observable, catchError, map, of } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { PaymentBalanceDynamicConfigResponseDto } from '../../application/dtos/payment-balance-dynamic-config-response.dto';
import { PaymentBalanceDynamicConfigRepository } from '../../domain/repositories/payment-balance-dynamic-config.repository';

export const defaultDynamicDate: PaymentBalanceDynamicConfigResponseDto = {
  sensitiveData: false,
  paymentBalanceDateStart: '2024-01-01',
};

@Injectable({ providedIn: 'root' })
export class PaymentBalanceDynamicConfigRepositoryImp
  implements
    PaymentBalanceDynamicConfigRepository<
      Observable<PaymentBalanceDynamicConfigResponseDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  execute(): Observable<PaymentBalanceDynamicConfigResponseDto> {
    return this.#http
      .get<PaymentBalanceDynamicConfigResponseDto>(
        `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/status`
      )
      .pipe(
        map(response => {
          const defaultDynamicDate: RawDateYearFirstWithHyphen = '2024-01-01';

          return {
            sensitiveData: response.sensitiveData ?? false,
            paymentBalanceDateStart:
              response.paymentBalanceDateStart ?? defaultDynamicDate,
          };
        }),

        catchError(error => {
          console.error(error);
          return of(defaultDynamicDate);
        })
      );
  }
}
