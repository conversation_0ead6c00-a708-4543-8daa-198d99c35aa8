import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { RawYearMonthDateWithHyphen } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { PaymentBalanceInvoiceRepository } from '../../domain/repositories/payment-balance-invoice.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceInvoiceWithHttp
  implements PaymentBalanceInvoiceRepository
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  byId(args: { balanceId: number }): Observable<any> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/${args.balanceId}/download/invoice`,
      {
        responseType: 'blob',
      }
    );
  }

  byDate(args: { date: RawYearMonthDateWithHyphen }): Observable<any> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/year-month/${args.date}/download/invoice`,
      {
        responseType: 'blob',
      }
    );
  }
}
