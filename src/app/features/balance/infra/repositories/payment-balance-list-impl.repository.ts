import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IPaymentBalanceListRepositoryParamsDto } from '../../application/dtos/payment-balance-list-repository-params.dto';
import { PaymentBalanceListResponseUIDto } from '../../application/dtos/payment-balance-list-response-ui.dto';
import { PaymentBalanceListRepository } from '../../domain/repositories/payment-balance-list.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceListRepositoryImpl
  implements
    PaymentBalanceListRepository<
      IPaymentBalanceListRepositoryParamsDto,
      Observable<PaymentBalanceListResponseUIDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getList(
    args: IPaymentBalanceListRepositoryParamsDto
  ): Observable<PaymentBalanceListResponseUIDto> {
    return this.#http.get<PaymentBalanceListResponseUIDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/page`,
      {
        params: {
          status: args.status,
          monthYear: args.monthYear,
          pageNum: args.pageNum,
          pageSize: args.pageSize,
        },
      }
    );
  }
}
