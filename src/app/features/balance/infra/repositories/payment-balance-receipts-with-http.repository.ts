import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { RawYearMonthDateWithHyphen } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { PaymentBalanceReceiptsRepository } from '../../domain/repositories/payment-balance-receipts.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceReceiptsWithHttp
  implements PaymentBalanceReceiptsRepository
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  byId(args: { balanceId: number }): Observable<any> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/${args.balanceId}/export/receipt`,
      {
        responseType: 'blob',
      }
    );
  }

  byDate(args: { date: RawYearMonthDateWithHyphen }): Observable<any> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/year-month/${args.date}/export/receipt-zip`,
      {
        responseType: 'blob',
      }
    );
  }
}
