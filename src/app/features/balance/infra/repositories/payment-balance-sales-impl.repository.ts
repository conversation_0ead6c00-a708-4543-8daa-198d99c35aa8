import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { PaymentBalanceSalesUIDto } from '../../application/dtos/payment-balance-sales-params.dto';
import { IPaymentBalanceSalesResponseDto } from '../../application/dtos/payment-balance-sales-response.dto';
import { PaymentBalanceSalesRepository } from '../../domain/repositories/payment-balance-sales.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceSalesRepositoryImpl
  implements
    PaymentBalanceSalesRepository<
      PaymentBalanceSalesUIDto,
      Observable<IPaymentBalanceSalesResponseDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getList(
    args: PaymentBalanceSalesUIDto
  ): Observable<IPaymentBalanceSalesResponseDto> {
    return this.#http.get<IPaymentBalanceSalesResponseDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/payment/merchantPayment/${args.salesBalanceId}`,
      {
        params: {
          pageNum: args.pageNum,
          pageSize: args.pageSize,
        },
      }
    );
  }
}
