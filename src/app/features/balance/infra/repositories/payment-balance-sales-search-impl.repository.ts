import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Transaction } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IPaymentBalanceSalesSearchRepositoryParams } from '../../application/dtos/payment-balance-sales-search-repository-params.dto';
import { PaymentBalanceSalesSearchRepository } from '../../domain/repositories/payment-balance-sales-search.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceSalesSearchRepositoryImpl
  implements
    PaymentBalanceSalesSearchRepository<
      IPaymentBalanceSalesSearchRepositoryParams,
      Observable<Transaction[]>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  public search(
    args: IPaymentBalanceSalesSearchRepositoryParams
  ): Observable<Transaction[]> {
    return this.#http.get<Transaction[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/payment/filter`,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
          element: args.search,
        },
      }
    );
  }
}
