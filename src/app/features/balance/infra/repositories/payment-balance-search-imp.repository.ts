import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { PaymentBalance } from '../../domain/entities/payment-balance';
import { PaymentBalanceSearchRepository } from '../../domain/repositories/payment-balance-search.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceSearchRepositoryImpl
  implements PaymentBalanceSearchRepository<Observable<PaymentBalance>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getPaymentById(id: number): Observable<PaymentBalance> {
    return this.#http.get<PaymentBalance>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/${id}`
    );
  }
}
