import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IPaymentBalanceStatsRepositoryParamsDto } from '../../application/dtos/payment-balance-stats-repository-params.dto';
import { PaymentBalanceStats } from '../../domain/entities/payment-balance-stats';
import { PaymentBalanceStatsRepository } from '../../domain/repositories/payment-balance-stats.repository';

@Injectable({
  providedIn: 'root',
})
export class PaymentBalanceStatsRepositoryImpl
  implements
    PaymentBalanceStatsRepository<
      IPaymentBalanceStatsRepositoryParamsDto,
      Observable<PaymentBalanceStats>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getStats(
    request: IPaymentBalanceStatsRepositoryParamsDto
  ): Observable<PaymentBalanceStats> {
    return this.#http.get<PaymentBalanceStats>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/summary`,
      {
        params: {
          status: request.status,
          monthYear: request.monthYear,
        },
      }
    );
  }
}
