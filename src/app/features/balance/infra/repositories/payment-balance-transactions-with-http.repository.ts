import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { RawMonthYearDate } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { TransactionStatus } from '../../domain/entities/payment-balance.status';
import { PaymentBalanceTransctionsRepository } from '../../domain/repositories/payment-balance-transactions.repository';

@Injectable({ providedIn: 'root' })
export class PaymentBalanceTransactionsWithHttp
  implements PaymentBalanceTransctionsRepository
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  byId(args: { balanceId: number }): Observable<string> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/${args.balanceId}/export`,
      {
        responseType: 'text' as const,
      }
    );
  }

  byDate(args: {
    date: RawMonthYearDate;
    status: TransactionStatus;
  }): Observable<string> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchantPayment/edo-summary`,
      {
        params: {
          status: args.status,
          monthYear: args.date,
        },
        responseType: 'text' as const,
      }
    );
  }
}
