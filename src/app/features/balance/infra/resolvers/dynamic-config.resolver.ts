import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { catchError, Observable, of, take } from 'rxjs';
import { PaymentBalanceDynamicConfigResponseDto } from '../../application/dtos/payment-balance-dynamic-config-response.dto';
import { PaymentBalanceDynamicConfigRepository } from '../../domain/repositories/payment-balance-dynamic-config.repository';

export const dynamicConfigDefault: PaymentBalanceDynamicConfigResponseDto = {
  paymentBalanceDateStart: null,
  sensitiveData: false,
};

export const getDynamicConfig: ResolveFn<
  PaymentBalanceDynamicConfigResponseDto
> = (): Observable<PaymentBalanceDynamicConfigResponseDto> => {
  const repository: PaymentBalanceDynamicConfigRepository<
    Observable<PaymentBalanceDynamicConfigResponseDto>
  > = inject(PaymentBalanceDynamicConfigRepository);

  return repository.execute().pipe(
    catchError(() => of(dynamicConfigDefault)),
    take(1)
  );
};
