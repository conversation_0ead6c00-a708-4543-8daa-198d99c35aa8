import { inject, Injectable } from '@angular/core';
import { B2BDateRange, TemporalService } from '@aplazo/merchant/shared';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { PaymentBalance } from '../../domain/entities/payment-balance';

export interface ICriteriaDetailsContent {
  search: string | null;
  pageNum: number;
  pageSize: number;
  dateRange?: B2BDateRange;
}

export interface ICriteriaBalanceDetails {
  selectedBalance: PaymentBalance;
  sales: ICriteriaDetailsContent;
  adjustments: ICriteriaDetailsContent;
}

@Injectable({ providedIn: 'root' })
export class CriteriaBalanceDetailsService {
  readonly #temporal = inject(TemporalService);
  readonly #defaultCriteria: ICriteriaDetailsContent = {
    search: null,
    pageNum: 0,
    pageSize: 10,
  };
  readonly #selectedBalance$ = new BehaviorSubject<PaymentBalance | null>(null);
  readonly #salesCriteria$ = new BehaviorSubject<ICriteriaDetailsContent>({
    ...this.#defaultCriteria,
  });
  readonly #adjustmentsCriteria$ = new BehaviorSubject<ICriteriaDetailsContent>(
    {
      ...this.#defaultCriteria,
    }
  );

  selectedBalance$ = this.#selectedBalance$.asObservable();

  salesCriteria$ = this.#salesCriteria$.asObservable();

  adjustmentsCriteria$ = this.#adjustmentsCriteria$.asObservable();

  hasSelectedBalance$(): Observable<boolean> {
    return this.#selectedBalance$.pipe(
      map(
        selectedBalance =>
          selectedBalance != null && Boolean(selectedBalance.id)
      )
    );
  }

  getSalesDateRange$(): Observable<B2BDateRange | null> {
    return this.#selectedBalance$.pipe(
      map(balance => {
        if (!balance) {
          return null;
        }

        const dateToEvaluate = this.#temporal.fromStringToDate(
          balance.paymentDate
        );

        return {
          startDate: this.#temporal.firstDayOfMonth(dateToEvaluate),
          endDate: this.#temporal.lastDayOfMonthOrToday(dateToEvaluate),
        };
      })
    );
  }

  getSalesPageNum$(): Observable<number> {
    return this.#salesCriteria$.pipe(map(criteria => criteria.pageNum));
  }

  getAdjustmentsPageNum$(): Observable<number> {
    return this.#adjustmentsCriteria$.pipe(map(criteria => criteria.pageNum));
  }

  getSalesPageSize$(): Observable<number> {
    return this.#salesCriteria$.pipe(map(criteria => criteria.pageSize));
  }

  getAdjustmentsPageSize$(): Observable<number> {
    return this.#adjustmentsCriteria$.pipe(map(criteria => criteria.pageSize));
  }

  getSalesSearch$(): Observable<string | null> {
    return this.#salesCriteria$.pipe(map(criteria => criteria.search));
  }

  hasSalesActiveSearch$(): Observable<boolean> {
    return this.#salesCriteria$.pipe(
      map(criteria => criteria.search !== null && criteria.search !== '')
    );
  }

  setSelectedBalance(value: PaymentBalance): void {
    this.#selectedBalance$.next(value);
  }

  setPageNumSalesCriteria(pageNum: number): void {
    if (this.#salesCriteria$.value.pageNum == pageNum) {
      return;
    }

    this.#salesCriteria$.next({
      ...this.#salesCriteria$.value,
      pageNum,
    });
  }

  setPageNumAdjustmentsCriteria(pageNum: number): void {
    if (this.#adjustmentsCriteria$.value.pageNum == pageNum) {
      return;
    }

    this.#adjustmentsCriteria$.next({
      ...this.#adjustmentsCriteria$.value,
      pageNum,
    });
  }

  setSearchSalesCriteria(search: string): void {
    if (this.#salesCriteria$.value.search == search) {
      return;
    }

    this.#salesCriteria$.next({
      ...this.#salesCriteria$.value,
      pageNum: 0,
      search,
    });
  }

  setSearchAdjustmentsCriteria(search: string): void {
    if (this.#adjustmentsCriteria$.value.search == search) {
      return;
    }

    this.#adjustmentsCriteria$.next({
      ...this.#adjustmentsCriteria$.value,
      pageNum: 0,
      search,
    });
  }

  setPageSizeSalesCriteria(pageSize: number): void {
    if (this.#salesCriteria$.value.pageSize == pageSize) {
      return;
    }

    this.#salesCriteria$.next({
      search: null,
      pageNum: 0,
      pageSize,
    });
  }

  setPageSizeAdjustmentsCriteria(pageSize: number): void {
    if (this.#adjustmentsCriteria$.value.pageSize == pageSize) {
      return;
    }

    this.#adjustmentsCriteria$.next({
      search: null,
      pageNum: 0,
      pageSize,
    });
  }
}
