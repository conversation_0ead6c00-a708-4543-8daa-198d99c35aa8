import { inject, Injectable } from '@angular/core';
import { RawDateDayFirst, TemporalService } from '@aplazo/merchant/shared';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { TransactionStatusId } from '../../domain/entities/payment-balance.status';

export interface IBalanceCriteriaDto {
  status: TransactionStatusId;
  date: RawDateDayFirst;
  pageNum: number;
  pageSize: number;
  search: string | null;
}

@Injectable({ providedIn: 'root' })
export class CriteriaBalanceService {
  readonly #temporal = inject(TemporalService);
  readonly #balanceCriteria$ = new BehaviorSubject<IBalanceCriteriaDto>({
    status: 'process',
    date: this.#temporal.todayRawDayFirst,
    pageNum: 0,
    pageSize: 10,
    search: null,
  });

  readonly criteria$ = this.#balanceCriteria$.asObservable();

  getBalanceCriteria$(): Observable<IBalanceCriteriaDto> {
    return this.#balanceCriteria$;
  }

  getStatus$(): Observable<TransactionStatusId> {
    return this.#balanceCriteria$.pipe(map(criteria => criteria.status));
  }

  getDate$(): Observable<RawDateDayFirst> {
    return this.#balanceCriteria$.pipe(map(criteria => criteria.date));
  }

  getDateSync(): RawDateDayFirst {
    return this.#balanceCriteria$.value.date;
  }

  getPageNum$(): Observable<number> {
    return this.#balanceCriteria$.pipe(map(criteria => criteria.pageNum));
  }

  getPageSize$(): Observable<number> {
    return this.#balanceCriteria$.pipe(map(criteria => criteria.pageSize));
  }

  getSearch$(): Observable<string> {
    return this.#balanceCriteria$.pipe(map(criteria => criteria.search));
  }

  setPageNum(pageNum: number): void {
    if (this.#balanceCriteria$.value.pageNum == pageNum) {
      return;
    }

    this.#balanceCriteria$.next({
      ...this.#balanceCriteria$.value,
      pageNum,
    });
  }

  setPageSize(pageSize: number): void {
    if (this.#balanceCriteria$.value.pageSize == pageSize) {
      return;
    }

    this.#balanceCriteria$.next({
      ...this.#balanceCriteria$.value,
      pageSize,
      pageNum: 0,
      search: '',
    });
  }

  setBalanceStatus(status: TransactionStatusId): void {
    if (this.#balanceCriteria$.value.status === status) {
      return;
    }

    this.#balanceCriteria$.next({
      ...this.#balanceCriteria$.value,
      status,
      pageNum: 0,
      search: '',
    });
  }

  setDate(date: RawDateDayFirst): void {
    if (this.#balanceCriteria$.value.date === date) {
      return;
    }

    this.#balanceCriteria$.next({
      ...this.#balanceCriteria$.value,
      date,
      pageNum: 0,
      search: '',
    });
  }

  setSearch(search: string): void {
    if (this.#balanceCriteria$.value.search === search) {
      return;
    }

    this.#balanceCriteria$.next({
      ...this.#balanceCriteria$.value,
      search,
      pageNum: 0,
    });
  }
}
