import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import {
  IBranchOfficeRepositoryResponseDto,
  IBranchOfficeUIDto,
} from '../../domain/entities/branch-office';
import { BranchOfficesRepository } from '../../domain/repositories/branch-offices.repository';

export const getBranchOfficesErrorTitle =
  'Error al cargar las sucursales del comercio.';
export const getBranchOfficesErrorMsg =
  'Nuestro equipo ya está trabajando en ello. Por favor, intenta más tarde.';

@Injectable({ providedIn: 'any' })
export class GetAllBranchOfficesUsecase
  implements BaseUsecase<any, Observable<IBranchOfficeUIDto[]>>
{
  constructor(
    private repository: BranchOfficesRepository<
      Observable<IBranchOfficeRepositoryResponseDto>
    >,
    private loader: LoaderService,
    private usecaseErrorHandler: UseCaseErrorHandler
  ) {}

  execute(): Observable<IBranchOfficeUIDto[]> {
    const idLoader = this.loader.show();

    return this.repository.getItems().pipe(
      map(response => {
        const mapped = response.content?.branches.map(branch => ({
          id: branch.id,
          name: branch.name,
        }));

        return mapped ?? [];
      }),
      catchError(error => this.usecaseErrorHandler.handle(error, of([]))),
      take(1),
      finalize(() => {
        this.loader.hide(idLoader);
      })
    );
  }
}
