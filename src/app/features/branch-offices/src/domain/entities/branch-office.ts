export interface IBranchDetail {
  id: number;
  name: string;
  shareLinkFlags: {
    isWhatsappAllowed: boolean;
    isQrAllowed: boolean;
    isSMSAllowed: boolean;
  } | null;
  branchFeatureFlags: {
    isSellAgentTrackEnable: boolean;
    isSupportChatEnable: boolean;
  } | null;
}

export interface IBranchOfficeUIDto {
  id: number;
  name: string;
}

export interface IBranchOffice {
  id: number;
  logoUrl: string;
  merchantId: number;
  branches: IBranchDetail[];
  DeletedAt: string; // date string
  createdAt: string; // date string
  updatedAt: string; // date string
}

export interface IBranchOfficeRepositoryResponseDto {
  code: number;
  error: unknown;
  content: IBranchOffice | null;
}
