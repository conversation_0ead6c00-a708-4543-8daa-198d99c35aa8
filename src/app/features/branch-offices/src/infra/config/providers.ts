import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { BranchOfficesRepository } from '../../domain/repositories/branch-offices.repository';
import { GetBranchOfficesWithHttpRepository } from '../repositories/branch-offices-with-http.repository';

export function provideBranchOfficesRepository(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: BranchOfficesRepository,
      useClass: GetBranchOfficesWithHttpRepository,
    },
  ]);
}
