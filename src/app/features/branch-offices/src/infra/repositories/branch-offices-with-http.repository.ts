import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { IBranchOfficeRepositoryResponseDto } from '../../domain/entities/branch-office';
import { BranchOfficesRepository } from '../../domain/repositories/branch-offices.repository';

@Injectable({
  providedIn: 'root',
})
export class GetBranchOfficesWithHttpRepository
  implements
    BranchOfficesRepository<Observable<IBranchOfficeRepositoryResponseDto>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getItems(): Observable<IBranchOfficeRepositoryResponseDto> {
    return this.#http.get<IBranchOfficeRepositoryResponseDto>(
      `${this.#environment.apiPosUiBaseUrl}/api/merchant_configs`
    );
  }
}
