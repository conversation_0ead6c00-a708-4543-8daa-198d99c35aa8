import {
  B2BDate<PERSON><PERSON><PERSON>,
  Guard,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { ClarificationStat } from '../../domain/entities/clarification-stat';
import { CLARIFICATION_STATUS } from '../../domain/validation';
import { IGetListRepositoryRequestDto } from '../dtos/get-list-repository-request.dto';
import { IGetListUIRequestDto } from '../dtos/get-list-ui-request.dto';
import { IGetStatsRepositoryRequestDto } from '../dtos/get-stats-repository-request.dto';
import {
  IClarificationGetStatsUIDto,
  IClarificationSummaryUIDto,
} from '../dtos/get-stats-ui.dto';

export const emptyStatusGenericErrorMessage =
  'El estatus de "Aclaratorias" no es válido.';

export const nullStatusGenericErrorMessage =
  'El estatus válido de "Aclaratorias" no puede estar vacio.';

export const emptyDateRangeGenericErrorMessage =
  'El rango de fechas de "Aclaratorias" no es válido.';

export const emptyStartDateGenericErrorMessage =
  'La fecha de inicio de "Aclaratorias" no es válida.';

export const emptyEndDateGenericErrorMessage =
  'La fecha de fin de "Aclaratorias" no es válida.';

export const invalidFormatStartDateGenericErrorMessage =
  'El formato de la fecha de inicio de "Aclaratorias" no es válido.';

export const invalidFormatEndDateGenericErrorMessage =
  'El formato de la fecha de fin de "Aclaratorias" no es válido.';

export const emptyPaginationGenericErrorMessage =
  'La paginación de "Aclaratorias" no es válida.';

export class ClarificationMapper {
  static fromListUIRequestToRepositoryRequest(
    params: IGetListUIRequestDto
  ): IGetListRepositoryRequestDto {
    if (!Guard.againstNullOrUndefined(params, 'status').succeeded) {
      throw new RuntimeMerchantError(
        emptyStatusGenericErrorMessage,
        'ClarificationMapper::List::invalidStatus',
        'clarification-list-mapper-ui-repository'
      );
    }

    const validStatus = CLARIFICATION_STATUS[params.status] ?? false;

    if (!validStatus) {
      throw new RuntimeMerchantError(
        nullStatusGenericErrorMessage,
        'ClarificationMapper::List::nullStatus',
        'clarification-list-mapper-ui-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(params, 'dateRange').succeeded) {
      throw new RuntimeMerchantError(
        emptyDateRangeGenericErrorMessage,
        'ClarificationMapper::List::invalidDateRange',
        'clarification-list-mapper-ui-repository'
      );
    }

    if (
      !Guard.againstNullOrUndefined(params.dateRange, 'startDate').succeeded
    ) {
      throw new RuntimeMerchantError(
        emptyStartDateGenericErrorMessage,
        'ClarificationMapper::List::invalidStartDate',
        'clarification-list-mapper-ui-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(params.dateRange, 'endDate').succeeded) {
      throw new RuntimeMerchantError(
        emptyEndDateGenericErrorMessage,
        'ClarificationMapper::List::invalidEndDate',
        'clarification-list-mapper-ui-repository'
      );
    }

    if (!this.validateExpectedDateFormat(params.dateRange.startDate)) {
      throw new RuntimeMerchantError(
        invalidFormatStartDateGenericErrorMessage,
        'ClarificationMapper::List::invalidFormatStartDate',
        'clarification-list-mapper-ui-repository'
      );
    }

    if (!this.validateExpectedDateFormat(params.dateRange.endDate)) {
      throw new RuntimeMerchantError(
        invalidFormatEndDateGenericErrorMessage,
        'ClarificationMapper::List::invalidFormatEndDate',
        'clarification-list-mapper-ui-repository'
      );
    }

    if (
      !Guard.againstNullOrUndefined(params, 'pageNum').succeeded ||
      !Guard.againstNullOrUndefined(params, 'pageSize').succeeded
    ) {
      throw new RuntimeMerchantError(
        emptyPaginationGenericErrorMessage,
        'ClarificationMapper::List::invalidPagination',
        'clarification-list-mapper-ui-repository'
      );
    }

    return {
      status: validStatus,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      startDate: params.dateRange.startDate,
      endDate: params.dateRange.endDate,
      type: 'A,AP',
    };
  }

  static fromStatsUIRequestToRepositoryRequest(
    dateRange: B2BDateRange
  ): IGetStatsRepositoryRequestDto {
    if (dateRange == null) {
      throw new RuntimeMerchantError(
        emptyDateRangeGenericErrorMessage,
        'ClarificationMapper::Stats::invalidDateRange',
        'clarification-stats-mapper-ui-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(dateRange, 'startDate').succeeded) {
      throw new RuntimeMerchantError(
        emptyStartDateGenericErrorMessage,
        'ClarificationMapper::Stats::invalidStartDate',
        'clarification-stats-mapper-ui-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(dateRange, 'endDate').succeeded) {
      throw new RuntimeMerchantError(
        emptyEndDateGenericErrorMessage,
        'ClarificationMapper::Stats::invalidEndDate',
        'clarification-stats-mapper-ui-repository'
      );
    }

    if (!this.validateExpectedDateFormat(dateRange.startDate)) {
      throw new RuntimeMerchantError(
        invalidFormatStartDateGenericErrorMessage,
        'ClarificationMapper::Stats::invalidFormatStartDate',
        'clarification-stats-mapper-ui-repository'
      );
    }

    if (!this.validateExpectedDateFormat(dateRange.endDate)) {
      throw new RuntimeMerchantError(
        invalidFormatEndDateGenericErrorMessage,
        'ClarificationMapper::Stats::invalidFormatEndDate',
        'clarification-stats-mapper-ui-repository'
      );
    }

    return {
      status:
        // fixed as we can preserve the same behavior on every change of dates
        'MERCHANT_EVIDENCE,INVESTIGATION,APPROVED',
      type: 'A,AP', // fixed as we can preserve the same behavior on every change of dates
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    };
  }

  static fromStatsResponseToUI(
    stats: ClarificationStat[]
  ): IClarificationSummaryUIDto {
    const emptyStats: Partial<IClarificationGetStatsUIDto> = {
      refundAmount: 0,
      totalRefunds: 0,
    };

    const partialRequested = stats
      .filter(val => val.status.toLowerCase() !== 'approved')
      .reduce(
        (prev, curr) => {
          prev.refundAmount += curr.refundAmount;
          prev.totalRefunds += curr.totalRefunds;
          return prev;
        },
        { refundAmount: 0, totalRefunds: 0 }
      );

    const partialApproved =
      stats.find(val => val.status.toLowerCase() === 'approved') || emptyStats;

    const summary: IClarificationSummaryUIDto = {
      requested: {
        refundAmount: partialRequested.refundAmount ?? 0,
        totalRefunds: partialRequested.totalRefunds ?? 0,
      },
      approved: {
        refundAmount: partialApproved.refundAmount ?? 0,
        totalRefunds: partialApproved.totalRefunds ?? 0,
      },
    };

    return summary;
  }

  private static validateExpectedDateFormat(date: string): boolean {
    if (typeof date !== 'string') {
      return false;
    }

    /**
     * regex to probe dd/mm/yyyy format
     * where dd, mm and yyyy are numbers
     */
    return date.match(/^\d{2}\/\d{2}\/\d{4}$/) !== null;
  }
}
