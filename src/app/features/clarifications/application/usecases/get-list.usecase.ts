import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { ClarificationListRepository } from '../../domain/repositories/clarification-list.repository';
import { CLARIFICATION_LABELS } from '../../domain/validation';
import { IGetListRepositoryRequestDto } from '../dtos/get-list-repository-request.dto';
import { IGetListResponseDto } from '../dtos/get-list-response.dto';
import { IGetListUIRequestDto } from '../dtos/get-list-ui-request.dto';
import { ClarificationMapper } from '../mapper/clarification-mapper';

export const emptyListResponse: IGetListResponseDto = {
  first: false,
  last: false,
  number: 0,
  numberOfElements: 0,
  size: 0,
  totalElements: 0,
  totalPages: 0,
  hasContent: false,
  content: [],
};

export const controlledTitleError =
  'Hemos detectado un error en la consulta de aclaratorias';

export const uncontrolledTitleError = 'Parece que algo salió mal,';

export class GetClarificationListUsecase
  implements BaseUsecase<IGetListUIRequestDto, Observable<IGetListResponseDto>>
{
  private readonly repository: ClarificationListRepository<
    IGetListRepositoryRequestDto,
    Observable<IGetListResponseDto>
  >;
  private readonly loader: LoaderService;
  private readonly notifier: NotifierService;

  constructor(
    repository: ClarificationListRepository<
      IGetListRepositoryRequestDto,
      Observable<IGetListResponseDto>
    >,
    loader: LoaderService,
    notifier: NotifierService
  ) {
    this.repository = repository;
    this.loader = loader;
    this.notifier = notifier;
  }

  execute(args: IGetListUIRequestDto): Observable<IGetListResponseDto> {
    const idLoader = this.loader.show();

    try {
      const request =
        ClarificationMapper.fromListUIRequestToRepositoryRequest(args);

      return this.repository.getList(request).pipe(
        map(response => {
          const fallbackStatusName =
            CLARIFICATION_LABELS[args.status] ?? 'NO PROPORCIONADO';

          const content = response.content.map(i => {
            if (!i.statusRefund.trim()) {
              return {
                ...i,
                statusRefund: fallbackStatusName,
              };
            }

            return i;
          });

          return {
            ...response,
            content,
          };
        }),
        catchError(err => this.#handleError(err)),
        take(1),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error: unknown) {
      this.loader.hide(idLoader);
      return this.#handleError(error);
    }
  }

  #handleError(error: unknown): Observable<IGetListResponseDto> {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);

      this.notifier.warning({
        title: controlledTitleError,
        message: error.message,
      });
    } else {
      console.error(error);
      this.notifier.warning({
        title: uncontrolledTitleError,
        message:
          (error as any)?.message ||
          'Algo salió mal al obtener el listado de aclaratorias',
      });
    }

    return of(emptyListResponse);
  }
}
