import {
  B2BDateRange,
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import { ClarificationStat } from '../../domain/entities/clarification-stat';
import { ClarificationStatsRepository } from '../../domain/repositories/clarification-stats.repository';
import { IGetStatsRepositoryRequestDto } from '../dtos/get-stats-repository-request.dto';
import { IClarificationSummaryUIDto } from '../dtos/get-stats-ui.dto';
import { ClarificationMapper } from '../mapper/clarification-mapper';

export const controlledClarificationStatsTitleError =
  'Hemos detectado un error en la consulta de estadísticas de aclaratorias';

export const uncontrolledClarificationStatsTitleError =
  'Parece que algo salió mal,';

export const emptyStatsResponse: IClarificationSummaryUIDto = {
  approved: {
    refundAmount: 0,
    totalRefunds: 0,
  },
  requested: {
    refundAmount: 0,
    totalRefunds: 0,
  },
};

export class GetClarificationsStatsUseCase
  implements BaseUsecase<B2BDateRange, Observable<IClarificationSummaryUIDto>>
{
  private repository: ClarificationStatsRepository<
    IGetStatsRepositoryRequestDto,
    Observable<ClarificationStat[]>
  >;
  private loader: LoaderService;
  private notifier: NotifierService;

  constructor(
    repository: ClarificationStatsRepository<
      IGetStatsRepositoryRequestDto,
      Observable<ClarificationStat[]>
    >,
    loader: LoaderService,
    notifier: NotifierService
  ) {
    this.repository = repository;
    this.loader = loader;
    this.notifier = notifier;
  }

  execute(args: B2BDateRange): Observable<IClarificationSummaryUIDto> {
    const idLoader = this.loader.show();

    try {
      const request =
        ClarificationMapper.fromStatsUIRequestToRepositoryRequest(args);

      return this.repository.getStats(request).pipe(
        take(1),
        map(ClarificationMapper.fromStatsResponseToUI),
        catchError(err => this.handleError(err)),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error: unknown) {
      this.loader.hide(idLoader);
      return this.handleError(error);
    }
  }

  private handleError(error: unknown): Observable<IClarificationSummaryUIDto> {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);

      this.notifier.warning({
        title: controlledClarificationStatsTitleError,
        message: error.message,
      });
    } else {
      console.error(error);
      this.notifier.warning({
        title: uncontrolledClarificationStatsTitleError,
        message:
          (error as any)?.message ||
          'Algo salió mal al obtener las estadísticas de aclaratorias',
      });
    }

    return of(emptyStatsResponse);
  }
}
