export const CLARIFICATION_STATUS = {
  approved: 'APPROVED',
  requested: 'REQUESTED',
  rejected: 'REJECTED',
  customerEvidence: 'CUSTOMER_EVIDENCE',
  merchantEvidence: 'MERCHANT_EVIDENCE',
  investigation: 'INVESTIGATION',
} as const;

export const CLARIFICATION_LABELS = {
  approved: 'APROBADO',
  requested: 'SOLICITADO',
  rejected: 'RECHAZADO',
  customerEvidence: 'EVIDENCIA USUARIO',
  merchantEvidence: 'EVIDENCIA MERCHANT',
  investigation: 'EN REVISIÓN',
} as const;

export type ClarificationStatusKey = keyof typeof CLARIFICATION_STATUS;
export type ClarificationStatus =
  (typeof CLARIFICATION_STATUS)[ClarificationStatusKey];

export const CLARIFICATION_TYPES = {
  partial: 'AP',
  total: 'A',
} as const;

export type ClarificationTypeKey = keyof typeof CLARIFICATION_TYPES;
export type ClarificationType =
  (typeof CLARIFICATION_TYPES)[ClarificationTypeKey];
