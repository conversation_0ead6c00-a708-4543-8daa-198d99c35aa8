@if (vm$ | async; as context) {
  <section class="px-4 md:px-8 pt-4">
    <h3 class="font-semibold text-center md:text-start mb-2">
      {{ context.titleTextUI.title }}
    </h3>

    <div
      class="flex flex-col md:flex-row flex-wrap gap-x-3 items-center mt-2 md:-mt-4 pt-4 pb-2">
      <aplz-ui-form-datepicker
        [formControl]="dateControl"
        [rangeEnabled]="true"
        [maxDate]="todayRawDateDayFirst"
        [centerText]="true"></aplz-ui-form-datepicker>

      <div class="flex items-center">
        <aplz-ui-select
          [formControl]="clarificationStatusControl"
          label="mostrar">
          @for (opt of clarificationStatus; track opt) {
            <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
          }
        </aplz-ui-select>
      </div>
    </div>
  </section>
  <section
    class="-mt-4 pt-8 md:pt-0 px-4 md:px-8 grid gap-4 grid-cols-1 md:grid-cols-2 2xl:grid-cols-4">
    @for (stat of context.clarificationsStats; track stat) {
      <aplz-ui-metric-card>
        <aplz-ui-metric-card-header>
          <div
            class="aplazo-metric-card__label"
            [class.tooltip--with-space]="
              stat.helpTooltip && stat.tooltipSpaceActive
            ">
            <span
              class="aplazo-metric-card__label-title text-base"
              [aplzTooltip]="stat.statCardTitle"
              aplzTooltipPosition="top">
              {{ stat.statCardTitle }}
            </span>
            @if (stat.helpTooltip) {
              <aplz-ui-icon
                name="question-mark-circle"
                size="xs"
                [aplzTooltip]="stat.helpTooltip ?? ''"></aplz-ui-icon>
            }
          </div>
        </aplz-ui-metric-card-header>
        <aplz-ui-metric-card-content>
          <div class="text-2xl font-semibold mb-1">
            {{ stat.value | aplzDynamicPipe: stat.pipeName }}
          </div>
        </aplz-ui-metric-card-content>
      </aplz-ui-metric-card>
    }
  </section>

  <div
    class="px-4 md:px-8 mt-4 md:mt-0 flex flex-col md:flex-row items-center justify-center md:justify-between">
    <h5 class="text-center lg:text-left md:mt-8">
      @if (context.counterLabelTextUI; as counterLabel) {
        <span class="whitespace-nowrap font-medium text-base">
          {{ context.clarificationsCounting | i18nPlural: counterLabel }}
        </span>
      }
    </h5>
  </div>

  <section class="px-4 md:px-8 pb-16">
    @if (context.hasClarifications) {
      <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
        <table aplzSimpleTable aria-label="Clarifications List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.clarificationsTableTextUI.loanId }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.clarificationsTableTextUI.refundAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.clarificationsTableTextUI.merchantCancelId }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center"
              colspan="1">
              {{ context.clarificationsTableTextUI.cartId }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center"
              colspan="2">
              {{ context.clarificationsTableTextUI.reason }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center"
              colspan="1">
              {{ context.clarificationsTableTextUI.created }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.clarificationsTableTextUI.statusRefund }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.clarificationsTableTextUI.requestType }}
            </th>
          </tr>

          @for (item of context.clarifications; track item) {
            <tr aplzSimpleTableBodyRow>
              <td
                aplzSimpleTableBodyCell
                class="font-semibold tabular-nums text-center">
                {{ item.loanId }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.refundAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.merchantCancelId }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center" colspan="1">
                <p
                  class="truncate max-w-28 mx-auto"
                  [aplzTooltip]="item.cartId">
                  {{ item.cartId }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="text-center" colspan="2">
                <p class="min-w-32 font-semibold">
                  {{ item.reason }}
                </p>
              </td>
              <td
                aplzSimpleTableBodyCell
                class="tabular-nums text-center"
                colspan="1">
                {{ item.created | aplzDynamicPipe: 'date' }}
              </td>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.statusRefund }}
              </td>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.requestType === 'A' ? 'Total' : 'Parcial' }}
              </td>
            </tr>
          }
        </table>
      </div>
      <div class="my-3">
        <aplz-ui-pagination
          [totalPages]="context.pagesByClarificationsCounting"
          [currentPage]="context.currentPage"
          (selectedPage)="changePage($event)">
        </aplz-ui-pagination>
      </div>
    } @else {
      <div class="bg-light py-4 rounded-lg shadow-md overflow-x-auto">
        <aplz-ui-common-message
          [i18Text]="{
            title: context.emptyListTextUI.title,
            description: context.emptyListTextUI.description
          }"
          imgName="emptyLoans">
        </aplz-ui-common-message>
      </div>
    }
  </section>
}
