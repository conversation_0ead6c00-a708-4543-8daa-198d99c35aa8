import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import {
  LoaderService,
  NotifierService,
  TemporalService,
} from '@aplazo/merchant/shared';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconRegistryService,
  AplazoIconComponent,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { iconDownloadTray, iconSearch, iconXMark } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  map,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { EventManagerService } from '../../../services/event-manger.service';
import { IGetListRepositoryRequestDto } from '../application/dtos/get-list-repository-request.dto';
import { IGetListResponseDto } from '../application/dtos/get-list-response.dto';
import { IGetStatsRepositoryRequestDto } from '../application/dtos/get-stats-repository-request.dto';
import { GetClarificationListUsecase } from '../application/usecases/get-list.usecase';
import { GetClarificationsStatsUseCase } from '../application/usecases/get-stats.usecase';
import { ClarificationStat } from '../domain/entities/clarification-stat';
import { ClarificationListRepository } from '../domain/repositories/clarification-list.repository';
import { ClarificationStatsRepository } from '../domain/repositories/clarification-stats.repository';
import { ClarificationStatusKey } from '../domain/validation';
import { CriteriaClarificationsService } from './services/criteria-clarifications.service';

interface IClarificationTextUI {
  loanId: string;
  saleRefund: string;
  refundAmount: string;
  merchantCancelId: string;
  cartId: string;
  reason: string;
  created: string;
  statusRefund: string;
  requestType: string;
}

interface IClarificationsStatsTextUI {
  requested: {
    refundAmount: IStatCardInjectedTextUI;
    totalRefunds: IStatCardInjectedTextUI;
  };
  approved: {
    refundAmount: IStatCardInjectedTextUI;
    totalRefunds: IStatCardInjectedTextUI;
  };
}
interface IStatCardInjectedTextUI {
  statCardTitle: string;
  helpTooltip?: string;
}

interface ICounterI18n {
  empty: string;
  singular: string;
  plural: string;
}

export const CLARIFICATION_STATUS: Record<string, ClarificationStatusKey> = {
  'Evidencia Merchant': 'merchantEvidence',
  'En revisión': 'investigation',
  Aprobadas: 'approved',
} as const;

@Component({
  selector: 'app-clarifications',
  templateUrl: './clarifications.component.html',
  providers: [CriteriaClarificationsService],
  imports: [
    AsyncPipe,
    I18nPluralPipe,
    ReactiveFormsModule,
    I18nPluralPipe,
    AplazoDynamicPipe,
    AplazoPaginationComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoTooltipDirective,
    AplazoSelectComponents,
    AplazoFormDatepickerComponent,
    AplazoMetricCardComponents,
    AplazoIconComponent,
  ],
})
export class ClarificationsComponent implements OnInit, OnDestroy {
  readonly #eventManager = inject(EventManagerService);
  readonly #notifier = inject(NotifierService);
  readonly #loader = inject(LoaderService);
  readonly #listRepository: ClarificationListRepository<
    IGetListRepositoryRequestDto,
    Observable<IGetListResponseDto>
  > = inject(ClarificationListRepository);
  readonly #statsRepository: ClarificationStatsRepository<
    IGetStatsRepositoryRequestDto,
    Observable<ClarificationStat[]>
  > = inject(ClarificationStatsRepository);
  readonly #criteria = inject(CriteriaClarificationsService);
  readonly #i18n = inject(I18NService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #temporal = inject(TemporalService);
  readonly #scope = 'clarifications';

  readonly #destroy$ = new Subject<void>();

  readonly #listUsecase = new GetClarificationListUsecase(
    this.#listRepository,
    this.#loader,
    this.#notifier
  );
  readonly #statsUsecase = new GetClarificationsStatsUseCase(
    this.#statsRepository,
    this.#loader,
    this.#notifier
  );

  clarificationsTableTextUI$: Observable<IClarificationTextUI> =
    this.#i18n.getTranslateObjectByKey<IClarificationTextUI>({
      key: 'list',
      scope: this.#scope,
    });
  clarificationsStatsTextUI$: Observable<IClarificationsStatsTextUI> =
    this.#i18n.getTranslateObjectByKey<IClarificationsStatsTextUI>({
      key: 'stats',
      scope: this.#scope,
    });
  emptyListTextUI$: Observable<{
    title: string;
    description: string;
  }> = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptyList',
    scope: this.#scope,
  });
  titleTextUI$: Observable<{ title: string }> =
    this.#i18n.getTranslateObjectByKey<{ title: string }>({
      key: 'header',
      scope: this.#scope,
    });
  counterLabelTextUI$: Observable<Record<string, string>> = this.#i18n
    .getTranslateObjectByKey<ICounterI18n>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      }))
    );

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;
  sevenDaysAgoRawDateDayFirst = this.#temporal.sevenDaysAgo;

  clarificationStatus = Object.keys(CLARIFICATION_STATUS);

  clarificationStatusControl = new FormControl<string>('', {
    nonNullable: true,
  });

  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  readonly #clarificationsCounting$ = new BehaviorSubject<number>(0);

  clarificationsCounting$ = this.#clarificationsCounting$.pipe(
    takeUntil(this.#destroy$)
  );
  pagesByClarificationsCounting$ = this.#clarificationsCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  dateRange$ = this.#criteria.getDateRange$();
  currentPage$ = this.#criteria.getPageNum$();

  clarifications$ = this.#criteria.getClarificationsCriteria$().pipe(
    switchMap(criteria =>
      this.#listUsecase
        .execute({
          dateRange: criteria.dateRange,
          pageNum: criteria.pageNum,
          pageSize: criteria.pageSize,
          status: criteria.status,
        })
        .pipe(take(1))
    ),
    tap(response => {
      this.#clarificationsCounting$.next(response.totalElements);
    }),
    map(response => response.content),
    map(items => [...items].sort((a, b) => b.loanId - a.loanId)),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  hasClarifications$ = this.clarifications$.pipe(
    map(items => items.length > 0),
    takeUntil(this.#destroy$)
  );

  readonly #statCardKeys = ['refundAmount', 'totalRefunds'];

  clarificationsStats$ = this.#criteria.getClarificationsCriteria$().pipe(
    switchMap(criteria =>
      this.#statsUsecase.execute({
        endDate: criteria.dateRange.endDate,
        startDate: criteria.dateRange.startDate,
      })
    ),
    combineLatestWith(this.clarificationsStatsTextUI$.pipe(take(1))),
    map(([stats, i18nText]) => {
      // requested status is for not rejected non approved status
      const refundStatsStatus = ['requested', 'approved'];
      // Iterate over refund status to get the
      // data from usecase response
      //
      return refundStatsStatus.flatMap(status => {
        // iterate over each status
        // to map into each card info
        return this.#statCardKeys.map(key => {
          const pipeName: ValidDynamicPipesNames = ['totalRefunds'].includes(
            key
          )
            ? 'decimal'
            : 'currency';
          return {
            isDarkMode: false,
            statCardKey: key,
            statCardTitle: i18nText[status][key].statCardTitle,
            value: String(stats[status][key]),
            helpTooltip: i18nText[status][key].helpTooltip,
            tooltipSpaceActive:
              i18nText[status][key].tooltipSpaceActive ?? false,
            pipeName,
          };
        });
      });
    }),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    clarificationsStats: this.clarificationsStats$,
    clarificationsCounting: this.clarificationsCounting$,
    hasClarifications: this.hasClarifications$,
    clarificationsTableTextUI: this.clarificationsTableTextUI$,
    clarificationsStatsTextUI: this.clarificationsStatsTextUI$,
    emptyListTextUI: this.emptyListTextUI$,
    titleTextUI: this.titleTextUI$,
    counterLabelTextUI: this.counterLabelTextUI$,
    clarifications: this.clarifications$,
    pagesByClarificationsCounting: this.pagesByClarificationsCounting$,
    currentPage: this.currentPage$,
    dateRange: this.dateRange$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconSearch, iconXMark, iconDownloadTray]);
  }

  changePage(page: number): void {
    this.#criteria.setPageNum(page);
    this.#eventManager.sendTrackEvent('pagination', { pageNum: page });
  }

  ngOnInit(): void {
    this.#criteria
      .getStatus$()
      .pipe(take(1))
      .subscribe(status => {
        const key = Object.entries(CLARIFICATION_STATUS).find(
          ([, value]) => value === status
        )[0];

        this.clarificationStatusControl.setValue(key);

        if (this.clarificationStatus.length <= 1) {
          this.clarificationStatusControl.disable();
        } else {
          this.clarificationStatusControl.enable();
        }
      });

    this.clarificationStatusControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(statusId => {
        const status = CLARIFICATION_STATUS[statusId];
        this.#criteria.setStatus(status);
        this.#eventManager.sendTrackEvent('status', { status });
      });

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(val => {
          if (Array.isArray(val) && val.length === 2) {
            const [start, end] = val;
            const startDate = this.#temporal.formatRawDateDayFirst(start);
            const endDate = this.#temporal.formatRawDateDayFirst(end);

            this.#criteria.setDateRange({ startDate, endDate });
            this.#eventManager.sendTrackEvent('dateRange', {
              startDate,
              endDate,
            });
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
