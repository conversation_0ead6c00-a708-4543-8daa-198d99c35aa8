import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { ClarificationListRepository } from '../../domain/repositories/clarification-list.repository';
import { ClarificationStatsRepository } from '../../domain/repositories/clarification-stats.repository';
import { GetClarificationsListRepository } from '../repositories/get-list-imp.repository';
import { GetClarificationsStatsRepository } from '../repositories/get-stats-impl.repository';

export function provideClarificationsRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: ClarificationListRepository,
      useClass: GetClarificationsListRepository,
    },
    {
      provide: ClarificationStatsRepository,
      useClass: GetClarificationsStatsRepository,
    },
  ]);
}
