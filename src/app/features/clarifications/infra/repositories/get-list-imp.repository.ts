import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IGetListRepositoryRequestDto } from '../../application/dtos/get-list-repository-request.dto';
import { IGetListResponseDto } from '../../application/dtos/get-list-response.dto';
import { ClarificationListRepository } from '../../domain/repositories/clarification-list.repository';

@Injectable({ providedIn: 'root' })
export class GetClarificationsListRepository
  implements
    ClarificationListRepository<
      IGetListRepositoryRequestDto,
      Observable<IGetListResponseDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getList(args: IGetListRepositoryRequestDto): Observable<IGetListResponseDto> {
    return this.#http.get<IGetListResponseDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v2/merchant/refund/page`,
      {
        params: {
          listType: args.type,
          listStatus: args.status,
          pageNum: args.pageNum.toString(),
          pageSize: args.pageSize.toString(),
          startDate: args.startDate,
          endDate: args.endDate,
        },
      }
    );
  }
}
