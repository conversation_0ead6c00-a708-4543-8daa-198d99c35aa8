import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IGetStatsRepositoryRequestDto } from '../../application/dtos/get-stats-repository-request.dto';
import { ClarificationStat } from '../../domain/entities/clarification-stat';
import { ClarificationStatsRepository } from '../../domain/repositories/clarification-stats.repository';

@Injectable({ providedIn: 'root' })
export class GetClarificationsStatsRepository
  implements
    ClarificationStatsRepository<
      IGetStatsRepositoryRequestDto,
      Observable<ClarificationStat[]>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getStats(
    args: IGetStatsRepositoryRequestDto
  ): Observable<ClarificationStat[]> {
    return this.#http.get<ClarificationStat[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v2/merchant/refund/summary`,
      {
        params: {
          startDate: args.startDate,
          endDate: args.endDate,
          listStatus: args.status,
          listType: args.type,
        },
      }
    );
  }
}
