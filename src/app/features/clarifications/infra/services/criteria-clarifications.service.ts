import { inject, Injectable } from '@angular/core';
import { B2BDateRange, TemporalService } from '@aplazo/merchant/shared';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { ClarificationStatusKey } from '../../domain/validation';
import { CLARIFICATION_STATUS } from '../clarifications.component';

export interface ICriteriaClarificationsDto {
  pageNum: number;
  pageSize: number;
  dateRange: B2BDateRange;
  status: ClarificationStatusKey;
}

@Injectable({ providedIn: 'root' })
export class CriteriaClarificationsService {
  readonly #temporal = inject(TemporalService);
  readonly #initialCriteriaClarifications: ICriteriaClarificationsDto = {
    pageNum: 0,
    pageSize: 10,
    dateRange: {
      startDate: this.#temporal.sevenDaysAgo,
      endDate: this.#temporal.todayRawDayFirst,
    },
    status: CLARIFICATION_STATUS['Evidencia Merchant'],
  } as const;

  readonly #clarificationsCriteria$ =
    new BehaviorSubject<ICriteriaClarificationsDto>(
      this.#initialCriteriaClarifications
    );

  readonly initialClarificationsCriteria = Object.seal(
    Object.freeze(this.#initialCriteriaClarifications)
  );

  getClarificationsCriteria$(): Observable<ICriteriaClarificationsDto> {
    return this.#clarificationsCriteria$.asObservable();
  }

  getPageNum$(): Observable<number> {
    return this.#clarificationsCriteria$.pipe(
      map(criteria => criteria.pageNum)
    );
  }

  getPageSize$(): Observable<number> {
    return this.#clarificationsCriteria$.pipe(
      map(criteria => criteria.pageSize)
    );
  }

  getDateRange$(): Observable<B2BDateRange> {
    return this.#clarificationsCriteria$.pipe(
      map(criteria => criteria.dateRange)
    );
  }

  getDateRangeSync(): B2BDateRange {
    return this.#clarificationsCriteria$.getValue().dateRange;
  }

  getStatus$(): Observable<string> {
    return this.#clarificationsCriteria$.pipe(map(criteria => criteria.status));
  }

  setPageNum(pageNum: number): void {
    if (this.#clarificationsCriteria$.value.pageNum == pageNum) {
      return;
    }

    this.#clarificationsCriteria$.next({
      ...this.#clarificationsCriteria$.value,
      pageNum,
    });
  }

  setPageSize(pageSize: number): void {
    if (this.#clarificationsCriteria$.value.pageSize == pageSize) {
      return;
    }

    this.#clarificationsCriteria$.next({
      ...this.#clarificationsCriteria$.value,
      pageSize,
      pageNum: 0,
    });
  }

  setDateRange(dateRange: B2BDateRange): void {
    if (
      this.#clarificationsCriteria$.value.dateRange.startDate ===
        dateRange.startDate &&
      this.#clarificationsCriteria$.value.dateRange.endDate ===
        dateRange.endDate
    ) {
      return;
    }

    this.#clarificationsCriteria$.next({
      ...this.#clarificationsCriteria$.value,
      pageNum: 0,
      pageSize: 10,
      dateRange,
    });
  }

  setStatus(status: ClarificationStatusKey): void {
    if (this.#clarificationsCriteria$.value.status === status) {
      return;
    }

    this.#clarificationsCriteria$.next({
      ...this.#clarificationsCriteria$.value,
      pageNum: 0,
      pageSize: 10,
      status,
    });
  }

  clearCriteria(): void {
    this.#clarificationsCriteria$.next(this.#initialCriteriaClarifications);
  }
}
