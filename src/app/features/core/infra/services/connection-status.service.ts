import {
  Injectable,
  inject,
  InjectionToken,
  Optional,
  Inject,
} from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { Observable, fromEvent, map, merge, of, startWith } from 'rxjs';

export interface ConnectionStatusConfig {
  notifierText?: {
    title?: string;
    message?: string;
  };
  position?: string;
}

export const CONNECTION_STATUS_CONFIG =
  new InjectionToken<ConnectionStatusConfig>('CONNECTION_STATUS_CONFIG');

@Injectable({
  providedIn: 'root',
})
export class ConnectionStatusService {
  readonly #notifier = inject(NotifierService);
  readonly #config: ConnectionStatusConfig;

  readonly #online$ = merge(
    of(navigator.onLine),
    fromEvent(window, 'online').pipe(map(() => true)),
    fromEvent(window, 'offline').pipe(map(() => false))
  ).pipe(startWith(navigator.onLine));

  constructor(
    @Optional()
    @Inject(CONNECTION_STATUS_CONFIG)
    config: ConnectionStatusConfig = {}
  ) {
    this.#config = {
      notifierText: {
        title: config.notifierText?.title || 'Sin conexión a internet',
        message:
          config.notifierText?.message ||
          'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
      },
      position: config.position || 'toast-bottom-left',
    };

    this.#online$.subscribe(isOnline => {
      if (!isOnline) {
        this.#notifier.warning({
          title: this.#config.notifierText?.title,
          message: this.#config.notifierText?.message,
        });
      }
    });
  }

  /**
   * Observable that emits the current online status
   */
  public get online$(): Observable<boolean> {
    return this.#online$;
  }
}
