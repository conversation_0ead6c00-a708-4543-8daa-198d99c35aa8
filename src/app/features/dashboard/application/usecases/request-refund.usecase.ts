import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, EMPTY, finalize, Observable, take, tap } from 'rxjs';
import {
  RequestRefundRequest,
  RequestRefundResponse,
} from '../../domain/entities/refund';
import { RefundsRepository } from '../../domain/repositories/request-refund.repository';

export const successRefundRequestMessage =
  'Devolución solicitada exitosamente, revisa reporte de devoluciones para más detalles';

export const cancelledRefundRequestMessage =
  'Lo sentimos , este Pedido ya se encuentra en estado Cancelado, por lo que no puede procesar la devoución';

@Injectable({ providedIn: 'root' })
export class RequestRefundUseCase
  implements
    BaseUsecase<RequestRefundRequest, Observable<RequestRefundResponse>>
{
  readonly #repository = inject(RefundsRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: RequestRefundRequest): Observable<RequestRefundResponse> {
    const idLoader = this.#loader.show();

    try {
      const mappedErrors = Object.keys(args)
        .map(key => {
          return Guard.againstNullOrUndefined(args, key);
        })
        .filter(({ succeeded }) => {
          return !succeeded;
        });

      if (mappedErrors.length > 0) {
        throw new RuntimeMerchantError(
          mappedErrors.map(({ message }) => message).join('; '),
          'RequestRefundUseCase::execute::invalidArgs'
        );
      }

      Guard.againstInvalidNumbers(
        args.totalAmount,
        'RequestRefundUseCase::execute::invalidTotalAmount'
      );

      if (args.totalAmount <= 0) {
        throw new RuntimeMerchantError(
          'El monto total debe ser mayor a cero',
          'RequestRefundUseCase::execute::totalAmountLessThanZero'
        );
      }

      return this.#repository
        .createOne({
          cartId: args.cartId,
          totalAmount: args.totalAmount,
          reason: '[MP]: ' + args.reason,
        })
        .pipe(
          tap(resp => {
            if (resp.refundStatus === 'REQUESTED') {
              this.#notifier.success({
                title: successRefundRequestMessage,
              });
            }

            if (resp.refundStatus === 'CANCELLED') {
              this.#notifier.info({
                title: cancelledRefundRequestMessage,
              });
            }
          }),
          catchError(e => {
            if (
              e instanceof HttpErrorResponse &&
              e.status === 400 &&
              e.error.code === 'INVALID_REFUND' &&
              e.error.data.error
                .toLowerCase()
                .includes(
                  'amount requested or sum of the refunded amount exceeds'
                )
            ) {
              throw new RuntimeMerchantError(
                'La devolución solicitada o la suma del monto devuelto excede el monto total del préstamo',
                'RequestRefundUseCase::execute::exceededTotalAmount'
              );
            }

            if (
              e instanceof HttpErrorResponse &&
              e.status === 400 &&
              e.error.code === 'LOAN_DONT_EXISTS'
            ) {
              throw new RuntimeMerchantError(
                'El pedido no existe',
                'RequestRefundUseCase::execute::cartIdNotFound'
              );
            }

            throw e;
          }),

          catchError(e => {
            return this.#errorHandler.handle(e, EMPTY);
          }),

          take(1),

          finalize(() => {
            this.#loader.hide(idLoader);
          })
        );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(error, EMPTY);
    }
  }
}
