import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  ViewEncapsulation,
} from '@angular/core';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { map } from 'rxjs';
import { SelectedLoanStore } from '../../../../services/selected-loan.store';

@Component({
  selector: 'app-loan-details',
  template: `
    @if (loanInfo$ | async; as loan) {
      <table aplzSimpleTable aria-label="Loan Details">
        <tr aplzSimpleTableHeaderRow>
          <th scope="col" class="min-w-24">Fecha</th>
          <th scope="col" class="min-w-24" colspan="2">ID</th>
          <th scope="col" class="min-w-24">IVA</th>
          <th scope="col" class="min-w-24">MONTO</th>
        </tr>
        <tr aplzSimpleTableBodyRow class="min-h-8">
          <td class="min-w-24">
            {{ loan?.creationDate | aplzDynamicPipe: 'shortDate' }}
          </td>
          <td class="min-w-24 tabular-nums" colspan="2">
            {{ loan?.id }}
          </td>
          <td class="min-w-24 tabular-nums">
            {{ loan?.fees | aplzDynamicPipe: 'currency' }}
          </td>
          <td class="min-w-24 tabular-nums">
            {{ loan?.total | aplzDynamicPipe: 'currency' }}
          </td>
        </tr>
      </table>
    }
  `,
  imports: [AplazoSimpleTableComponents, AplazoDynamicPipe, AsyncPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class LoanDetailsComponent {
  readonly #selectedLoanStore = inject(SelectedLoanStore);

  readonly loanInfo$ = this.#selectedLoanStore.loan$.pipe(map(l => l?.loan));
}
