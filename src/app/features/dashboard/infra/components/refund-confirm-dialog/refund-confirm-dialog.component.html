<aplz-ui-card size="full" appearance="borderless" class="p-6">
  <ng-container *ngTemplateOutlet="currentContent"> </ng-container>
</aplz-ui-card>

<ng-template #step1>
  <h2 class="text-xl font-medium text-center mt-4">
    Ingrese los datos para solicitar la devolución
  </h2>
  <h3 class="text-base text-center font-light my-3">
    Pedido {{ (selectedLoan$ | async)?.loan.id }}
  </h3>
  <h3 class="text-base text-center font-light mb-6">
    Monto total:
    <strong>
      {{ (selectedLoan$ | async)?.loan.total | aplzDynamicPipe: 'currency' }}
    </strong>
  </h3>

  <form [formGroup]="form" class="flex flex-col gap-4 p-4 items-center w-auto">
    <aplz-ui-form-field class="w-full md:w-3/4">
      <aplz-ui-form-label>Monto </aplz-ui-form-label>
      <input
        type="text"
        aplzFormInput
        formControlName="amount"
        mask="separator.2"
        placeholder="0.00"
        class="w-full" />

      <span
        class="text-lg font-medium pl-2 text-dark-secondary"
        aplzInputPrefix>
        $
      </span>

      <p aplzFormHint>Ej. 300.00</p>
      <ng-container aplzFormError>
        @if (amount.touched && amount.hasError('required')) {
          <p>El monto de la devolución es requerido</p>
        }
      </ng-container>

      <ng-container aplzFormError>
        @if (
          amount.touched &&
          !amount.hasError('required') &&
          amount.hasError('min')
        ) {
          <p>El monto mínimo de devolución es 1</p>
        }
      </ng-container>

      <ng-container aplzFormError>
        @if (
          amount.touched &&
          !amount.hasError('required') &&
          !amount.hasError('min') &&
          amount.hasError('max')
        ) {
          <p>
            El monto máximo de devolución es
            {{ (selectedLoan$ | async)?.loan.total }}
          </p>
        }
      </ng-container>
    </aplz-ui-form-field>

    <aplz-ui-form-field class="w-full md:w-3/4">
      <aplz-ui-form-label>Motivo </aplz-ui-form-label>
      <input
        type="text"
        aplzFormInput
        formControlName="reason"
        class="w-full" />
      <p aplzFormHint>Ej. Paquete nunca llego</p>

      <ng-container aplzFormError>
        @if (reason.touched && reason.hasError('required')) {
          <p>La razón de la devolución es requerida</p>
        }
      </ng-container>
    </aplz-ui-form-field>

    <div class="flex justify-end items-center w-full gap-4 mt-6">
      <button
        aplzButton
        aplzAppearance="stroked"
        size="md"
        type="button"
        (click)="close()">
        Cancelar
      </button>

      <button
        aplzButton
        aplzAppearance="solid"
        aplzColor="dark"
        size="md"
        type="button"
        (click)="stepConfirmation()">
        Continuar
      </button>
    </div>
  </form>
</ng-template>

<ng-template #step2>
  <h2 class="text-xl text-center mt-4">
    ¿Estás seguro deseas solicitar la devolución de
    <strong class="ml-2">
      {{ amount.value | aplzDynamicPipe: 'currency' }}
    </strong>
    MXN para el pedido
    <strong class="ml-2">
      {{ (selectedLoan$ | async)?.loan.id }}
    </strong>
    ?
  </h2>

  <h3 class="text-lg text-center font-light mt-3">
    Motivo:
    <strong class="mx-2">
      {{ reason.value }}
    </strong>
  </h3>

  <h3 class="text-lg text-left text-special-danger font-light mt-6">
    **Este proceso no se puede deshacer
  </h3>

  <div class="flex justify-end items-center w-full gap-4 mt-12 mb-6">
    <button
      aplzButton
      aplzAppearance="stroked"
      size="md"
      type="button"
      (click)="stepForm()">
      Regresar
    </button>

    <button
      aplzButton
      aplzAppearance="solid"
      aplzColor="dark"
      size="md"
      type="button"
      (click)="applyRefund()">
      Solicitar
    </button>
  </div>
</ng-template>
