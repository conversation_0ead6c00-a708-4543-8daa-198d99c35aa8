import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { NgxMaskDirective } from 'ngx-mask';
import { shareReplay, take } from 'rxjs';
import { SelectedLoanStore } from '../../../../../services/selected-loan.store';

@Component({
  selector: 'app-refund-confirm-dialog',
  templateUrl: './refund-confirm-dialog.component.html',
  imports: [
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
    NgxMaskDirective,
    AplazoDynamicPipe,
    AsyncPipe,
    NgTemplateOutlet,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class RefundConfirmDialogComponent implements OnInit {
  readonly #selectedLoanStore = inject(SelectedLoanStore);
  readonly #dialogRef: DialogRef<
    any,
    { confirm: boolean; data?: { amount: number; reason: string } }
  > = inject(DialogRef);

  @ViewChild('step1', { read: TemplateRef, static: true })
  __step1!: TemplateRef<any>;

  @ViewChild('step2', { read: TemplateRef, static: true })
  __step2!: TemplateRef<any>;

  currentContent: TemplateRef<any> | null = null;

  readonly amount = new FormControl('');

  readonly reason = new FormControl('', [Validators.required]);

  readonly form = new FormGroup({
    amount: this.amount,
    reason: this.reason,
  });

  readonly selectedLoan$ = this.#selectedLoanStore.loan$.pipe(shareReplay(1));

  stepConfirmation(): void {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.currentContent = this.__step2;
    }
  }

  stepForm(): void {
    this.currentContent = this.__step1;

    this.form.markAllAsTouched();
  }

  applyRefund(): void {
    if (this.form.valid) {
      this.#dialogRef.close({
        confirm: true,
        data: {
          amount: Number(this.amount.value),
          reason: this.reason.value,
        },
      });
      this.form.reset();
    }
  }

  close(): void {
    this.#dialogRef.close({ confirm: false });
  }

  ngOnInit(): void {
    this.selectedLoan$.pipe(take(1)).subscribe(loan => {
      if (loan) {
        this.amount.setValidators([
          Validators.required,
          Validators.min(1),
          Validators.max(loan.loan.total),
        ]);
        this.amount.updateValueAndValidity();
      }
    });

    this.currentContent = this.__step1;
  }
}
