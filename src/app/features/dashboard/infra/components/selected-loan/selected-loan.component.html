@if (vm$ | async; as context) {
  <div class="bg-light px-4 md:px-6 pt-2 pb-4 rounded-lg shadow">
    <article
      class="flex flex-wrap gap-4 py-6 items-center justify-end w-full mb-8">
      @if (context.loan?.customer) {
        <section
          class="max-w-full flex-grow flex-shrink-0 items-center"
          >
          <h3 class="font-semibold text-md">
            {{ context.loan?.customer?.name ?? '' }}
          </h3>
          <div class="mt-2">
            <span class="tabular-nums">
              {{ context.loan.customer?.phoneNumber ?? '' }}
            </span>
            @if (context.loan?.customer?.email) {
              <span> &middot; </span>
            }
            <span> {{ context.loan?.customer?.email ?? '' }} </span>
          </div>
        </section>
      }
      @if (context.loan) {
        <span
          class="font-semibold inline-block px-5 py-3 rounded-lg"
        [class.bg-special-success]="
          context.loan?.loan?.status?.toLowerCase() === 'aprobado'
        "
        [class.bg-special-warning]="
          ['cancelado', 'no completado'].includes(
            context.loan?.loan?.status?.toLowerCase()
          )
        ">
          {{ context.loan?.loan?.status }}
        </span>
      }
    </article>
    <div class="overflow-x-auto">
      <app-loan-details></app-loan-details>
    </div>
    <span class="mt-8 mb-10 flex w-full h-0.5 bg-dark/20"></span>
    <div class="overflow-auto max-h-80">
      <table aplzSimpleTable aria-label="Products List">
        <tr aplzSimpleTableHeaderRow>
          <th scope="col" class="min-w-24">ID</th>
          <th scope="col" class="min-w-24" colspan="2">Producto</th>
          <th scope="col" class="min-w-24 text-right">Precio</th>
        </tr>
        @for (prod of context.loan.products; track prod) {
          <tr aplzSimpleTableBodyRow>
            <td class="min-w-24 tabular-nums">
              {{ prod.id }}
            </td>
            <td class="min-w-24 tabular-nums" colspan="2">
              {{ prod.title }}
            </td>
            <td class="min-w-24 tabular-nums text-right">
              {{ prod.price | aplzDynamicPipe : 'currency' }}
            </td>
          </tr>
        }
      </table>
    </div>
    @if (context.loan?.loan?.status.toLocaleLowerCase() === 'aprobado') {
      <span class="mt-8 mb-10 flex w-full h-0.5 bg-dark/20"></span>
      @if ((context.refunds?.length ?? 0) > 0) {
        <div
          class="overflow-auto max-h-80 mt-8"
          >
          <h2 class="text-xl font-medium my-4">Devoluciones</h2>
          <table aplzSimpleTable aria-label="refund List">
            <tr aplzSimpleTableHeaderRow>
              <th scope="col" class="min-w-24">Fecha</th>
              <th scope="col" class="min-w-24" colspan="2">
                Monto de devolución
              </th>
              <th scope="col" class="min-w-24 text-right">Status</th>
            </tr>
            @for (refunds of context.refunds; track refunds) {
              <tr aplzSimpleTableBodyRow>
                <td class="min-w-24 tabular-nums">
                  {{ refunds.created }}
                </td>
                <td class="min-w-24 tabular-nums" colspan="2">
                  {{ refunds.refundAmount }}
                </td>
                <td class="min-w-24 tabular-nums text-right">
                  {{ refunds.statusRefund }}
                </td>
              </tr>
            }
          </table>
        </div>
      }
      <div class="pt-6 pb-10" *aplazoIfValidRole="['ROLE_PANEL_ADMIN']">
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          class="w-full"
          (click)="requestRefund()">
          Solicitar devolución
        </button>
      </div>
    }
  </div>
}
