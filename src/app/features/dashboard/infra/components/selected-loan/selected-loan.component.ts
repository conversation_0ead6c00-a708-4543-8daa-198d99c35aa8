import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Output,
} from '@angular/core';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { combineLatest } from 'rxjs';
import { SelectedLoanStore } from '../../../../../services/selected-loan.store';
import { IfValidRoleDirective } from '../../../../shared/directives/if-valid-role.directive';
import { LoanDetailsComponent } from '../loan-details.component';

@Component({
  selector: 'app-selected-loan',
  templateUrl: './selected-loan.component.html',
  imports: [
    LoanDetailsComponent,
    IfValidRoleDirective,
    AplazoSimpleTableComponents,
    AplazoButtonComponent,
    AplazoDynamicPipe,
    AsyncPipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectedLoanComponent {
  @Output()
  refundRequested = new EventEmitter<void>();

  readonly #selectedLoanStore = inject(SelectedLoanStore);

  readonly vm$ = combineLatest({
    loan: this.#selectedLoanStore.loan$,
    refunds: this.#selectedLoanStore.refunds$,
  });

  requestRefund(): void {
    this.refundRequested.emit();
  }
}
