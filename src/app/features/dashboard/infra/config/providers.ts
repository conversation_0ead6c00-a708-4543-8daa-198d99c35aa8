import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { RequestRefundUseCase } from '../../application/usecases/request-refund.usecase';
import { RefundsRepository } from '../../domain/repositories/request-refund.repository';
import { RequestRefundWithHttpRepository } from '../repositories/refunds-with-http.repository';

export function provideDashboardRefundsDeps(): EnvironmentProviders {
  return makeEnvironmentProviders([
    RequestRefundUseCase,
    {
      provide: RefundsRepository,
      useClass: RequestRefundWithHttpRepository,
    },
  ]);
}
