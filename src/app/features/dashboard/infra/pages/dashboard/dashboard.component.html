@if (vm$ | async; as context) {
  <section class="bg-dark-background pt-4 pb-8 px-4 md:px-8">
    <h3 class="font-semibold mb-2">
      <span>
        {{ context.summaryComparisonLabelInjectedText.title }}
      </span>
      <span class="ml-3 uppercase">
        {{ currentMonthYear }}
      </span>
    </h3>

    <div class="grid gap-2 grid-cols-1 md:grid-cols-2 2xl:grid-cols-5">
      @for (stat of context.comparisonStatistics; track stat) {
        <aplz-ui-metric-card>
          <aplz-ui-metric-card-header>
            <div
              class="aplazo-metric-card__label"
              [class.tooltip--with-space]="
                stat.helpTooltip && stat.tooltipSpaceActive
              ">
              <span class="aplazo-metric-card__label-title">
                {{ stat.statCardTitle }}
              </span>
              @if (stat.helpTooltip) {
                <aplz-ui-icon
                  name="question-mark-circle"
                  size="xs"
                  [aplzTooltip]="stat.helpTooltip ?? ''"></aplz-ui-icon>
              }
            </div>
          </aplz-ui-metric-card-header>
          <aplz-ui-metric-card-content>
            <div class="text-2xl font-semibold mb-1">
              {{ stat.value | aplzDynamicPipe: stat.pipeName }}
            </div>
            @if (stat.comparison) {
              <p class="aplazo-stats-grid__comparison-base mt-6">
                <span
                  class="aplazo-stats-grid__comparison-percent"
                  [class.stat-danger]="+stat.comparison <= 0"
                  [class.stat-success]="+stat.comparison > 0">
                  {{ stat.comparison | aplzDynamicPipe: 'percent' }}
                </span>
                <span class="aplazo-stats-grid__comparison-close">
                  vs mes anterior
                </span>
              </p>
            }
          </aplz-ui-metric-card-content>
        </aplz-ui-metric-card>
      }

      @if (accountStatementStats$ | async; as accountStatementStats) {
        <aplz-ui-metric-card>
          <aplz-ui-metric-card-header>
            <div
              class="aplazo-metric-card__label"
              [class.tooltip--with-space]="
                accountStatementStats.helpTooltip &&
                accountStatementStats.tooltipSpaceActive
              ">
              <span class="aplazo-metric-card__label-title">
                {{ accountStatementStats.statCardTitle }}
              </span>
              @if (accountStatementStats.helpTooltip) {
                <aplz-ui-icon
                  name="question-mark-circle"
                  size="xs"
                  [aplzTooltip]="
                    accountStatementStats.helpTooltip ?? ''
                  "></aplz-ui-icon>
              }
            </div>
          </aplz-ui-metric-card-header>
          <aplz-ui-metric-card-content>
            <div class="text-2xl font-semibold mb-1">
              {{ accountStatementStats.value | aplzDynamicPipe: 'currency' }}
            </div>
            <div class="text-sm text-gray-600 mt-6">
              Monto pagado en el último corte:
              <span class="font-semibold text-dark-primary">
                {{
                  accountStatementStats.principalAmount
                    | aplzDynamicPipe: 'currency'
                }}
              </span>
            </div>
          </aplz-ui-metric-card-content>
          <aplz-ui-metric-card-footer>
            <div class="flex gap-2 justify-end">
              <button
                [aplzDropdownTriggerFor]="downloadMenu"
                class="download-button"
                [attr.aria-label]="accountStatementStats.mainActionLabel"
                (click)="openReportsByMonthDownloadDropdown()">
                {{ accountStatementStats.mainActionLabel }}
              </button>
              <aplz-ui-dropdown #downloadMenu>
                @if (
                  downloadAllReceiptsTextUI$ | async;
                  as monthOptionsTextUI
                ) {
                  <aplz-ui-dropdown-item>
                    <button (click)="downloadSummaryReport()">
                      {{ monthOptionsTextUI.paymentsLabel }}
                    </button>
                  </aplz-ui-dropdown-item>
                  <aplz-ui-dropdown-item>
                    <button (click)="downloadAllReceipts()">
                      {{ monthOptionsTextUI.receiptsLabel }}
                    </button>
                  </aplz-ui-dropdown-item>
                  <aplz-ui-dropdown-item>
                    <button (click)="downloadAllInvoices()">
                      {{ monthOptionsTextUI.invoiceLabel }}
                    </button>
                  </aplz-ui-dropdown-item>
                }
              </aplz-ui-dropdown>
            </div>
          </aplz-ui-metric-card-footer>
        </aplz-ui-metric-card>
      }
    </div>

    <div class="mt-8">
      <aplz-ui-banner-announcement
        [content]="context.banner"
        (ctaEvent)="goToCta(context.banner)"></aplz-ui-banner-announcement>
    </div>
  </section>
  <section class="pt-4 px-4 md:px-8">
    <h3 class="font-semibold">
      {{ context.summaryDynamicsLabelInjectedText.title }}
    </h3>

    <div class="flex gap-x-3 items-center py-2">
      <aplz-ui-form-datepicker
        [formControl]="dateControl"
        [rangeEnabled]="true"
        [maxDate]="todayRawDateDayFirst"
        [centerText]="true"></aplz-ui-form-datepicker>

      <aplz-ui-select [formControl]="orderTypeControl" label="mostrar">
        @for (opt of context.orderTypes; track opt) {
          <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
        }
      </aplz-ui-select>
    </div>

    <section class="grid gap-2 md:grid-cols-2 2xl:grid-cols-4">
      @for (stat of context.dynamicStatistics; track stat) {
        <aplz-ui-metric-card>
          <aplz-ui-metric-card-header>
            <div
              class="aplazo-metric-card__label"
              [class.tooltip--with-space]="
                stat.helpTooltip && stat.tooltipSpaceActive
              ">
              <span
                class="aplazo-metric-card__label-title text-base"
                [aplzTooltip]="stat.statCardTitle"
                aplzTooltipPosition="top">
                {{ stat.statCardTitle }}
              </span>
              @if (stat.helpTooltip) {
                <aplz-ui-icon
                  name="question-mark-circle"
                  size="xs"
                  [aplzTooltip]="stat.helpTooltip ?? ''"></aplz-ui-icon>
              }
            </div>
          </aplz-ui-metric-card-header>
          <aplz-ui-metric-card-content>
            <div class="text-2xl font-semibold mb-1">
              {{ stat.value | aplzDynamicPipe: stat.pipeName }}
            </div>
          </aplz-ui-metric-card-content>
        </aplz-ui-metric-card>
      }
    </section>
  </section>

  <div
    class="flex flex-col md:flex-row justify-between items-center w-full min-h-fit px-4 md:px-8">
    <div class="text-center lg:text-left">
      @if (counterLabelTextTemplate$ | async; as counterLabelTextTemplate) {
        <span class="whitespace-nowrap font-medium text-lg">
          {{
            context.loansCounting | i18nPlural: context.counterLabelTextTemplate
          }}
        </span>
      }
    </div>

    <div class="md:mt-8 max-w-full md:max-w-[310px]">
      <aplz-ui-search
        [textUI]="context.searchbarInjectedText"
        [minLength]="minLength"
        [formControl]="searchControl"
        [trimSpaces]="true">
      </aplz-ui-search>
    </div>
  </div>

  <section class="flex flex-wrap gap-4 px-4 md:px-8">
    <div class="flex-grow-[999] basis-0">
      @if (context.hasLoans) {
        <div>
          <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
            <table aplzSimpleTable aria-label="Loans List">
              <tr aplzSimpleTableHeaderRow>
                <th aplzSimpleTableHeaderCell scope="col">
                  {{ context.loansListInjectedText.id }}
                </th>
                <th aplzSimpleTableHeaderCell class="text-center" scope="col">
                  {{ context.loansListInjectedText.status }}
                </th>
                <th aplzSimpleTableHeaderCell class="text-center" scope="col">
                  {{ context.loansListInjectedText.amount }}
                </th>
                <th aplzSimpleTableHeaderCell scope="col"></th>
              </tr>

              @for (loan of context.loans; track loan) {
                <tr
                  aplzSimpleTableBodyRow
                  (bodyRowClick)="selectLoan(loan)"
                  class="cursor-pointer"
                  [selected]="(selectedLoan$ | async)?.loan.id === loan.id">
                  <td aplzSimpleTableBodyCell class="font-semibold">
                    <p>
                      {{ loan.creationDate | aplzDynamicPipe: 'shortDate' }}
                    </p>
                    <p>
                      {{ loan.id }}
                    </p>
                  </td>
                  <td aplzSimpleTableBodyCell class="text-center">
                    <span
                      class="font-semibold inline-block px-5 py-3 rounded-lg whitespace-nowrap"
                      [class.bg-special-success]="
                        loan.status.toLowerCase() === 'aprobado'
                      "
                      [class.bg-special-warning]="
                        ['cancelado', 'no completado'].includes(
                          loan.status.toLowerCase()
                        )
                      ">
                      {{ loan.status }}
                    </span>
                  </td>
                  <td aplzSimpleTableBodyCell class="text-center tabular-nums">
                    {{ loan.total | aplzDynamicPipe: 'currency' }}
                  </td>
                  <td aplzSimpleTableBodyCell class="">
                    <aplz-ui-icon name="chevron-right" size="xs">
                    </aplz-ui-icon>
                  </td>
                </tr>
              }
            </table>
          </div>
          <div class="my-4">
            @if (!context.hasSearch) {
              <aplz-ui-pagination
                [totalPages]="context.pagesByLoansCounting"
                [currentPage]="context.currentPage"
                (selectedPage)="changePage($event)">
              </aplz-ui-pagination>
            }
          </div>
        </div>
      } @else {
        <aplz-ui-card appearance="shadow">
          <div class="py-4 md:py-7 lg:py-10">
            @if (context.hasSearch) {
              <aplz-ui-common-message
                [i18Text]="{
                  title: context.emptySearchInjectedText.title,
                  description: context.emptySearchInjectedText.description
                }"
                imgName="emptyLoans">
              </aplz-ui-common-message>
            } @else {
              <aplz-ui-common-message
                [i18Text]="{
                  title: context.emptyLoansInjectedText.title,
                  description: context.emptyLoansInjectedText.description
                }"
                imgName="emptyLoans">
                <aplz-ui-message-actions>
                  <button
                    aplzButton
                    aplzAppearance="solid"
                    aplzColor="dark"
                    size="md"
                    [rounded]="true"
                    class="mx-auto min-w-56"
                    (click)="clickEmptyLoansButton()">
                    {{ context.emptyLoansInjectedText.button?.label }}
                  </button>
                </aplz-ui-message-actions>
              </aplz-ui-common-message>
            }
          </div>
        </aplz-ui-card>
      }
    </div>

    @if (hasSelectedLoan()) {
      <div class="flex-grow basis-96">
        <app-selected-loan (refundRequested)="requestRefund()">
        </app-selected-loan>
      </div>
    }
  </section>
}
