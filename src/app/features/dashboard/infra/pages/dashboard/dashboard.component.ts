import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import { Component, inject, OnDestroy, OnInit } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import {
  NotifierService,
  RedirectionService,
  StatusGroupingId,
  TemporalService,
} from '@aplazo/merchant/shared';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import {
  BannerAnnouncementComponent,
  DynamicBannerContentUI,
} from '@aplazo/shared-ui/banner-announcement';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
  AplazoStatsComponent,
  StatsItemContentUI,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { iconChevronRight } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  EMPTY,
  filter,
  lastValueFrom,
  map,
  MonoTypeOperatorFunction,
  Observable,
  pipe,
  shareReplay,
  startWith,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
  zip,
} from 'rxjs';
import { CriteriaBalanceService } from 'src/app/features/balance/infra/services/criteria-balance.service';
import { GetActiveBannerUsecase } from 'src/app/features/dynamic-banners/application/get-active-banner.usecase';
import { GetLoansStatementSummaryUseCase } from 'src/app/features/loan/application/usecases/loans-statement-summary.usecase';
import { RefundSearchUseCase } from 'src/app/features/refund/application/usecases/refund-search.usecase';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { EventManagerService } from '../../../../../services/event-manger.service';
import { SelectedLoanStore } from '../../../../../services/selected-loan.store';
import { SharedCriteria } from '../../../../../services/shared-criteria.store';
import { PaymentBalanceAllInvoicesDownloadUsecase } from '../../../../balance/application/usecases/payment-balance-all-invoices-download.usecase';
import { PaymentBalanceAllReceiptsDownloadUseCase } from '../../../../balance/application/usecases/payment-balance-all-receipts-download.usecase';
import { PaymentBalanceSummaryReportUsecase } from '../../../../balance/application/usecases/payment-balance-summary-report.usecase';
import { LoanDetailsUseCase } from '../../../../loan/application/usecases/loan-details.usecase';
import { MerchantLoansBySearchUseCase } from '../../../../loan/application/usecases/loans-by-search.usecase';
import { MerchantLoansComparisonStatisticsUseCase } from '../../../../loan/application/usecases/loans-comparison-statistics.usecase';
import { MerchantLoansListUseCase } from '../../../../loan/application/usecases/loans-list.usecase';
import { MerchantLoansStatisticsUseCase } from '../../../../loan/application/usecases/loans-statistics.usecase';
import { MerchantLoan } from '../../../../loan/domain/entities/merchant-loan';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../../shared/shared-criteria';
import { UserStoreService } from '../../../../user/src/application/services/user-store.service';
import { RequestRefundUseCase } from '../../../application/usecases/request-refund.usecase';
import { RefundConfirmDialogComponent } from '../../components/refund-confirm-dialog/refund-confirm-dialog.component';
import { SelectedLoanComponent } from '../../components/selected-loan/selected-loan.component';

declare global {
  interface Window {
    hj(...args: any): void;
  }
}

const ORDER_TYPES: Readonly<Record<string, StatusGroupingId>> = {
  'Todos los pedidos': 'all',
  Aprobados: 'approved',
  'No completados': 'cancelled',
} as const;

type ExtendedStatsItem = StatsItemContentUI & {
  statCardKey?: string;
  principalAmount?: number;
  downloadOption?: Array<{ label: string; value: string; action: string }>;
  mainActionLabel?: string;
  mainActionDisableds?: boolean;
};

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  imports: [
    AsyncPipe,
    I18nPluralPipe,
    ReactiveFormsModule,
    AplazoDynamicPipe,
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoFormDatepickerComponent,
    AplazoFormFieldDirectives,
    AplazoIconComponent,
    AplazoSearchInputComponent,
    AplazoSelectComponents,
    AplazoStatsComponent,
    AplazoCommonMessageComponents,
    AplazoPaginationComponent,
    AplazoSimpleTableComponents,
    AplazoDropdownComponents,
    SelectedLoanComponent,
    AplazoSearchInputComponent,
    AplazoFormDatepickerComponent,
    AplazoMetricCardComponents,
    AplazoTooltipDirective,
    BannerAnnouncementComponent,
  ],
})
export class DashboardComponent implements OnInit, OnDestroy {
  readonly #criteria = inject(SharedCriteria);
  readonly #notifier = inject(NotifierService);
  readonly #redirecter = inject(RedirectionService);
  readonly #eventManager = inject(EventManagerService);
  readonly #i18n = inject(I18NService);
  readonly #balanceCriteria = inject(CriteriaBalanceService);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #temporal = inject(TemporalService);
  readonly #dialog = inject(DialogService);
  readonly #selectedLoanStore = inject(SelectedLoanStore);
  readonly #requestRefundUsecase = inject(RequestRefundUseCase);
  readonly #searchRefundsUseCase = inject(RefundSearchUseCase);
  readonly #downloadAllReceipts = inject(
    PaymentBalanceAllReceiptsDownloadUseCase
  );
  readonly #downloadAllInvoicesUseCase = inject(
    PaymentBalanceAllInvoicesDownloadUsecase
  );
  readonly #balanceSummaryReportUsecase = inject(
    PaymentBalanceSummaryReportUsecase
  );
  readonly #userStore = inject(UserStoreService);
  readonly #loansListUseCase = inject(MerchantLoansListUseCase);
  readonly #loansSearchByIdPhoneEmailUseCase = inject(
    MerchantLoansBySearchUseCase
  );
  readonly #getLoansStatementSummaryUseCase = inject(
    GetLoansStatementSummaryUseCase
  );
  readonly #loansStatisticsUseCase = inject(MerchantLoansStatisticsUseCase);
  readonly #loansComparisonStatisticsUseCase = inject(
    MerchantLoansComparisonStatisticsUseCase
  );
  readonly #loanDetailsInfoUseCase = inject(LoanDetailsUseCase);
  readonly #getBanner = inject(GetActiveBannerUsecase);

  readonly #destroy$ = new Subject<void>();

  readonly #scope = 'dashboard';
  readonly #scopeBalance = 'balance';

  readonly downloadAllReceiptsTextUI$ = this.#i18n
    .getTranslateObjectByKey<{
      paymentsLabel: string;
      receiptsLabel: string;
      invoiceLabel: string;
    }>({
      key: 'downloadAllReceipts',
      scope: this.#scopeBalance,
    })
    .pipe(
      map(content => {
        if (!content || !content.paymentsLabel || !content.receiptsLabel) {
          return null;
        }

        return {
          paymentsLabel: content.paymentsLabel,
          receiptsLabel: content.receiptsLabel,
          invoiceLabel: content.invoiceLabel,
        };
      })
    );

  readonly minLength = 3;

  readonly accountStatementStats$ = this.#getLoansStatementSummaryUseCase
    .execute()
    .pipe(
      map(summary => ({
        statCardTitle: 'Estado de cuenta',
        value: summary.nextPayment,
        isDarkMode: false,
        pipeName: 'currency' as const,
        helpTooltip: 'Total a pagar en el próximo corte',
        tooltipSpaceActive: true,
        mainActionLabel: 'Descargar',
        principalAmount: summary.latestPayment,
        downloadOption: [
          {
            label: 'Descargar Transacciones',
            value: 'transactions',
            action: 'transactions',
          },
          { label: 'Descargar Recibos', value: 'receipts', action: 'receipts' },
          {
            label: 'Descargar Facturas',
            value: 'invoices',
            action: 'invoices',
          },
        ],
      }))
    );

  readonly #refresh$ = new Subject<void>();

  readonly selectedLoan$ = this.#selectedLoanStore.loan$.pipe(
    takeUntil(this.#destroy$)
  );

  hasSelectedLoan = () => this.#selectedLoanStore.hasSelectedLoan;

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;
  currentMonthYear = this.#temporal.currentMonthYear;

  readonly searchControl = new FormControl<string>('');
  readonly #searchControlFac = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });
  hasSearch$ = this.searchControl.valueChanges.pipe(
    this.#searchControlFac.hasActiveSearch()
  );
  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  readonly #comparisonStats$ = this.#i18n
    .getTranslateObjectByKey<{
      title: string;
    }>({
      key: 'comparisonStats',
      scope: this.#scope,
    })
    .pipe(shareReplay(1));

  readonly #dynamicStats$ = this.#i18n
    .getTranslateObjectByKey<{
      title: string;
    }>({
      key: 'dynamicStats',
      scope: this.#scope,
    })
    .pipe(shareReplay(1));

  loansComparisonStatsInjectedText$ = this.#comparisonStats$.pipe(
    map(t => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { title, ...rest } = t;

      return rest;
    })
  );
  summaryComparisonLabelInjectedText$ = this.#comparisonStats$.pipe(
    map(t => ({ title: t.title }))
  );
  summaryDynamicsLabelInjectedText$ = this.#dynamicStats$.pipe(
    map(t => ({ title: t.title }))
  );
  loansStatsInjectedText$ = this.#dynamicStats$.pipe(
    map(t => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { title, ...rest } = t;

      return rest;
    })
  );
  searchbarInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: this.#scope,
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  loansListInjectedText$ = this.#i18n
    .getTranslateObjectByKey<{
      cards: {
        id: string;
        status: string;
        amount: string;
      };
    }>({
      key: 'loansList',
      scope: this.#scope,
    })
    .pipe(map(l => l.cards));
  emptyLoansInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button: {
      label: string;
    };
  }>({
    key: 'emptyLoans',
    scope: this.#scope,
  });
  emptySearchInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: this.#scope,
  });
  allLoanLabels$ = this.#i18n.getTranslateObjectByKey<{
    enabled: boolean;
  }>({
    key: 'allLoanStatusLabels',
    scope: this.#scope,
  });
  counterLabelTextTemplate$: Observable<Record<string, string>> = this.#i18n
    .getTranslateObjectByKey<{
      empty: string;
      singular: string;
      plural: string;
    }>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      }))
    );

  orderTypes$ = this.allLoanLabels$.pipe(
    map(({ enabled }) => {
      if (enabled) {
        return Object.keys(ORDER_TYPES);
      }
      return Object.keys(ORDER_TYPES).filter(key => key.includes('Aprobados'));
    }),
    take(1)
  );

  orderTypeControl = new FormControl<string>('', {
    nonNullable: true,
  });

  currentPage$ = this.#criteria.page$;

  readonly #statCardKeys = [
    'salesAmount',
    'salesOrder',
    'customers',
    'salesAmountAvg',
  ];

  comparisonStatistics$: Observable<ExtendedStatsItem[]> =
    this.#criteria.criteria$.pipe(
      this.#filterSearchStream(),
      combineLatestWith(this.#refresh$.pipe(startWith(undefined))),
      switchMap(([criteria]) =>
        this.#loansComparisonStatisticsUseCase.execute(criteria).pipe(take(1))
      ),
      combineLatestWith(this.loansComparisonStatsInjectedText$.pipe(take(1))),
      map(([comparison, i18nText]) => {
        const currentMonthStats = comparison.currentMonth;
        const differenceInPercet = comparison.comparisonStats;

        return this.#statCardKeys.map(key => {
          const pipeName: ValidDynamicPipesNames = [
            'salesOrder',
            'customers',
          ].includes(key)
            ? 'decimal'
            : 'currency';
          return {
            isDarkMode: true,
            statCardKey: key,
            statCardTitle: i18nText[key].statCardTitle,
            value: String(currentMonthStats[key]),
            helpTooltip: i18nText[key].helpTooltip,
            tooltipSpaceActive: i18nText[key].tooltipSpaceActive ?? false,
            pipeName: pipeName as ValidDynamicPipesNames,
            comparison: String(differenceInPercet[key]),
          };
        });
      }),
      takeUntil(this.#destroy$)
    );

  dynamicStatistics$: Observable<ExtendedStatsItem[]> =
    this.#criteria.criteria$.pipe(
      this.#filterSearchStream(),
      combineLatestWith(this.#refresh$.pipe(startWith(undefined))),
      switchMap(([criteria]) =>
        this.#loansStatisticsUseCase.execute(criteria).pipe(take(1))
      ),
      combineLatestWith(this.loansStatsInjectedText$.pipe(take(1))),
      map(([stats, i18nText]) => {
        return this.#statCardKeys.map(key => {
          const pipeName: ValidDynamicPipesNames = [
            'salesOrder',
            'customers',
          ].includes(key)
            ? 'decimal'
            : 'currency';

          return {
            isDarkMode: false,
            statCardKey: key,
            statCardTitle: i18nText[key].statCardTitle,
            value: String(stats[key]),
            helpTooltip: i18nText[key].helpTooltip,
            tooltipSpaceActive: i18nText[key].tooltipSpaceActive ?? false,
            pipeName,
          };
        });
      }),
      takeUntil(this.#destroy$)
    );

  loans$ = this.#criteria.criteria$.pipe(
    this.#filterSearchStream(),
    combineLatestWith(this.#refresh$.pipe(startWith(undefined))),
    switchMap(([criteria]) => {
      if (criteria.element) {
        return this.#loansSearchByIdPhoneEmailUseCase.execute(criteria).pipe(
          take(1),
          tap(loans => {
            this.#loansCounting$.next(loans.length);
          })
        );
      }

      return this.#loansListUseCase.execute(criteria).pipe(
        take(1),
        tap(data => {
          this.#loansCounting$.next(data.totalElements);
        }),
        map(loansList => loansList.content)
      );
    }),
    map(list => {
      return list.map(loan => {
        if (loan.status.toLowerCase() === 'cancelado') {
          return {
            ...loan,
            status: 'NO COMPLETADO',
          };
        }

        return loan;
      });
    }),
    map(loans => {
      return [...loans].sort((a, b) => b.id - a.id);
    }),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  hasLoans$ = this.loans$.pipe(
    map(loans => loans.length > 0),
    takeUntil(this.#destroy$)
  );

  readonly #loansCounting$ = new BehaviorSubject<number>(0);

  loansCounting$ = this.#loansCounting$.pipe(takeUntil(this.#destroy$));
  pagesByLoansCounting$ = this.#loansCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  readonly banner$ = this.#userStore.merchantId$.pipe(
    switchMap(merchantId => this.#getBanner.execute({ merchantId })),
    take(1)
  );

  readonly vm$ = combineLatest({
    loans: this.loans$,
    loansCounting: this.loansCounting$,
    pagesByLoansCounting: this.pagesByLoansCounting$,
    hasLoans: this.hasLoans$,
    currentPage: this.currentPage$,
    downloadAllReceiptsTextUI: this.downloadAllReceiptsTextUI$,
    accountStatementStats: this.accountStatementStats$,
    comparisonStatistics: this.comparisonStatistics$,
    dynamicStatistics: this.dynamicStatistics$,
    searchbarInjectedText: this.searchbarInjectedText$,
    loansListInjectedText: this.loansListInjectedText$,
    emptyLoansInjectedText: this.emptyLoansInjectedText$,
    emptySearchInjectedText: this.emptySearchInjectedText$,
    allLoanLabels: this.allLoanLabels$,
    counterLabelTextTemplate: this.counterLabelTextTemplate$,
    orderTypes: this.orderTypes$,
    selectedLoan: this.selectedLoan$,
    summaryComparisonLabelInjectedText:
      this.summaryComparisonLabelInjectedText$,
    summaryDynamicsLabelInjectedText: this.summaryDynamicsLabelInjectedText$,
    hasSearch: this.hasSearch$,
    banner: this.banner$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegistry.registerIcons([iconChevronRight]);
  }

  selectLoan(loan: MerchantLoan): void {
    this.#loanDetailsInfoUseCase
      .execute({
        customerUrl: loan.customerUrl,
        productUrl: loan.productUrl,
      })
      .pipe(
        map(({ customer, products }) => {
          return {
            loan: { ...loan },
            customer,
            products,
          };
        }),
        switchMap(details =>
          this.#searchRefundsUseCase.execute(String(details.loan.id)).pipe(
            map(refunds => ({
              ...details,
              refunds,
            }))
          )
        ),
        tap(() => {
          this.#eventManager.sendTrackEvent('selection', { loanId: loan.id });
        }),
        take(1)
      )
      .subscribe(({ refunds, ...details }) => {
        this.#selectedLoanStore.setInfo(details);
        this.#selectedLoanStore.setRefunds(refunds);
      });
  }

  changePage(page: number): void {
    this.#criteria.setPageNum(page);
    this.#eventManager.sendTrackEvent('pagination', { pageNum: page });
  }

  clickEmptyLoansButton(): void {
    const howItWorksLandingUrl = `${this.#environment.redirectionsLandingPage}/para-comercios`;
    this.#redirecter.externalNavigation(howItWorksLandingUrl, '_blank');
  }

  async requestRefund(): Promise<void> {
    this.#eventManager.sendTrackEvent('refundButtonClick', {
      buttonName: 'openRequestRefund',
      category: 'refund_interaction',
      label: 'refund_button_click',
      url: window.location.href,
      timestamp: Date.now(),
    });

    const dialog = this.#dialog.open(RefundConfirmDialogComponent, {
      enableClose: false,
    });

    const dialogResult = await lastValueFrom(dialog.afterClosed$.pipe(take(1)));

    if (!dialogResult?.confirm) {
      this.#notifier.info({
        title: 'Proceso de devolución cancelado',
      });
      return void 0;
    }

    const selected = await lastValueFrom(
      this.#selectedLoanStore.loan$.pipe(take(1))
    );

    this.#requestRefundUsecase
      .execute({
        cartId: selected.loan.cartId,
        totalAmount: dialogResult.data.amount,
        reason: dialogResult.data.reason,
      })
      .subscribe(resp => {
        if (resp.refundStatus === 'CANCELLED') {
          this.#refresh$.next();
          this.#selectedLoanStore.clearLoan();
          return;
        }

        if (window.hj) {
          window.hj('event', 'refund_success');
        }

        this.#eventManager.sendTrackEvent('applyRefundEvent', {
          buttonName: 'applyRefundBtn',
          category: 'refund_interaction',
          label: 'refund_confirmed',
          url: window.location.href,
          timestamp: Date.now(),
        });
      });
  }

  goToCta(content: DynamicBannerContentUI): void {
    if (content.ctaText && content.ctaUrl) {
      this.#eventManager.sendTrackEvent('buttonClick', {
        buttonName: 'mp_buttonbanner_click',
        genericInfo: content.ctaUrl,
        label: content.ctaText,
        loanId: content?.id ?? 0,
        timestamp: new Date().getTime(),
      });
    }

    if (content?.isInternalRedirect) {
      this.#redirecter.internalNavigation(content!.ctaUrl ?? '');
    } else {
      this.#redirecter.externalNavigation(content?.ctaUrl ?? '', '_blank');
    }
  }

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        startWith(this.searchControl.value),
        this.#searchControlFac.debouncedValue(),
        takeUntil(this.#destroy$)
      )
      .subscribe(value => {
        this.#selectedLoanStore.clearLoan();
        this.#criteria.setSearch(value);
        if (value?.trim()) {
          this.#eventManager.sendTrackEvent('search', { searchTerm: value });
        }
      });

    this.#criteria.status$.pipe(take(1)).subscribe(status => {
      const key = Object.entries(ORDER_TYPES).find(
        ([, value]) => value === status
      )[0];

      this.orderTypeControl.setValue(key);
    });

    this.orderTypeControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(statusId => {
        const status = ORDER_TYPES[statusId];
        this.#criteria.setStatus(status);
        this.#eventManager.sendTrackEvent('status', { status });
        this.#selectedLoanStore.clearLoan();
      });

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(val => {
          if (Array.isArray(val) && val.length === 2) {
            const [start, end] = val;
            const startDate = this.#temporal.formatRawDateDayFirst(start);
            const endDate = this.#temporal.formatRawDateDayFirst(end);

            this.#criteria.setDateRange({ startDate, endDate });
            this.#eventManager.sendTrackEvent('dateRange', {
              startDate,
              endDate,
            });
            this.#selectedLoanStore.clearLoan();
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
    this.#selectedLoanStore.clearLoan();
  }

  #filterSearchStream(): MonoTypeOperatorFunction<SharedCriteriaWithBranchOfficesUIDto> {
    return pipe(
      filter(
        criteria =>
          criteria.element != null &&
          (criteria.element.length === 0 ||
            criteria.element.length >= this.minLength)
      )
    );
  }

  downloadSummaryReport(): void {
    zip([
      this.#userStore.merchantId$,
      this.#balanceCriteria.getDate$(),
      this.#balanceCriteria.getStatus$(),
    ])
      .pipe(
        switchMap(([merchantId, date, status]) =>
          this.#balanceSummaryReportUsecase.execute({
            merchantId: Number(merchantId),
            date,
            status,
          })
        ),
        take(1),
        tap(() => {
          this.#eventManager.sendTrackEvent(
            'account_statement_quick_access_download_transactions_click',
            {
              buttonName: 'download_transactions_month',
              category: 'dashboard_interaction',
              label:
                'account_statement_quick_access_download_transactions_click',
              url: window.location.href,
              timestamp: Date.now(),
            }
          );
        }),
        catchError(() => {
          return EMPTY;
        })
      )
      .subscribe();
  }

  downloadAllReceipts(): void {
    zip([this.#userStore.merchantId$, this.#balanceCriteria.getDate$()])
      .pipe(
        switchMap(([merchantId, date]) =>
          this.#downloadAllReceipts.execute({
            date,
            merchantId,
          })
        ),
        take(1),
        tap(() => {
          this.#eventManager.sendTrackEvent(
            'account_statement_quick_access_download_receipts_click',
            {
              buttonName: 'download_receipts_month',
              category: 'dashboard_interaction',
              label: 'account_statement_quick_access_download_receipts_click',
              url: window.location.href,
              timestamp: Date.now(),
            }
          );
        }),
        catchError(() => {
          return EMPTY;
        })
      )
      .subscribe();
  }

  downloadAllInvoices(): void {
    zip([this.#userStore.merchantId$, this.#balanceCriteria.getDate$()])
      .pipe(
        switchMap(([merchantId, date]) =>
          this.#downloadAllInvoicesUseCase.execute({
            date,
            merchantId,
          })
        ),
        take(1),
        tap(() => {
          this.#eventManager.sendTrackEvent(
            'account_statement_quick_access_download_invoices_click',
            {
              buttonName: 'download_invoices_month',
              category: 'dashboard_interaction',
              label: 'account_statement_quick_access_download_invoices_click',
              url: window.location.href,
              timestamp: Date.now(),
            }
          );
        }),
        catchError(() => {
          return EMPTY;
        })
      )
      .subscribe();
  }

  openReportsByMonthDownloadDropdown(): void {
    this.#eventManager.sendTrackEvent(
      'account_statement_quick_access_download_button_click',
      {
        buttonName: 'account_statement_download_button',
        category: 'dashboard_interaction',
        label: 'account_statement_quick_access_download_button_click',
        url: window.location.href,
        timestamp: Date.now(),
      }
    );
  }
}
