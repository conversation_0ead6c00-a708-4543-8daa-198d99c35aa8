import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import {
  RequestRefundRequest,
  RequestRefundResponse,
} from '../../domain/entities/refund';
import { RefundsRepository } from '../../domain/repositories/request-refund.repository';

@Injectable({ providedIn: 'root' })
export class RequestRefundWithHttpRepository implements RefundsRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiMicroserviceBaseUrl;

  createOne(request: RequestRefundRequest): Observable<RequestRefundResponse> {
    return this.#http.post<RequestRefundResponse>(
      `${this.#apiUrl}api/v1/merchant/refund`,
      {
        cartId: request.cartId,
        totalAmount: request.totalAmount,
        reason: request.reason,
      }
    );
  }
}
