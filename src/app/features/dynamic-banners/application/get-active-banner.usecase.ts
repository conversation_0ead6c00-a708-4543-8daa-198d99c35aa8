import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { DynamicBannerContentUI } from '@aplazo/shared-ui/banner-announcement';
import { catchError, finalize, map, Observable } from 'rxjs';
import { fromDynamicBannerToDynamicBannerContentUI } from '../domain/entities/banner';
import { DynamicBannerRepository } from '../domain/repositories/dynamic-banner.repository';

@Injectable()
export class GetActiveBannerUsecase extends BaseUsecase<
  {
    merchantId: number;
  },
  Observable<DynamicBannerContentUI | null>
> {
  readonly #repository = inject(DynamicBannerRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: {
    merchantId: number;
  }): Observable<DynamicBannerContentUI | null> {
    const idLoader = this.#loader.show();

    this.#validateRequest(args);

    return this.#repository.getTheOneActiveBanner(args).pipe(
      map(banner => {
        if (banner) {
          return fromDynamicBannerToDynamicBannerContentUI(banner);
        } else {
          return null;
        }
      }),
      catchError(e => this.#errorHandler.handle<never>(e)),
      finalize(() => {
        this.#loader.hide(idLoader);
      })
    );
  }

  #validateRequest(request: { merchantId: number }): void {
    if (!Guard.againstNullOrUndefined(request, 'merchantId').succeeded) {
      throw new RuntimeMerchantError(
        'El ID del comercio es requerido',
        'GetActiveBannerUsecase::null::merchantId'
      );
    }

    Guard.againstInvalidNumbers(
      request.merchantId,
      'GetActiveBannerUsecase',
      'El ID del comercio debe ser un número válido'
    );

    if (request.merchantId <= 0) {
      throw new RuntimeMerchantError(
        'El ID del comercio debe ser mayor que 0',
        'GetActiveBannerUsecase::invalid::merchantId'
      );
    }
  }
}
