import { DynamicBannerContentUI } from '@aplazo/shared-ui/banner-announcement';

export interface DynamicBanner {
  id: number;
  title: string;
  message: string;
  applyAll: boolean;
  merchantIds: number[];
  branchIds: number[];
  buttonVisible: boolean;
  buttonLabel: string;
  redirectUrl: string;
  bannerVisible: boolean;
  imageUrl: string;
  endAt: string; // ISO 8601
  createdAt: string; // ISO 8601
  updatedAt: string; // ISO 8601
}

export const fromDynamicBannerToDynamicBannerContentUI = (
  banner: DynamicBanner
): DynamicBannerContentUI => {
  return {
    id: banner.id,
    title: banner.title,
    description: banner.message,
    imageUrl: banner.imageUrl,
    showCta: banner.buttonVisible,
    ctaText: banner.buttonLabel,
    ctaUrl: banner.redirectUrl,
    bannerVisible: banner.bannerVisible,
    isInternalRedirect:
      !banner.redirectUrl.startsWith('http') &&
      !banner.redirectUrl.startsWith('www'),
  };
};
