import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from 'src/app/config/merchant-core.environment';
import { DynamicBanner } from '../../domain/entities/banner';
import { DynamicBannerRepository } from '../../domain/repositories/dynamic-banner.repository';

@Injectable({
  providedIn: 'root',
})
export class DynamicBannerWithHttpClientRepository extends DynamicBannerRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #apiUrl = this.#environment.promoApiUrl;

  getTheOneActiveBanner(request: {
    merchantId: number;
  }): Observable<DynamicBanner | null> {
    return this.#http.get<DynamicBanner>(
      `${this.#apiUrl}api/v1/banners?merchantId=${request.merchantId}&platform=MP`
    );
  }
}
