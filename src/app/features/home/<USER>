import { DASH_ROUTES } from '../../config/app-route-core';
import { ValidMerchantRoles } from '../user/src/domain/entities/valid-roles';

const paths = [
  DASH_ROUTES.dashboard,
  DASH_ROUTES.loansDailyStatistics,
  DASH_ROUTES.balance,
  DASH_ROUTES.legacyReport,
  DASH_ROUTES.refunds,
  DASH_ROUTES.report,
  DASH_ROUTES.account,
  DASH_ROUTES.clarifications,
  DASH_ROUTES.integration,
] as const;

export type ValidRoutes = (typeof paths)[number];

const rolesByRoute = new Map<ValidRoutes, ValidMerchantRoles[]>();

rolesByRoute.set(DASH_ROUTES.dashboard, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_SUPPORT',
  'ROLE_PANEL_MARKETING',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.loansDailyStatistics, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_MARKETING',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.balance, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_FINANCE',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.legacyReport, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_FINANCE',
  'ROLE_PANEL_MARKETING',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.refunds, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_FINANCE',
  'ROLE_PANEL_SUPPORT',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.clarifications, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_FINANCE',
  'ROLE_PANEL_SUPPORT',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.report, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_FINANCE',
  'ROLE_ADMIN_INC',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.account, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_MANAGER',
]);
rolesByRoute.set(DASH_ROUTES.integration, [
  'ROLE_MERCHANT',
  'ROLE_PANEL_ADMIN',
  'ROLE_PANEL_MANAGER',
]);

export { rolesByRoute };
