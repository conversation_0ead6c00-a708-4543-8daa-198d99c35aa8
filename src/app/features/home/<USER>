import { Route } from '@angular/router';
import { DASH_ROUTES } from '../../config/app-route-core';
import { provideBranchOfficesRepository } from '../branch-offices/src/infra/config/providers';
import { provideDynamicBanners } from '../dynamic-banners/infra/config/providers';
import { preventNavigationByRole } from './guards/role.guard';
import { HomeComponent } from './pages/home.component';
import { getBranchOffices } from './resolvers/branchOffice.resolver';

export default [
  {
    path: '',
    component: HomeComponent,
    providers: [provideBranchOfficesRepository(), provideDynamicBanners()],
    resolve: {
      branchOffices: getBranchOffices,
    },
    children: [
      {
        path: DASH_ROUTES.dashboard,
        loadChildren: () => import('../dashboard/infra/dashboard.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Panel',
        },
      },
      {
        path: DASH_ROUTES.legacyReport,
        loadComponent: () =>
          import('../legacy-report/legacy-report.component').then(
            stl => stl.LegacyReportComponent
          ),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Reportes',
        },
      },
      {
        path: DASH_ROUTES.report,
        loadChildren: () =>
          import('../payment-settlement/infra/payment-settlement.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Reportes de Pago',
        },
      },
      {
        path: DASH_ROUTES.refunds,
        loadChildren: () => import('../refund/infra/refund.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Reporte de Devoluciones',
        },
      },
      {
        path: DASH_ROUTES.clarifications,
        loadComponent: () =>
          import('../clarifications/infra/clarifications.component').then(
            stl => stl.ClarificationsComponent
          ),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Aclaraciones',
        },
      },
      {
        path: DASH_ROUTES.account,
        loadChildren: () => import('../account/infra/account.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Información del Comercio',
        },
      },
      {
        path: DASH_ROUTES.loansDailyStatistics,
        loadChildren: () =>
          import('../loans-days-statistics/loans-days-statistics.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Ventas por día',
        },
      },
      {
        path: DASH_ROUTES.balance,
        loadChildren: () => import('../balance/infra/balance.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Estado de Cuenta',
        },
      },
      {
        path: DASH_ROUTES.integration,
        loadChildren: () => import('../integration/infra/integration.routes'),
        canActivate: [preventNavigationByRole],
        data: {
          title: 'Integración con APLAZO',
        },
      },

      {
        path: '',
        pathMatch: 'full',
        redirectTo: DASH_ROUTES.dashboard,
      },
    ],
  },
] satisfies Route[];
