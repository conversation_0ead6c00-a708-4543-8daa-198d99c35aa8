import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { take, tap } from 'rxjs';
import { BranchOfficesStore } from '../../../services/branch-offices.store';
import { GetAllBranchOfficesUsecase } from '../../branch-offices/src/application/usecases/get-all-branch-offices.usecase';
import { IBranchOfficeUIDto } from '../../branch-offices/src/domain/entities/branch-office';

export const getBranchOffices: ResolveFn<IBranchOfficeUIDto[]> = () => {
  const branchOfficesStore = inject(BranchOfficesStore);
  const usecase = inject(GetAllBranchOfficesUsecase);

  return usecase.execute().pipe(
    tap(resp => {
      const sortedBranches = [...resp].sort((a, b) => a.id - b.id);
      branchOfficesStore.setBranchOffices(sortedBranches);
    }),

    take(1)
  );
};
