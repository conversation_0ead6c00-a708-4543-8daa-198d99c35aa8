<aplz-ui-dashboard (logoClick)="logoClicked()">
  <aplz-ui-dash-header>
    <nav class="flex w-full justify-between items-center">
      <h2 class="font-medium text-2xl truncate max-w-40 md:max-w-full">
        {{ title$ | async }}
      </h2>

      <button
        aplzButton
        aplzAppearance="basic"
        size="md"
        class="items-center flex-grow-0 flex-shrink-0"
        [aplzDropdownTriggerFor]="logoutMenu">
        <span class="lowercase font-light -mt-1">
          {{ username$ | async }}
        </span>
        <span class="ml-2">
          <aplz-ui-icon name="chevron-down" size="xs"></aplz-ui-icon>
        </span>
      </button>
      <aplz-ui-dropdown #logoutMenu>
        <aplz-ui-dropdown-item>
          <button class="px-4 py-2" (click)="logout()"><PERSON><PERSON><PERSON> se<PERSON></button>
        </aplz-ui-dropdown-item>
      </aplz-ui-dropdown>
    </nav>
  </aplz-ui-dash-header>

  <aplz-ui-dash-sidebar>
    <aplz-ui-details
      *aplazoRoutesByRole="[
        appRoutes.dashboard,
        appRoutes.loansDailyStatistics
      ]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="home" size="sm"></aplz-ui-icon>
          <span class="ml-7">Panel</span>
        </div>
      </summary>
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.dashboard]"
        [routerLink]="[appRoutes.dashboard]">
        Pedidos
      </a>
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.loansDailyStatistics]"
        [routerLink]="[appRoutes.loansDailyStatistics]">
        Ventas por día
      </a>
    </aplz-ui-details>
    <ng-container *stgCheckGate="'b2b_front_panel_integration_screen'">
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.integration]"
        [routerLink]="[appRoutes.integration]"
        iconName="docs">
        Integración
      </a>
    </ng-container>
    <a
      aplzSidenavLink
      *aplazoRoutesByRole="[appRoutes.balance]"
      [routerLink]="[appRoutes.balance]"
      iconName="docs">
      Estado de cuenta
    </a>

    <aplz-ui-details
      *aplazoRoutesByRole="[
        appRoutes.report,
        appRoutes.refunds,
        appRoutes.clarifications
      ]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="docs" size="sm"></aplz-ui-icon>
          <span class="ml-7">Reportes</span>
        </div>
      </summary>
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.legacyReport]"
        [routerLink]="[appRoutes.legacyReport]">
        Reporte de Ventas
      </a>
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.report]"
        [routerLink]="[appRoutes.report]">
        Reporte de Transacciones
      </a>
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.refunds]"
        [routerLink]="[appRoutes.refunds]">
        Reporte de Devoluciones
      </a>
      <a
        aplzSidenavLink
        *aplazoRoutesByRole="[appRoutes.clarifications]"
        [routerLink]="[appRoutes.clarifications]">
        Reporte de Aclaraciones
      </a>
    </aplz-ui-details>
    <a
      aplzSidenavLink
      *aplazoRoutesByRole="[appRoutes.account]"
      [routerLink]="[appRoutes.account]"
      iconName="docs">
      Información del Comercio
    </a>
  </aplz-ui-dash-sidebar>

  <div class="bg-dark/5 min-h-dvh pb-36">
    <div class="h-full max-w-[1920px]">
      <router-outlet></router-outlet>
    </div>
  </div>
</aplz-ui-dashboard>
