import { AsyncPipe } from '@angular/common';
import { Component, OnDestroy, inject } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterLink,
  RouterOutlet,
} from '@angular/router';
import { I18NService } from '@aplazo/i18n';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDashboardComponents } from '@aplazo/shared-ui/dashboard';
import { AplazoDetailsComponents } from '@aplazo/shared-ui/details';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSidenavLinkComponent } from '@aplazo/shared-ui/sidenav';
import {
  iconChevronDown,
  iconDocs,
  iconHome,
  iconMenu1,
} from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigModule, StatsigService } from '@statsig/angular-bindings';
import {
  Subject,
  filter,
  map,
  of,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { UserStoreService } from '../../user/src/application/services/user-store.service';
import { UserLogoutUseCase } from '../../user/src/application/usecases/logout.usecase';
import { RouteByRoleDirective } from '../directives/route-by-role.directive';

@Component({
  selector: 'app-home',
  imports: [
    RouterLink,
    RouterOutlet,
    AsyncPipe,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoDetailsComponents,
    AplazoDropdownComponents,
    AplazoDashboardComponents,
    AplazoSidenavLinkComponent,
    RouteByRoleDirective,
    StatsigModule,
  ],
  templateUrl: './home.component.html',
})
export class HomeComponent implements OnDestroy {
  readonly #redirectionService = inject(RedirectionService);
  readonly #userStore = inject(UserStoreService);
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly #featureFlagsService = inject(StatsigService);
  readonly #i18n = inject(I18NService);
  readonly #scope = 'home';
  readonly #destroy$ = new Subject<void>();
  readonly #matchMedia: AplazoMatchMediaService = inject(
    AplazoMatchMediaService
  );
  readonly #dialog: DialogService = inject(DialogService);
  readonly #registryIconService: AplazoIconRegistryService = inject(
    AplazoIconRegistryService
  );

  readonly #userLogoutUseCase = inject(UserLogoutUseCase);

  isLargeScreen$ = this.#matchMedia.matchLgScreen$;

  logoutInjectedText$ = this.#i18n
    .getTranslateObjectByKey<{
      title: string;
      description: string;
    }>({
      key: 'logoutDialog',
      scope: this.#scope,
    })
    .pipe(takeUntil(this.#destroy$));

  title$ = this.#router.events.pipe(
    filter(event => event instanceof NavigationEnd),
    switchMap(() => this.#route.firstChild.data.pipe(map(data => data.title))),
    startWith(this.#route.snapshot.firstChild?.data?.title || '')
  );

  username$ = this.#userStore.username$;

  appRoutes = DASH_ROUTES;

  constructor() {
    this.#registryIconService.registerIcons([
      iconMenu1,
      iconHome,
      iconDocs,
      iconChevronDown,
    ]);
  }

  logoClicked(): void {
    this.#redirectionService.internalNavigation('/');
  }

  logout(): void {
    this.logoutInjectedText$
      .pipe(
        switchMap(text =>
          this.#dialog
            .open(AplazoConfirmDialogComponent, {
              data: text,
              maxWidth: '320px',
            })
            .afterClosed$.pipe(take(1))
        ),
        switchMap(result => {
          if (result?.confirmation) {
            return this.#userLogoutUseCase
              .execute(`/${DASH_ROUTES.authentication}`)
              .pipe(
                tap(() => {
                  this.#featureFlagsService.updateUserAsync({
                    ...this.#featureFlagsService.getClient()?.getContext().user,
                    userID: undefined,
                    email: undefined,
                    custom: {},
                  });
                })
              );
          }
          return of(result?.confirmation ?? false);
        }),
        take(1)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
