import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { lastValueFrom, take } from 'rxjs';
import { DASH_ROUTES } from '../../../config/app-route-core';
import { UserStoreService } from '../../user/src/application/services/user-store.service';
import { ValidMerchantRoles } from '../../user/src/domain/entities/valid-roles';
import { ValidRoutes, rolesByRoute } from '../routes-by-role';

export const preventNavigationByRole: CanActivateFn = async route => {
  const userStore = inject(UserStoreService);
  const router = inject(Router);

  try {
    const role = (await lastValueFrom(
      userStore.role$.pipe(take(1))
    )) as ValidMerchantRoles;

    const roles = rolesByRoute.get(route.routeConfig.path as ValidRoutes);

    if (!roles.includes(role) && role === 'ROLE_PANEL_FINANCE') {
      return router.parseUrl(`/${DASH_ROUTES.rootApp}/${DASH_ROUTES.report}`);
    }

    if (!roles.includes(role) && role !== 'ROLE_PANEL_FINANCE') {
      return router.parseUrl(
        `/${DASH_ROUTES.rootApp}/${DASH_ROUTES.dashboard}`
      );
    }

    return true;
  } catch (error) {
    console.warn('RoleGuard::unexpectedError::', error);
    return false;
  }
};
