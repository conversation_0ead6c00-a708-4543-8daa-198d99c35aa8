import {
  Directive,
  Input,
  OnInit,
  TemplateRef,
  ViewContainerRef,
  inject,
} from '@angular/core';
import { UserStoreService } from '../../user/src/application/services/user-store.service';
import { ValidMerchantRoles } from '../../user/src/domain/entities/valid-roles';
import { ValidRoutes, rolesByRoute } from '../routes-by-role';

@Directive({
  standalone: true,
  selector: '[aplazoRoutesByRole]',
})
export class RouteByRoleDirective implements OnInit {
  readonly #viewContainerRef = inject(ViewContainerRef);
  readonly #templateRef = inject(TemplateRef);
  readonly #store = inject(UserStoreService);

  @Input('aplazoRoutesByRole')
  set routes(route: ValidRoutes[]) {
    this.#routes = route;
  }
  #routes: ValidRoutes[] = [];

  ngOnInit() {
    const roles = Array.from(
      new Set(
        this.#routes.flatMap(item => rolesByRoute.get(item)).filter(Boolean)
      )
    );
    const currentRole = this.#store.getRole() as ValidMerchantRoles;

    this.#viewContainerRef.clear();

    if (
      roles &&
      Array.isArray(roles) &&
      roles.length > 0 &&
      roles.includes(currentRole)
    ) {
      this.#viewContainerRef.createEmbeddedView(this.#templateRef, {
        $implicit: roles,
      });
    }
  }
}
