import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { StatsigService } from '@statsig/angular-bindings';
import { DASH_ROUTES } from '../../../config/app-route-core';

export const preventNavigationByUnavailableFlag: CanActivateFn = (
  route
): boolean | UrlTree => {
  const router = inject(Router);
  const featureFlagsService = inject(StatsigService);

  const flag = featureFlagsService.checkGate(
    'b2b_front_dash_logged_unavailable'
  );
  const restrictedPaths = [
    DASH_ROUTES.report,
    DASH_ROUTES.legacyReport,
    DASH_ROUTES.refunds,
    DASH_ROUTES.balance,
    DASH_ROUTES.clarifications,
  ];
  const currentPath = route.url[0].path;

  if (flag && restrictedPaths.includes(currentPath as any)) {
    return router.parseUrl(
      `/${DASH_ROUTES.rootApp}/${DASH_ROUTES.unavailable}`
    );
  }

  return true;
};
