import { Component } from '@angular/core';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';

@Component({
  selector: 'app-integration',
  imports: [AplazoCardComponent],
  template: `
    <section
      class="h-full max-w-[1920px] justify-between gap-4 items-center pt-4 pb-8 px-4 md:px-8">
      <aplz-ui-card class="h-full mb-4 min-h-[350px]">
        <div class="items-center">
          <div class="h-full overflow-y-auto">
            <h3 class="font-semibold mb-2 text-center">
              <span> Guía Rápida</span>
            </h3>
          </div>
        </div>
      </aplz-ui-card>
      <aplz-ui-card class="h-full mb-4 min-h-[350px]">
        <div class="items-center">
          <div class="h-full min-h-fit overflow-y-auto">
            <h3 class="font-semibold mb-2 text-center">
              <span>Chatbot de ayuda</span>
            </h3>
          </div>
        </div>
      </aplz-ui-card>
    </section>
  `,
})
export class IntegrationComponent {}
