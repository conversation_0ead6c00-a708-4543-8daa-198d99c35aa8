import { Async<PERSON>ipe } from '@angular/common';
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SecurityContext, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { I18NService } from '@aplazo/i18n';
import { TemporalService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormDatepickerComponent } from '@aplazo/shared-ui/forms';
import { Subject, take, takeUntil } from 'rxjs';
import { EventManagerService } from '../../services/event-manger.service';
import { SharedCriteria } from '../../services/shared-criteria.store';
import { MerchantLoansReportLegacyUseCase } from '../loan/application/usecases/merchant-loans-report.legacy.usecase';

export interface LegacyReportTextUI {
  title: string;
  description: string;
  actionButton: string;
}

@Component({
  selector: 'app-legacy-report',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoFormDatepickerComponent,
  ],
  template: `
    <section class="pt-4 pb-16 px-4 md:px-8">
      @if (contentTextTemplate$ | async; as textUI) {
        <aplz-ui-card>
          <div class="flex flex-col items-center pt-4 pb-12">
            <figure class="max-w-56 mb-12">
              <img [src]="settlementsImg" alt="reports pictogram" />
            </figure>

            <h1 class="font-semibold text-lg">
              {{ textUI.title }}
            </h1>

            <p
              class="font-light text-dark-tertiary text-center text-balance max-w-64 mt-6 mb-10">
              {{ textUI.description }}
            </p>

            <aplz-ui-form-datepicker
              [formControl]="dateControl"
              [rangeEnabled]="true"
              [maxDate]="todayFormated"
              [centerText]="true"
              legend="Seleccione el rango de fechas">
            </aplz-ui-form-datepicker>

            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="md"
              [rounded]="true"
              (click)="getReport()">
              {{ textUI.actionButton }}
            </button>
          </div>
        </aplz-ui-card>
      }
    </section>
  `,
})
export class LegacyReportComponent implements OnDestroy {
  readonly #i18n = inject(I18NService);
  readonly #eventManager = inject(EventManagerService);
  readonly #criteria = inject(SharedCriteria);
  readonly #temporal = inject(TemporalService);
  readonly #usecase = inject(MerchantLoansReportLegacyUseCase);
  readonly #sanitizer = inject(DomSanitizer);
  readonly #scope = 'legacy-report';

  readonly #destroy$ = new Subject<void>();

  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );
  readonly todayFormated = this.#temporal.todayRawDayFirst;

  readonly #settlementsImg =
    'https://cdn.aplazo.mx/merchant-dash-assets/settlements-payments.png';
  readonly settlementsImg = this.#sanitizer.sanitize(
    SecurityContext.URL,
    this.#settlementsImg
  );

  contentTextTemplate$ = this.#i18n
    .getTranslateObjectByKey<LegacyReportTextUI>({
      key: 'content',
      scope: this.#scope,
    })
    .pipe(takeUntil(this.#destroy$));

  getReport(): void {
    this.#eventManager.sendTrackEvent('buttonClick', {
      buttonName: 'downloadLegacyReport',
    });

    if (!this.dateControl.value || !this.dateControl.value.length) {
      return;
    }

    this.#usecase.execute(this.dateControl.value).pipe(take(1)).subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
