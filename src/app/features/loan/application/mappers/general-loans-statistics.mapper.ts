import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { IGeneralLoanStatisticsDto } from '../../domain/dtos/general-loans-statistics.dto';

export function mergeCurrentAndPreviousStatistics(
  currentMonthStats: IGeneralLoanStatisticsDto,
  previousMonthStats: IGeneralLoanStatisticsDto
): IGeneralLoanStatisticsDto {
  const comparisonStats = {} as IGeneralLoanStatisticsDto;

  Object.entries(currentMonthStats).forEach(([key, val]) => {
    if (!['dateRange', 'merchantId'].includes(key) && isNaN(+val)) {
      throw new RuntimeMerchantError(
        'Invalid value to compute stats',
        'mergeCurrentAndPreviousStatistics::invalidValue'
      );
    }

    if (!['dateRange', 'merchantId'].includes(key)) {
      if (previousMonthStats[key] === 0) {
        comparisonStats[key] = val;
        return;
      }
      comparisonStats[key] = val / previousMonthStats[key] - 1;
    } else {
      comparisonStats[key] = val;
    }
  });

  return comparisonStats;
}
