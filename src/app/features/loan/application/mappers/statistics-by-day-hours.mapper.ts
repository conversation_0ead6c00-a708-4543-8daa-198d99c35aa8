import { DAYS } from '@aplazo/merchant/shared';
import {
  IDayliLoansStatisticsDto,
  IDayliLoansStatisticsReponseDto,
} from '../../domain/dtos/dayli-loans-statistics.dto';
import {
  IDayliFormattedGraphStatisticsByDayAndHoursDto,
  IDayliStatisticsByDayAndHoursDto,
} from '../../domain/dtos/dayli-statistics-by-day-hours-request.dto';

export function dailyLoanStatsByDayHoursResponseToUIDto(
  stats: IDayliLoansStatisticsReponseDto[]
): IDayliStatisticsByDayAndHoursDto {
  const weekdays = Object.keys(DAYS).map(Number);
  const formattedGraphs: IDayliFormattedGraphStatisticsByDayAndHoursDto[] =
    weekdays.map(day => {
      return {
        weekday: day,
        customers: Array.from({ length: 24 }, () => 0),
        salesAmount: Array.from({ length: 24 }, () => 0),
        salesOrder: Array.from({ length: 24 }, () => 0),
        salesAmountAvg: Array.from({ length: 24 }, () => 0),
      };
    });
  const formattedContent: IDayliLoansStatisticsDto[] = [];

  for (let i = 0; i <= 23; i++) {
    const statsByHour = stats.filter(item => item.hourDay === i);

    if (statsByHour.length === 0) {
      formattedContent.push({
        timePeriod: i,
        customers: 0,
        salesAmount: 0,
        salesOrder: 0,
        salesAmountAvg: 0,
        latestPayment: 0,
        nextPayment: 0,
      });
    } else {
      const reducedStats = statsByHour.reduce((prev, curr) => {
        if (!prev.timePeriod) {
          prev.timePeriod = i;
        }

        if (!prev.salesAmount) {
          prev.salesAmount = curr.salesAmount;
        } else {
          prev.salesAmount += curr.salesAmount;
        }

        if (!prev.salesOrder) {
          prev.salesOrder = curr.salesOrder;
        } else {
          prev.salesOrder += curr.salesOrder;
        }

        if (!prev.salesAmountAvg) {
          prev.salesAmountAvg = curr.salesAmountAvg;
        } else {
          prev.salesAmountAvg += curr.salesAmountAvg;
        }

        if (!prev.customers) {
          prev.customers = curr.customers;
        } else {
          prev.customers += curr.customers;
        }

        if (!prev.latestPayment) {
          prev.latestPayment = curr.latestPayment;
        } else {
          prev.latestPayment += curr.latestPayment;
        }

        if (!prev.nextPayment) {
          prev.nextPayment = curr.nextPayment;
        } else {
          prev.nextPayment += curr.nextPayment;
        }

        return prev;
      }, {} as IDayliLoansStatisticsDto);

      formattedContent.push(reducedStats);
    }

    for (const day of weekdays) {
      const statByHourAndDay = statsByHour.find(i => i.dayWeek === day);

      if (!statByHourAndDay) {
        continue;
      }

      const refDay = formattedGraphs.find(i => i.weekday === day);

      if (!refDay) {
        continue;
      }

      refDay.customers[i] = statByHourAndDay.customers;
      refDay.salesAmount[i] = statByHourAndDay.salesAmount;
      refDay.salesOrder[i] = statByHourAndDay.salesOrder;
      refDay.salesAmountAvg[i] = statByHourAndDay.salesAmountAvg;
    }
  }

  return {
    graphs: formattedGraphs,
    content: formattedContent,
  };
}
