import {
  IDayliGraphLoansStatisticsDto,
  IDayliLoansStatisticsDto,
  IDayliLoansStatisticsReponseDto,
} from '../../domain/dtos/dayli-loans-statistics.dto';

export const fromResponseToGraphDto = (
  stats: IDayliLoansStatisticsReponseDto[]
): IDayliGraphLoansStatisticsDto => {
  const dto: IDayliGraphLoansStatisticsDto = {
    customers: Array.from({ length: 24 }, () => 0),
    salesAmount: Array.from({ length: 24 }, () => 0),
    salesOrder: Array.from({ length: 24 }, () => 0),
    salesAmountAvg: Array.from({ length: 24 }, () => 0),
  };

  if (stats === null || typeof stats === 'undefined' || stats.length === 0) {
    return dto;
  }

  for (let i = 0; i <= 23; i++) {
    const statsByHour = stats.find(stat => stat.hourDay === i);

    if (statsByHour === null || typeof statsByHour === 'undefined') {
      continue;
    }

    const customers = [...dto.customers];
    const salesAmount = [...dto.salesAmount];
    const salesOrder = [...dto.salesOrder];
    const salesAmountAvg = [...dto.salesAmountAvg];

    customers.splice(i, 1, statsByHour.customers);
    salesAmount.splice(i, 1, statsByHour.salesAmount);
    salesOrder.splice(i, 1, statsByHour.salesOrder);
    salesAmountAvg.splice(i, 1, statsByHour.salesAmountAvg);

    dto.customers = customers;
    dto.salesAmount = salesAmount;
    dto.salesOrder = salesOrder;
    dto.salesAmountAvg = salesAmountAvg;
  }

  return dto;
};

export const fromPartialResponseToDto = (
  stats: IDayliLoansStatisticsReponseDto[]
): IDayliLoansStatisticsDto[] => {
  const dto: IDayliLoansStatisticsDto[] = Array.from(
    { length: 24 },
    (_, index) => ({
      timePeriod: index,
      customers: 0,
      salesAmount: 0,
      salesOrder: 0,
      salesAmountAvg: 0,
      latestPayment: 0,
      nextPayment: 0,
    })
  );

  if (stats === null || typeof stats === 'undefined' || stats.length === 0) {
    return dto;
  }

  for (let i = 0; i <= 23; i++) {
    const statsByHour = stats?.find(stat => stat.hourDay === i);

    if (statsByHour === null || typeof statsByHour === 'undefined') {
      continue;
    }

    dto.splice(i, 1, {
      timePeriod: statsByHour.hourDay ?? 0,
      customers: statsByHour.customers,
      salesAmount: statsByHour.salesAmount,
      salesOrder: statsByHour.salesOrder,
      salesAmountAvg: statsByHour.salesAmountAvg,
      latestPayment: statsByHour.latestPayment,
      nextPayment: statsByHour.nextPayment,
    });
  }
  return dto;
};
