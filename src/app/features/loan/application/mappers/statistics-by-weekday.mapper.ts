import {
  IDayliGraphLoansStatisticsDto,
  IDayliLoansStatisticsDto,
  IDayliLoansStatisticsReponseDto,
} from '../../domain/dtos/dayli-loans-statistics.dto';

export const fromWeekdayResponseToGraphDto = (
  stats: IDayliLoansStatisticsReponseDto[]
): IDayliGraphLoansStatisticsDto => {
  const dto: IDayliGraphLoansStatisticsDto = {
    customers: Array.from({ length: 7 }, () => 0),
    salesAmount: Array.from({ length: 7 }, () => 0),
    salesAmountAvg: Array.from({ length: 7 }, () => 0),
    salesOrder: Array.from({ length: 7 }, () => 0),
  };

  if (stats === null || typeof stats === 'undefined' || stats.length === 0) {
    return dto;
  }

  for (let i = 0; i <= 6; i++) {
    const statsByWeekday = stats.find(stat => stat.dayWeek === i);

    if (statsByWeekday === null || typeof statsByWeekday === 'undefined') {
      continue;
    }

    const customers = [...dto.customers];
    const salesAmount = [...dto.salesAmount];
    const salesOrder = [...dto.salesOrder];
    const salesAmountAvg = [...dto.salesAmountAvg];

    customers.splice(i, 1, statsByWeekday.customers);
    salesAmount.splice(i, 1, statsByWeekday.salesAmount);
    salesOrder.splice(i, 1, statsByWeekday.salesOrder);
    salesAmountAvg.splice(i, 1, statsByWeekday.salesAmountAvg);

    dto.customers = customers;
    dto.salesAmount = salesAmount;
    dto.salesOrder = salesOrder;
    dto.salesAmountAvg = salesAmountAvg;
  }

  return dto;
};

export const fromWeekdayPartialResponseToDto = (
  stats: IDayliLoansStatisticsReponseDto[]
): IDayliLoansStatisticsDto[] => {
  const dto: IDayliLoansStatisticsDto[] = Array.from(
    { length: 7 },
    (_, index) => ({
      timePeriod: index,
      customers: 0,
      salesAmount: 0,
      salesOrder: 0,
      salesAmountAvg: 0,
      latestPayment: 0,
      nextPayment: 0,
    })
  );

  for (let i = 0; i <= 6; i++) {
    const statsByWeekday = stats?.find(stat => stat.dayWeek === i);

    if (statsByWeekday === null || typeof statsByWeekday === 'undefined') {
      continue;
    }

    dto.splice(i, 1, {
      timePeriod: statsByWeekday.dayWeek,
      customers: statsByWeekday.customers,
      salesAmount: statsByWeekday.salesAmount,
      salesOrder: statsByWeekday.salesOrder,
      salesAmountAvg: statsByWeekday.salesAmountAvg,
      latestPayment: statsByWeekday.latestPayment,
      nextPayment: statsByWeekday.nextPayment,
    });
  }

  return dto;
};
