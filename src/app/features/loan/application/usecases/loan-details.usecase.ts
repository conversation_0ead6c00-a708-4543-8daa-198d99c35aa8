import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  forkJoin,
  map,
  Observable,
  of,
  take,
  throwError,
} from 'rxjs';
import { ILoanDetailsRequestDto } from '../../domain/dtos/loan-details-request.dto';
import { ILoanDetailsUiInfoDto } from '../../domain/dtos/loan-details-ui-info.dto';
import { BriefCustomer } from '../../domain/entities/brief-customer';
import { BriefProduct } from '../../domain/entities/brief-product';
import { BriefCustomerRepository } from '../../domain/repositories/brief-customer.repository';
import { BriefProductRepository } from '../../domain/repositories/brief-product.repository';

@Injectable()
export class LoanDetailsUseCase
  implements
    BaseUsecase<ILoanDetailsRequestDto, Observable<ILoanDetailsUiInfoDto>>
{
  private readonly customerRepository: BriefCustomerRepository<
    string,
    Observable<BriefCustomer>
  > = inject(BriefCustomerRepository);
  private readonly productRepository: BriefProductRepository<
    string,
    Observable<BriefProduct[]>
  > = inject(BriefProductRepository);
  private readonly loaderService = inject(LoaderService);
  private readonly errorHandler = inject(UseCaseErrorHandler);

  execute(args: ILoanDetailsRequestDto): Observable<ILoanDetailsUiInfoDto> {
    const idLoader = this.loaderService.show();

    const emptyCustomer: BriefCustomer = {
      email: '',
      name: '',
      phoneNumber: '',
    };

    try {
      if (
        args.customerUrl === null ||
        args.customerUrl === '' ||
        typeof args.customerUrl === 'undefined'
      ) {
        return this.productRepository
          .getInfo(args.productUrl)
          .pipe(
            take(1),
            catchError(error => {
              console.error(error);

              return of([]);
            })
          )
          .pipe(
            map(products => {
              return {
                customer: emptyCustomer,
                products,
              };
            }),

            catchError(err => this.errorHandler.handle<never>(err)),

            finalize(() => {
              this.loaderService.hide(idLoader);
            })
          );
      }

      return forkJoin([
        this.customerRepository.getInfo(args.customerUrl).pipe(
          take(1),
          catchError(error => {
            console.error(error);
            return of(emptyCustomer);
          })
        ),
        this.productRepository.getInfo(args.productUrl).pipe(
          take(1),
          catchError(error => {
            console.error(error);

            return of([]);
          })
        ),
      ]).pipe(
        map(([customer, products]) => {
          return {
            customer,
            products,
          };
        }),

        catchError(err => this.errorHandler.handle<never>(err)),

        finalize(() => {
          this.loaderService.hide(idLoader);
        })
      );
    } catch (error) {
      this.loaderService.hide(idLoader);

      return this.errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
