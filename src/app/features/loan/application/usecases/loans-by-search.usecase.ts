import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  combineLatestWith,
  finalize,
  map,
  Observable,
  take,
  throwError,
} from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { MerchantLoan } from '../../domain/entities/merchant-loan';
import { MerchantSearchLoansRepository } from '../../domain/repositories/merchant-search-loans.repository';
import { MerchantShowSensitiveDataRepository } from '../../domain/repositories/merchant-show-sensitive-data.repository';

@Injectable()
export class MerchantLoansBySearchUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<MerchantLoan[]>
    >
{
  private readonly repository: MerchantSearchLoansRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<MerchantLoan[]>
  > = inject(MerchantSearchLoansRepository);

  private readonly sensitiveDataRepository: MerchantShowSensitiveDataRepository<
    Observable<boolean>
  > = inject(MerchantShowSensitiveDataRepository);

  private readonly loaderService = inject(LoaderService);
  private readonly errorHandler = inject(UseCaseErrorHandler);

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<MerchantLoan[]> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.repository.searchByIdPhoneOrEmail(criteria).pipe(
        combineLatestWith(this.sensitiveDataRepository.getFlag().pipe(take(1))),

        map(([response, showSensitiveData]) => {
          if (showSensitiveData) {
            return response;
          }

          return response.map(item => ({
            ...item,
            customerUrl: '',
          }));
        }),

        catchError(error => this.errorHandler.handle<never>(error)),

        take(1),

        finalize(() => this.loaderService.hide(idLoader))
      );
    } catch (error) {
      this.loaderService.hide(idLoader);

      return this.errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
