import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  DateCalculator,
  LoaderService,
  LOAN_STATUS,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  forkJoin,
  map,
  Observable,
  take,
  throwError,
} from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { IGeneralLoanStatisticsDto } from '../../domain/dtos/general-loans-statistics.dto';
import { IMerchantLoansComparisonStatisticsDto } from '../../domain/dtos/merchant-loans-comparison-statistics.dto';
import { MerchantLoansStatisticsRepository } from '../../domain/repositories/merchant-loans-statistics.repository';
import { mergeCurrentAndPreviousStatistics } from '../mappers/general-loans-statistics.mapper';

export const statusToComparison = [
  LOAN_STATUS.outstanding,
  LOAN_STATUS.historical,
  LOAN_STATUS.late,
  LOAN_STATUS.defaulted,
].join(',');

export const emptyResponse: IGeneralLoanStatisticsDto = {
  merchantId: -1,
  customers: 0,
  salesAmount: 0,
  salesOrder: 0,
  salesAmountAvg: 0,
  latestPayment: 0,
  nextPayment: 0,
};

@Injectable()
export class MerchantLoansComparisonStatisticsUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<IMerchantLoansComparisonStatisticsDto>
    >
{
  private readonly statsRepository: MerchantLoansStatisticsRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<IGeneralLoanStatisticsDto>
  > = inject(MerchantLoansStatisticsRepository);

  private readonly dateCalculator = inject(DateCalculator);

  private readonly loaderService = inject(LoaderService);
  private readonly errorHandler = inject(UseCaseErrorHandler);

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<IMerchantLoansComparisonStatisticsDto> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      const currentMonthDateRange =
        this.dateCalculator.getCurrentMonthDateRangeUntilCurrentDay();
      const previousMonthDateRange =
        this.dateCalculator.getPreviousMonthDateRangeSamePeriodCurrenMonth();

      return forkJoin([
        this.statsRepository
          .getStatisticsByDateRange({
            ...criteria,
            status: statusToComparison,
            dateRange: currentMonthDateRange,
          })
          .pipe(
            take(1),
            map(response => {
              if (!response) {
                return { ...emptyResponse };
              }
              return response;
            })
          ),
        this.statsRepository
          .getStatisticsByDateRange({
            ...criteria,
            status: statusToComparison,
            dateRange: previousMonthDateRange,
          })
          .pipe(
            take(1),
            map(response => {
              if (!response) {
                return { ...emptyResponse };
              }
              return response;
            })
          ),
      ]).pipe(
        map(([currentMonthStats, previousMonthStats]) => {
          return {
            comparisonStats: mergeCurrentAndPreviousStatistics(
              currentMonthStats,
              previousMonthStats
            ),
            currentMonth: currentMonthStats,
            previousMonth: previousMonthStats,
          };
        }),
        catchError(error =>
          this.errorHandler.handle(
            error,
            throwError(() => error)
          )
        ),
        finalize(() => this.loaderService.hide(idLoader))
      );
    } catch (error) {
      this.loaderService.hide(idLoader);

      return this.errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
