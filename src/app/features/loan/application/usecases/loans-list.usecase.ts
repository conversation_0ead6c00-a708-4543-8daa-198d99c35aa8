import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  map,
  Observable,
  switchMap,
  take,
  throwError,
} from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { MerchantLoansResponseDto } from '../../domain/dtos/merchant-loans-response.dto';
import { MerchantLoansRepository } from '../../domain/repositories/merchant-loans.repository';
import { MerchantShowSensitiveDataRepository } from '../../domain/repositories/merchant-show-sensitive-data.repository';

@Injectable()
export class MerchantLoansListUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<MerchantLoansResponseDto>
    >
{
  private readonly repository: MerchantLoansRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<MerchantLoansResponseDto>
  > = inject(MerchantLoansRepository);

  private readonly sensitiveDataRepository: MerchantShowSensitiveDataRepository<
    Observable<boolean>
  > = inject(MerchantShowSensitiveDataRepository);

  private readonly loaderService = inject(LoaderService);
  private readonly errorHandler = inject(UseCaseErrorHandler);

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<MerchantLoansResponseDto> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.repository.getLoans(criteria).pipe(
        switchMap(response =>
          this.sensitiveDataRepository.getFlag().pipe(
            map(showSensitiveData => {
              return {
                response,
                showSensitiveData,
              };
            })
          )
        ),

        map(({ response, showSensitiveData }) => {
          if (showSensitiveData) {
            return response;
          }

          return {
            ...response,
            content: response.content.map(item => ({
              ...item,
              customerUrl: '',
            })),
          };
        }),

        catchError(error => this.errorHandler.handle<never>(error)),

        take(1),

        finalize(() => {
          this.loaderService.hide(idLoader);
        })
      );
    } catch (error) {
      this.loaderService.hide(idLoader);

      return this.errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
