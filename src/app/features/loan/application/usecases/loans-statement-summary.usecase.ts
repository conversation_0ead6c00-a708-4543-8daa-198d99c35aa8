import { inject, Injectable } from '@angular/core';
import { BaseUsecase, LoaderService } from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { SharedCriteriaWithBranchOfficesRepositoryDto } from '../../../shared/shared-criteria';
import { IGeneralLoanStatisticsDto } from '../../domain/dtos/general-loans-statistics.dto';
import { IStatementSummaryDto } from '../../domain/dtos/statement-summary.dto';
import { MerchantLoansStatisticsRepository } from '../../domain/repositories/merchant-loans-statistics.repository';

export const emptyDefaultLoansStatisticsResponse: IGeneralLoanStatisticsDto = {
  merchantId: -1,
  customers: 0,
  salesAmount: 0,
  salesOrder: 0,
  salesAmountAvg: 0,
  latestPayment: 0,
  nextPayment: 0,
};

@Injectable()
export class GetLoansStatementSummaryUseCase
  implements BaseUsecase<undefined, Observable<IStatementSummaryDto>>
{
  private statsRepository: MerchantLoansStatisticsRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<IGeneralLoanStatisticsDto>
  > = inject(MerchantLoansStatisticsRepository);
  private loaderService: LoaderService = inject(LoaderService);

  execute(): Observable<IStatementSummaryDto> {
    const idLoader = this.loaderService.show();

    try {
      return this.statsRepository.getStatementSummary().pipe(
        map(response => response ?? { latestPayment: 0, nextPayment: 0 }),
        catchError(error => {
          console.error(error);
          return of({ latestPayment: 0, nextPayment: 0 });
        }),
        finalize(() => {
          this.loaderService.hide(idLoader);
        }),
        take(1)
      );
    } catch (error) {
      this.loaderService.hide(idLoader);
      console.error(error);
      return of({ latestPayment: 0, nextPayment: 0 });
    }
  }
}
