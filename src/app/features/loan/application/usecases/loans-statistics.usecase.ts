import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { IGeneralLoanStatisticsDto } from '../../domain/dtos/general-loans-statistics.dto';
import { MerchantLoansStatisticsRepository } from '../../domain/repositories/merchant-loans-statistics.repository';

export const emptyDefaultLoansStatisticsResponse: IGeneralLoanStatisticsDto = {
  merchantId: -1,
  customers: 0,
  salesAmount: 0,
  salesOrder: 0,
  salesAmountAvg: 0,
  latestPayment: 0,
  nextPayment: 0,
};

@Injectable()
export class MerchantLoansStatisticsUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<IGeneralLoanStatisticsDto>
    >
{
  private statsRepository: MerchantLoansStatisticsRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<IGeneralLoanStatisticsDto>
  > = inject(MerchantLoansStatisticsRepository);
  private loaderService = inject(LoaderService);
  private usecaseErrorHandler = inject(UseCaseErrorHandler);

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<IGeneralLoanStatisticsDto> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.statsRepository.getStatisticsByDateRange(criteria).pipe(
        map(response => {
          if (!response) {
            return emptyDefaultLoansStatisticsResponse;
          }
          return response;
        }),

        catchError(error => {
          return this.usecaseErrorHandler.handle(
            error,
            of(emptyDefaultLoansStatisticsResponse)
          );
        }),

        take(1),

        finalize(() => {
          this.loaderService.hide(idLoader);
        })
      );
    } catch (error) {
      this.loaderService.hide(idLoader);

      return this.usecaseErrorHandler.handle(
        error,
        of(emptyDefaultLoansStatisticsResponse)
      );
    }
  }
}
