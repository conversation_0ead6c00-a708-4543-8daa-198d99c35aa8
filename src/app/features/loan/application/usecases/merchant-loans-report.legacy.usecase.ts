import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LOAN_STATUS,
  LoaderService,
  NotifierService,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import { differenceInCalendarDays } from 'date-fns';
import {
  EMPTY,
  Observable,
  catchError,
  defer,
  finalize,
  map,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { ILegacyReportRequestDto } from '../../domain/dtos/legacy-report-request.dto';
import { MerchantLoansReportRepository } from '../../domain/repositories/merchant-loans-report.repository';

@Injectable()
export class MerchantLoansReportLegacyUseCase
  implements BaseUsecase<Date[], Observable<string[][]>>
{
  constructor(
    private readonly repository: MerchantLoansReportRepository<
      ILegacyReportRequestDto,
      Observable<string>
    >,
    private readonly loaderAdapter: LoaderService,
    private readonly notifierService: NotifierService,
    private readonly fileGenerator: FileGeneratorService<Promise<string[][]>>,
    private readonly reportResponseTransformService: CsvMapperService,
    private readonly errorHandler: UseCaseErrorHandler,
    private readonly temporal: TemporalService
  ) {}

  execute(dateRange: Date[]): Observable<string[][]> {
    const loaderId = this.loaderAdapter.show();

    const statusToGetReportRequest = [
      LOAN_STATUS.outstanding,
      LOAN_STATUS.historical,
      LOAN_STATUS.late,
      LOAN_STATUS.defaulted,
    ].join(',');

    const [start, end] = dateRange;

    const calendarDiff = differenceInCalendarDays(end, start);

    if (calendarDiff > 90) {
      this.notifierService.warning({
        title: 'Modifique el rango de fechas',
        message: 'El rango no puede ser mayor a 90 días',
      });
      this.loaderAdapter.hide(loaderId);
      return EMPTY;
    }

    const startDate = this.temporal.formatRawDateDayFirst(start);
    const endDate = this.temporal.formatRawDateDayFirst(end);

    const reportName = `reporte_ventas_${startDate}_a_${endDate}`;

    return this.repository
      .getLoansForReport({
        dateRange: {
          startDate,
          endDate,
        },
        status: statusToGetReportRequest,
      })
      .pipe(
        map(response =>
          this.reportResponseTransformService.transform(response)
        ),
        take(1),

        tap(content => {
          // Empty report
          if (!content || content.length === 0) {
            this.notifierService.info({
              title: 'Reporte vacio',
              message: 'No existen compras en el rango de fechas solicitado.',
            });
          }
        }),

        switchMap(content => {
          if (!content || content.length === 0) {
            return of([]);
          }

          return defer(() =>
            this.fileGenerator.generateFileAndDownload(content, reportName)
          ).pipe(
            take(1),
            tap(() => {
              // Success download
              this.notifierService.success({
                title: 'Reporte generado',
                message:
                  'El reporte se ha generado correctamente y la descarga comenzara pronto.',
              });
            })
          );
        }),

        catchError(err => this.errorHandler.handle(err, EMPTY)),

        finalize(() => {
          this.loaderAdapter.hide(loaderId);
        })
      );
  }
}
