import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  map,
  Observable,
  take,
  tap,
  throwError,
} from 'rxjs';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { IDayliLoansStatisticsReponseDto } from '../../domain/dtos/dayli-loans-statistics.dto';
import {
  IDayliStatisticsByDayAndHoursDto,
  IDayliStatisticsByDayAndHoursRequestDto,
} from '../../domain/dtos/dayli-statistics-by-day-hours-request.dto';
import { MerchantLoanStatisticsByDayHoursRepository } from '../../domain/repositories/merchant-loan-statistics-by-day-hours.repository';
import { dailyLoanStatsByDayHoursResponseToUIDto } from '../mappers/statistics-by-day-hours.mapper';

@Injectable()
export class StatisticsByDayAndHoursUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<IDayliStatisticsByDayAndHoursDto>
    >
{
  constructor(
    private repository: MerchantLoanStatisticsByDayHoursRepository<
      IDayliStatisticsByDayAndHoursRequestDto,
      Observable<IDayliLoansStatisticsReponseDto[]>
    >,
    private loaderService: LoaderService,
    private notifierService: NotifierService,
    private errorHandler: UseCaseErrorHandler
  ) {}

  public execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<IDayliStatisticsByDayAndHoursDto> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.repository
        .getStats({
          dateRange: criteria.dateRange,
          days: criteria.days ?? '',
        })
        .pipe(
          tap(stats => {
            if (
              stats === null ||
              typeof stats === 'undefined' ||
              stats.length === 0
            ) {
              this.notifierService.warning({
                title:
                  'Por el momento no hay datos para generar las estadísticas',
              });
            }
          }),
          map(dailyLoanStatsByDayHoursResponseToUIDto),
          catchError(err => this.errorHandler.handle<never>(err)),
          take(1),
          finalize(() => {
            this.loaderService.hide(idLoader);
          })
        );
    } catch (error) {
      this.loaderService.hide(idLoader);

      return this.errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
