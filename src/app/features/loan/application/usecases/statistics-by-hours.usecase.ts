import {
  B2BDateRange,
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { catchError, map, Observable, take, tap, throwError } from 'rxjs';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import {
  IDayliFormattedLoansStatisticsDto,
  IDayliLoansStatisticsReponseDto,
} from '../../domain/dtos/dayli-loans-statistics.dto';
import { MerchantLoanStatisticsByHoursRepository } from '../../domain/repositories/merchant-loan-statistics-by-hours.repository';
import {
  fromPartialResponseToDto,
  fromResponseToGraphDto,
} from '../mappers/statistics-by-hours.mapper';

export class StatisticsByHoursUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<IDayliFormattedLoansStatisticsDto>
    >
{
  private repository: MerchantLoanStatisticsByHoursRepository<
    B2BDateRange,
    Observable<IDayliLoansStatisticsReponseDto[]>
  >;

  private loaderService: LoaderService;

  private notifierService: NotifierService;

  constructor(
    repository: MerchantLoanStatisticsByHoursRepository<
      B2BDateRange,
      Observable<IDayliLoansStatisticsReponseDto[]>
    >,
    loaderService: LoaderService,
    notifierService: NotifierService
  ) {
    this.repository = repository;
    this.loaderService = loaderService;
    this.notifierService = notifierService;
  }

  public execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<IDayliFormattedLoansStatisticsDto> {
    const idLoader = this.loaderService.show();
    try {
      const dateRange = SharedCriteriaMapper.fromUIToRepository(args).dateRange;

      return this.repository.getStats(dateRange).pipe(
        take(1),
        tap(statsByHours => {
          if (
            statsByHours === null ||
            typeof statsByHours === 'undefined' ||
            statsByHours.length === 0
          ) {
            this.notifierService.warning({
              title:
                'Por el momento no hay datos para generar las estadísticas',
            });
          }
        }),
        map(statsByHours => {
          return {
            graphs: fromResponseToGraphDto(statsByHours),
            content: fromPartialResponseToDto(statsByHours),
          };
        }),
        catchError(error => throwError(() => error))
      );
    } catch (error) {
      console.error(error);

      this.notifierService.warning({
        title: 'Ups',
        message:
          (error as any)?.message ||
          'Algo salió mal con la consulta de estadísticas por hora',
      });
      throw error;
    } finally {
      this.loaderService.hide(idLoader);
    }
  }
}
