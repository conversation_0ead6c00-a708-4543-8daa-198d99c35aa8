import {
  B2BDateRange,
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { catchError, map, Observable, take, tap, throwError } from 'rxjs';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import {
  IDayliFormattedLoansStatisticsDto,
  IDayliLoansStatisticsReponseDto,
} from '../../domain/dtos/dayli-loans-statistics.dto';
import { MerchantLoanStatisticsByWeekdayRepository } from '../../domain/repositories/merchant-loan-statistics-by-weekday.repository';
import {
  fromWeekdayPartialResponseToDto,
  fromWeekdayResponseToGraphDto,
} from '../mappers/statistics-by-weekday.mapper';

export class StatisticsByWeekdayUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<IDayliFormattedLoansStatisticsDto>
    >
{
  private repository: MerchantLoanStatisticsByWeekdayRepository<
    B2BDateRange,
    Observable<IDayliLoansStatisticsReponseDto[]>
  >;

  private loaderService: LoaderService;

  private notifierService: NotifierService;

  constructor(
    repository: MerchantLoanStatisticsByWeekdayRepository<
      B2BDateRange,
      Observable<IDayliLoansStatisticsReponseDto[]>
    >,
    loaderService: LoaderService,
    notifierService: NotifierService
  ) {
    this.repository = repository;
    this.loaderService = loaderService;
    this.notifierService = notifierService;
  }

  public execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<IDayliFormattedLoansStatisticsDto> {
    const idLoader = this.loaderService.show();
    try {
      const dateRange = SharedCriteriaMapper.fromUIToRepository(args).dateRange;

      return this.repository.getStats(dateRange).pipe(
        take(1),
        tap(statsByWeekday => {
          if (
            statsByWeekday === null ||
            typeof statsByWeekday === 'undefined' ||
            statsByWeekday.length === 0
          ) {
            this.notifierService.warning({
              title:
                'Por el momento no hay datos para generar las estadísticas',
            });
          }
        }),
        map(statsByWeekday => {
          return {
            graphs: fromWeekdayResponseToGraphDto(statsByWeekday),
            content: fromWeekdayPartialResponseToDto(statsByWeekday),
          };
        }),
        catchError(error => throwError(() => error))
      );
    } catch (error) {
      console.error(error);

      this.notifierService.warning({
        title: 'Ups',
        message:
          (error as any)?.message ||
          'Algo salió mal con la consulta de estadísticas por semana',
      });
      throw error;
    } finally {
      this.loaderService.hide(idLoader);
    }
  }
}
