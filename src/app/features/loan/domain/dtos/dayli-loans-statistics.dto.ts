import { LoanStatistics } from '../../domain/entities/merchant-statistics';

export interface IDayliLoansStatisticsReponseDto extends LoanStatistics {
  hourDay?: number;
  dayWeek: number;
}

export interface IDayliLoansStatisticsDto extends LoanStatistics {
  timePeriod: number;
}

export interface IDayliGraphLoansStatisticsDto {
  customers: number[];
  salesAmount: number[];
  salesOrder: number[];
  salesAmountAvg: number[];
  label?: string;
}

export interface IDayliFormattedLoansStatisticsDto {
  graphs: IDayliGraphLoansStatisticsDto;
  content: IDayliLoansStatisticsDto[];
}
