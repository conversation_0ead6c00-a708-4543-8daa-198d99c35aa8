import { B2BDateRange } from '@aplazo/merchant/shared';
import { IDayliLoansStatisticsDto } from './dayli-loans-statistics.dto';

export interface IDayliStatisticsByDayAndHoursRequestDto {
  dateRange: B2BDateRange;
  days: string;
}

export interface IDayliFormattedGraphStatisticsByDayAndHoursDto {
  weekday: number;
  customers: number[];
  salesAmount: number[];
  salesOrder: number[];
  salesAmountAvg: number[];
}

export interface IDayliFormattedTableStatisticsByDayAndHoursDto
  extends IDayliLoansStatisticsDto {
  weekday: number;
}

export interface IDayliStatisticsByDayAndHoursDto {
  graphs: IDayliFormattedGraphStatisticsByDayAndHoursDto[];
  content: IDayliLoansStatisticsDto[];
}
