import { Pageable, Sort } from '@aplazo/merchant/shared';
import { MerchantLoan } from '../../domain/entities/merchant-loan';

export interface MerchantLoansResponseDto {
  readonly content: MerchantLoan[];
  readonly pageable: Pageable;
  readonly totalPages: number;
  readonly totalElements: number;
  readonly last: boolean;
  readonly first: boolean;
  readonly numberOfElements: number;
  readonly sort: Sort;
  readonly size: number;
  readonly number: number;
  readonly empty: boolean;
}
