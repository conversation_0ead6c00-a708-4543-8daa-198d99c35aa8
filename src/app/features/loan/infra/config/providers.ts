import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { LoanDetailsUseCase } from '../../application/usecases/loan-details.usecase';
import { MerchantLoansBySearchUseCase } from '../../application/usecases/loans-by-search.usecase';
import { MerchantLoansComparisonStatisticsUseCase } from '../../application/usecases/loans-comparison-statistics.usecase';
import { MerchantLoansListUseCase } from '../../application/usecases/loans-list.usecase';
import { GetLoansStatementSummaryUseCase } from '../../application/usecases/loans-statement-summary.usecase';
import { MerchantLoansStatisticsUseCase } from '../../application/usecases/loans-statistics.usecase';
import { MerchantLoansReportLegacyUseCase } from '../../application/usecases/merchant-loans-report.legacy.usecase';
import { StatisticsByDayAndHoursUseCase } from '../../application/usecases/statistics-by-day-hours.usecase';
import { BriefCustomerRepository } from '../../domain/repositories/brief-customer.repository';
import { BriefProductRepository } from '../../domain/repositories/brief-product.repository';
import { MerchantLoanStatisticsByDayHoursRepository } from '../../domain/repositories/merchant-loan-statistics-by-day-hours.repository';
import { MerchantLoanStatisticsByHoursRepository } from '../../domain/repositories/merchant-loan-statistics-by-hours.repository';
import { MerchantLoanStatisticsByWeekdayRepository } from '../../domain/repositories/merchant-loan-statistics-by-weekday.repository';
import { MerchantLoansReportRepository } from '../../domain/repositories/merchant-loans-report.repository';
import { MerchantLoansStatisticsRepository } from '../../domain/repositories/merchant-loans-statistics.repository';
import { MerchantLoansRepository } from '../../domain/repositories/merchant-loans.repository';
import { MerchantSearchLoansRepository } from '../../domain/repositories/merchant-search-loans.repository';
import { MerchantShowSensitiveDataRepository } from '../../domain/repositories/merchant-show-sensitive-data.repository';
import { BriefCustomerWithHttpRepository } from '../repositories/brief-customer-with-http.repository';
import { BriefProductWithHttpRepository } from '../repositories/brief-product-with-http.repository';
import { LoanSensitiveDataImplRepository } from '../repositories/loan-sensitive-data-impl.repository';
import { LoansListRepositoryImpl } from '../repositories/loans-list-impl.repository';
import { LoansStatisticsDayHoursRepositoryImpl } from '../repositories/loans-statistics-day-hours-impl.repository';
import { LoansStatisticsHoursRepositoryImpl } from '../repositories/loans-statistics-hours-impl.repository';
import { LoansStatisticsRepositoryImpl } from '../repositories/loans-statistics-impl.repository';
import { LoansStatisticsWeekdayRepositoryImpl } from '../repositories/loans-statistics-weekday-impl.repository';
import { MerchantLoansLegacyReportRepositoryImpl } from '../repositories/merchant-loans-legacy-report.repository';
import { SearchLoansRespositoryImpl } from '../repositories/search-loans-impl.repository';

export function provideLoanRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: BriefCustomerRepository,
      useClass: BriefCustomerWithHttpRepository,
    },
    {
      provide: BriefProductRepository,
      useClass: BriefProductWithHttpRepository,
    },
    {
      provide: MerchantShowSensitiveDataRepository,
      useClass: LoanSensitiveDataImplRepository,
    },
    {
      provide: MerchantLoansRepository,
      useClass: LoansListRepositoryImpl,
    },
    {
      provide: MerchantLoanStatisticsByDayHoursRepository,
      useClass: LoansStatisticsDayHoursRepositoryImpl,
    },
    {
      provide: MerchantLoanStatisticsByHoursRepository,
      useClass: LoansStatisticsHoursRepositoryImpl,
    },
    {
      provide: MerchantLoansStatisticsRepository,
      useClass: LoansStatisticsRepositoryImpl,
    },
    {
      provide: MerchantLoanStatisticsByWeekdayRepository,
      useClass: LoansStatisticsWeekdayRepositoryImpl,
    },
    {
      provide: MerchantLoansReportRepository,
      useClass: MerchantLoansLegacyReportRepositoryImpl,
    },
    {
      provide: MerchantSearchLoansRepository,
      useClass: SearchLoansRespositoryImpl,
    },
    StatisticsByDayAndHoursUseCase,
    MerchantLoansReportLegacyUseCase,
    MerchantLoansListUseCase,
    MerchantLoansBySearchUseCase,
    GetLoansStatementSummaryUseCase,
    MerchantLoansStatisticsUseCase,
    MerchantLoansComparisonStatisticsUseCase,
    LoanDetailsUseCase,
  ]);
}
