import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { BriefCustomer } from '../../domain/entities/brief-customer';
import { BriefCustomerRepository } from '../../domain/repositories/brief-customer.repository';

@Injectable({
  providedIn: 'root',
})
export class BriefCustomerWithHttpRepository
  implements BriefCustomerRepository<string, Observable<BriefCustomer>>
{
  readonly #http = inject(HttpClient);

  getInfo(url: string): Observable<BriefCustomer> {
    return this.#http.get<BriefCustomer>(url);
  }
}
