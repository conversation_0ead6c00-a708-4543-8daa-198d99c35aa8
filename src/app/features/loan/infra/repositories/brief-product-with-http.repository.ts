import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { BriefProduct } from '../../domain/entities/brief-product';
import { BriefProductRepository } from '../../domain/repositories/brief-product.repository';

@Injectable({
  providedIn: 'root',
})
export class BriefProductWithHttpRepository
  implements BriefProductRepository<string, Observable<BriefProduct[]>>
{
  readonly #http = inject(HttpClient);

  getInfo(url: string): Observable<BriefProduct[]> {
    return this.#http.get<BriefProduct[]>(url);
  }
}
