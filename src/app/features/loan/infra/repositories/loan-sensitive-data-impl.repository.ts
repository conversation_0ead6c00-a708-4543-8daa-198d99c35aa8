import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { MerchantShowSensitiveDataRepository } from '../../domain/repositories/merchant-show-sensitive-data.repository';

@Injectable({
  providedIn: 'root',
})
export class LoanSensitiveDataImplRepository
  implements MerchantShowSensitiveDataRepository<Observable<boolean>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getFlag(): Observable<boolean> {
    return this.#http.get<boolean>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/showDataSensitive`
    );
  }
}
