import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../shared/shared-criteria';
import { MerchantLoansResponseDto } from '../../domain/dtos/merchant-loans-response.dto';
import { MerchantLoansRepository } from '../../domain/repositories/merchant-loans.repository';

@Injectable({
  providedIn: 'root',
})
export class LoansListRepositoryImpl
  implements
    MerchantLoansRepository<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<MerchantLoansResponseDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getLoans(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<MerchantLoansResponseDto> {
    return this.#http.get<MerchantLoansResponseDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/order`,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
          pageNum: this.pageToString(args.pageNum),
          pageSize: args.pageSize.toString(),
        },
      }
    );
  }

  private pageToString(page: number): string {
    return isNaN(page) ? '0' : page.toString();
  }
}
