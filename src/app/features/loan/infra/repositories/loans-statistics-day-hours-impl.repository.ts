import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IDayliLoansStatisticsReponseDto } from '../../domain/dtos/dayli-loans-statistics.dto';
import { IDayliStatisticsByDayAndHoursRequestDto } from '../../domain/dtos/dayli-statistics-by-day-hours-request.dto';
import { MerchantLoanStatisticsByDayHoursRepository } from '../../domain/repositories/merchant-loan-statistics-by-day-hours.repository';

@Injectable({
  providedIn: 'root',
})
export class LoansStatisticsDayHoursRepositoryImpl
  implements
    MerchantLoanStatisticsByDayHoursRepository<
      IDayliStatisticsByDayAndHoursRequestDto,
      Observable<IDayliLoansStatisticsReponseDto[]>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getStats(
    args: IDayliStatisticsByDayAndHoursRequestDto
  ): Observable<IDayliLoansStatisticsReponseDto[]> {
    return this.#http.get<IDayliLoansStatisticsReponseDto[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/statistics/sales/dayWeek`,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          days: args.days,
        },
      }
    );
  }
}
