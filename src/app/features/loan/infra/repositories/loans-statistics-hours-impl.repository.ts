import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { B2BDateRange } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IDayliLoansStatisticsReponseDto } from '../../domain/dtos/dayli-loans-statistics.dto';
import { MerchantLoanStatisticsByHoursRepository } from '../../domain/repositories/merchant-loan-statistics-by-hours.repository';

@Injectable({
  providedIn: 'root',
})
export class LoansStatisticsHoursRepositoryImpl
  implements
    MerchantLoanStatisticsByHoursRepository<
      B2BDateRange,
      Observable<IDayliLoansStatisticsReponseDto[]>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getStats(args: B2BDateRange): Observable<IDayliLoansStatisticsReponseDto[]> {
    return this.#http.get<IDayliLoansStatisticsReponseDto[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/statistics/sales/hour`,
      {
        params: {
          startDate: args.startDate,
          endDate: args.endDate,
        },
      }
    );
  }
}
