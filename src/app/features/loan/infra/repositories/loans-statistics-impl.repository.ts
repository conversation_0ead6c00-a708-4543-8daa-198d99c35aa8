import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../shared/shared-criteria';
import { IGeneralLoanStatisticsDto } from '../../domain/dtos/general-loans-statistics.dto';
import { IStatementSummaryDto } from '../../domain/dtos/statement-summary.dto';
import { MerchantLoansStatisticsRepository } from '../../domain/repositories/merchant-loans-statistics.repository';

@Injectable({
  providedIn: 'root',
})
export class LoansStatisticsRepositoryImpl
  implements
    MerchantLoansStatisticsRepository<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<IGeneralLoanStatisticsDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  public getStatisticsByDateRange(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<IGeneralLoanStatisticsDto> {
    return this.#http.get<IGeneralLoanStatisticsDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/order/summary-between`,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
        },
      }
    );
  }

  public getStatementSummary(): Observable<IStatementSummaryDto> {
    const url = `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/order/statement-summary`;
    return this.#http.get<IStatementSummaryDto>(url);
  }
}
