import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { ILegacyReportRequestDto } from '../../domain/dtos/legacy-report-request.dto';
import { MerchantLoansReportRepository } from '../../domain/repositories/merchant-loans-report.repository';

@Injectable({
  providedIn: 'root',
})
export class MerchantLoansLegacyReportRepositoryImpl
  implements
    MerchantLoansReportRepository<ILegacyReportRequestDto, Observable<string>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getLoansForReport(args: ILegacyReportRequestDto): Observable<string> {
    const allBranchesRequest: number[] = [];

    return this.#http.post(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/order/export`,
      allBranchesRequest,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
        },
        responseType: 'text' as const,
      }
    );
  }
}
