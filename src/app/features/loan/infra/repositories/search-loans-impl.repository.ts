import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesRepositoryDto } from '../../../shared/shared-criteria';
import { MerchantLoan } from '../../domain/entities/merchant-loan';
import { MerchantSearchLoansRepository } from '../../domain/repositories/merchant-search-loans.repository';

@Injectable({
  providedIn: 'root',
})
export class SearchLoansRespositoryImpl
  implements
    MerchantSearchLoansRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<MerchantLoan[]>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  public searchByIdPhoneOrEmail(
    args: SharedCriteriaWithBranchOfficesRepositoryDto
  ): Observable<MerchantLoan[]> {
    return this.#http.get<MerchantLoan[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/order/filter`,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
          element: args.search ?? '',
        },
      }
    );
  }
}
