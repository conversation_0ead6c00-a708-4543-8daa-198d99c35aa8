import { AsyncPipe, NgClass } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import {
  NoopValueAccesorDirective,
  ngControlInjector,
} from '@aplazo/shared-ui/forms';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'aplazo-button-group',
  template: `
    @if (currentSelection$ | async; as currentSelection) {
      <div class="inline-flex rounded-md shadow-sm" role="group">
        @for (item of buttons; track item; let f = $first; let l = $last) {
          <button
            type="button"
            class="px-4 py-2 text-dark-secondary bg-white border hover:bg-dark hover:text-light focus:z-10 focus:ring-2 focus:border-aplazo-aplazo active:border-aplazo-aplazo"
            [class.rounded-s-lg]="f"
            [class.rounded-e-lg]="l"
            [ngClass]="{
              'border-dark-tertiary': currentSelection !== item.value,
              'border-aplazo-aplazo text-dark font-semibold':
                currentSelection === item.value
            }"
            (click)="changeSelection(item.value)">
            {{ item.label }}
          </button>
        }
      </div>
    }
  `,
  imports: [NgClass, AsyncPipe],
  hostDirectives: [NoopValueAccesorDirective],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AplazoButtonGroupComponent implements OnInit {
  readonly #ngControl = ngControlInjector();

  @Input()
  buttons: { label: string; value: string }[] = [];

  readonly #currentSelection$ = new BehaviorSubject<string>('');

  currentSelection$ = this.#currentSelection$.asObservable();

  changeSelection(val: string): void {
    if (!val) {
      return;
    }

    this.#ngControl?.control.setValue(val);
    this.#currentSelection$.next(val);
  }

  ngOnInit(): void {
    this.changeSelection(this.buttons[0].value);
  }
}
