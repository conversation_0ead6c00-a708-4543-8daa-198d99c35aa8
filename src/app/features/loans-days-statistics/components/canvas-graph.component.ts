import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  inject,
  input,
  Input,
  OnDestroy,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  CategoryScale,
  Chart,
  Legend,
  LinearScale,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
} from 'chart.js';
import {
  BehaviorSubject,
  combineLatest,
  defer,
  filter,
  lastValueFrom,
  map,
  Subject,
  take,
  takeUntil,
  tap,
  timer,
} from 'rxjs';

const lineColors = [
  '#3772FF',
  '#CDB4DB',
  '#E2EF70',
  '#FFB703',
  '#F038FF',
  '#32A287',
  '#8783D1',
] as const;

const weekdays = [
  'Domingo',
  'Lunes',
  'Martes',
  'Miércoles',
  'Jueves',
  'Viernes',
  'Sábado',
] as const;

type AvailableColor = (typeof lineColors)[number];

class HourDataset {
  readonly fill = false;
  readonly tension = 0.1;
  readonly data: number[];
  readonly borderColor: AvailableColor;
  readonly pointBackgroundColor: AvailableColor;
  readonly label: string;

  constructor(data: number[], label: string | undefined, index?: number) {
    this.data = data;
    this.label = label;
    this.borderColor = lineColors[index ?? 0];
    this.pointBackgroundColor = lineColors[index ?? 0];
  }
}

@Component({
  standalone: true,
  selector: 'aplazo-hours-graph',
  template: ` <canvas #weekdaysGraph width="400" height="100"> </canvas> `,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AplazoCanvasGraphComponent implements AfterViewInit, OnDestroy {
  readonly #cdr = inject(ChangeDetectorRef);
  readonly #destroy$ = new Subject<void>();
  readonly #graphs$ = new BehaviorSubject<number[] | number[][]>([]);

  @Input()
  set graphData(val: number[] | number[][]) {
    if (!val) {
      return;
    }
    this.#graphs$.next(Array.isArray(val) ? val : []);
  }

  @ViewChild('weekdaysGraph', {
    read: ElementRef<HTMLCanvasElement>,
  })
  _canvasChild!: ElementRef<HTMLCanvasElement>;

  readonly #hoursGraphLabels = Array.from({ length: 24 }, (_, i) => `${i}:00`);

  readonly #weekdays = [
    'Domingo',
    'Lunes',
    'Martes',
    'Miércoles',
    'Jueves',
    'Viernes',
    'Sábado',
  ];

  readonly daysOnly = input<boolean>(null, {
    transform: ((value: boolean | null) => Boolean(value)) as any,
  });

  constructor() {
    Chart.register(
      LineController,
      LinearScale,
      CategoryScale,
      PointElement,
      LineElement,
      Tooltip,
      Legend
    );
  }

  ngAfterViewInit(): void {
    combineLatest({
      chart: defer(() => this.#startChart()),
      graphs: this.#graphs$.pipe(),
    })
      .pipe(
        filter(({ graphs, chart }) => !!chart && !!graphs && graphs.length > 0),
        map(({ chart, graphs }) => {
          if (!graphs || !Array.isArray(graphs)) {
            chart.data.datasets[0] = new HourDataset([], undefined);
            return chart;
          }

          const isMatrix =
            graphs.length > 0 && graphs.every(i => Array.isArray(i));

          if (isMatrix) {
            chart.data.datasets = graphs.map(
              (data, i) => new HourDataset(data, weekdays[i], i)
            );
            chart.options.plugins.legend.display = true;
            return chart;
          }

          chart.data.datasets[0] = new HourDataset(
            graphs as number[],
            undefined,
            0
          );
          return chart;
        }),
        tap(chart => {
          chart.resize();
          chart.update();
          this.#cdr.detectChanges();
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  async #startChart(): Promise<Chart> {
    /**
     * workaround to run the execution of the chart creation after the view is initialized
     */
    await lastValueFrom(timer(0).pipe(take(1)));

    return new Chart(this._canvasChild.nativeElement, {
      type: 'line',

      data: {
        labels: this.daysOnly() ? this.#weekdays : this.#hoursGraphLabels,

        datasets: this.daysOnly()
          ? [
              {
                data: this.#graphs$.getValue() as number[],
                borderColor: '#3D72C9',
                fill: false,
                pointBackgroundColor: '#3D72C9',
                tension: 0.1,
              },
            ]
          : [],
      },

      options: {
        plugins: {
          legend: {
            display: false,
            fullSize: true,
          },
          tooltip: {
            enabled: true,
            backgroundColor: '#FAFBFC',
            bodyColor: '#131332',
            bodyFont: {
              size: 14,
            },
            titleColor: '#3D72C9',
            titleFont: {
              size: 16,
            },
            bodySpacing: 6,
            padding: 10,
          },
        },
        responsive: true,
        scales: {
          y: {
            type: 'linear',
            ticks: {
              autoSkip: true,
              includeBounds: true,
            },
          },
        },
      },
    });
  }
}
