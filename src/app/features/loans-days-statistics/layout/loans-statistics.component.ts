import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
} from '@angular/router';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { delay, filter, map, startWith } from 'rxjs';
import { DASH_ROUTES } from '../../../config/app-route-core';

export const dailyLoansLabels = {
  SEMANA: DASH_ROUTES.weekdaysStats,
  HORA: DASH_ROUTES.hoursStats,
  DÍAS: DASH_ROUTES.dayHoursStats,
} as const;

@Component({
  selector: 'app-loans-statistics',
  imports: [AsyncPipe, RouterOutlet, AplazoTabsComponents],
  template: `
    <div class="px-4 md:px-8 pt-4 pb-36">
      <div class="shadow-sm rounded-lg overflow-hidden">
        <aplz-ui-tab-group
          [selectedIndex]="selectedIndex$ | async"
          (tabSelectionChange)="tabSelection($event)">
          @for (label of links; track label) {
            <aplz-ui-tab [label]="label"></aplz-ui-tab>
          }
        </aplz-ui-tab-group>
      </div>

      <router-outlet></router-outlet>
    </div>
  `,
})
export class LoansStatisticsComponent {
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly links = Object.keys(dailyLoansLabels);

  readonly selectedIndex$ = this.#router.events.pipe(
    filter(event => event instanceof NavigationEnd),
    startWith(this.#router),
    delay(0),
    map(() => {
      const currentUrl = this.#router.url;
      const currentTab = Object.entries(dailyLoansLabels).find(([, path]) =>
        currentUrl.endsWith(path)
      );

      if (currentTab) {
        const [label] = currentTab;
        const index = this.links.indexOf(label);

        if (index < 0) {
          return 0;
        }

        return index;
      }

      return 0;
    })
  );

  tabSelection(event: { index: number }): void {
    const finalPath = dailyLoansLabels[this.links[event.index]];

    this.#router.navigate([finalPath], {
      relativeTo: this.#route,
    });
  }
}
