import { Route } from '@angular/router';
import { DASH_ROUTES } from '../../config/app-route-core';
import { setInitialDaysToCriteria } from './resolvers/criteria.resolver';

export default [
  {
    path: '',
    loadComponent: () =>
      import('./layout/loans-statistics.component').then(
        stl => stl.LoansStatisticsComponent
      ),
    resolve: { criteria: setInitialDaysToCriteria },
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: DASH_ROUTES.weekdaysStats,
      },
      {
        path: DASH_ROUTES.weekdaysStats,
        loadComponent: () =>
          import('./pages/weekdays/weekdays.component').then(
            stl => stl.WeekdaysComponent
          ),
      },
      {
        path: DASH_ROUTES.hoursStats,
        loadComponent: () =>
          import('./pages/hours/hours.component').then(
            stl => stl.HoursComponent
          ),
      },
      {
        path: DASH_ROUTES.dayHoursStats,
        loadComponent: () =>
          import('./pages/day-hours/day-hours.component').then(
            stl => stl.DayHoursComponent
          ),
      },
    ],
  },
] satisfies Route[];
