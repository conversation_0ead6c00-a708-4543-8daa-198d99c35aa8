import { AsyncPipe } from '@angular/common';
import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { DAYS, DayByNumber, TemporalService } from '@aplazo/merchant/shared';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import {
  Subject,
  combineLatest,
  map,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { EventManagerService } from '../../../../services/event-manger.service';
import { SharedCriteria } from '../../../../services/shared-criteria.store';
import { StatisticsByDayAndHoursUseCase } from '../../../loan/application/usecases/statistics-by-day-hours.usecase';
import { AplazoButtonGroupComponent } from '../../components/button-group.component';
import { AplazoCanvasGraphComponent } from '../../components/canvas-graph.component';
import { AplazoPadWithPipe } from '../../pipes/pad-with.pipe';
import { AplazoWeekdayPipe } from '../../pipes/weekday.pipe';

@Component({
  selector: 'app-day-hours',
  templateUrl: './day-hours.component.html',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    AplazoDynamicPipe,
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoButtonGroupComponent,
    AplazoPadWithPipe,
    AplazoCanvasGraphComponent,
    AplazoWeekdayPipe,
    AplazoFormDatepickerComponent,
  ],
})
export class DayHoursComponent implements OnInit, OnDestroy, AfterViewInit {
  readonly #criteria = inject(SharedCriteria);
  readonly #i18n = inject(I18NService);
  readonly #eventManager = inject(EventManagerService);
  readonly #temporal = inject(TemporalService);
  readonly #dayHoursStatsUseCase = inject(StatisticsByDayAndHoursUseCase);
  readonly #scope = 'daily-combine';

  readonly #destroy$ = new Subject<void>();

  buttonsDescription = [
    { label: 'Clientes', value: 'customers' },
    { label: 'Venta', value: 'salesAmount' },
    { label: 'Ticket promedio', value: 'salesAmountAvg' },
    { label: 'Loans', value: 'salesOrder' },
  ];

  readonly weekdays = Object.keys(DAYS).map(Number);
  readonly graphTypeSelection = new FormControl<string>('');
  readonly daySelectionControl = new FormControl<number[]>([]);
  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;

  readonly #titleUIText$ = this.#i18n
    .getTranslateObjectByKey<{
      mainTitle: string;
      actionsTitle: string;
    }>({
      key: 'headers',
      scope: this.#scope,
    })
    .pipe(shareReplay(1));

  readonly statsTitle$ = this.#titleUIText$.pipe(map(s => s.mainTitle));

  readonly graphsTitle$ = this.#titleUIText$.pipe(map(s => s.actionsTitle));

  dateRangePickerTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    daterangePicker: string;
  }>({
    key: 'daterangePicker',
    scope: this.#scope,
  });

  statsTableInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    timePeriod: string;
    customers: string;
    salesAmount: string;
    salesAmountAvg: string;
    salesOrder: string;
  }>({
    key: 'table',
    scope: this.#scope,
  });

  weekdaysSelectTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    tooltip: string;
    placeholder: string;
  }>({
    key: 'weekdaysSelect',
    scope: this.#scope,
  });

  titlesTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    mainTitle: string;
    actionsTitle: string;
  }>({
    key: 'headers',
    scope: this.#scope,
  });

  readonly stats$ = this.#criteria.criteria$.pipe(
    switchMap(criteria => this.#dayHoursStatsUseCase.execute(criteria)),
    map(rawStats => {
      return {
        content: rawStats.content
          .sort((a, b) => a.timePeriod - b.timePeriod)
          .map(stat => ({
            ...stat,
            salesAmountAvg: stat.salesAmount / stat.salesOrder || 0,
          })),
        graphs: rawStats.graphs,
      };
    }),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  readonly hoursGraphsData$ = combineLatest({
    stats: this.stats$,
    type: this.graphTypeSelection.valueChanges.pipe(
      startWith(this.buttonsDescription[0].value)
    ),
  }).pipe(
    tap(({ type }) => {
      this.#eventManager.sendTrackEvent('graphTypeSelection', {
        graphType: type,
      });
    }),
    map(({ stats, type }) => stats.graphs.map(i => i[type])),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    stats: this.stats$,
    statsTitle: this.statsTitle$,
    graphsTitle: this.graphsTitle$,
    dateRangePickerTextTemplate: this.dateRangePickerTextTemplate$,
    statsTableInjectedText: this.statsTableInjectedText$,
    weekdaysSelectTextTemplate: this.weekdaysSelectTextTemplate$,
    titlesTextTemplate: this.titlesTextTemplate$,
    hoursGraphsData: this.hoursGraphsData$,
  }).pipe(takeUntil(this.#destroy$));

  ngOnInit(): void {
    this.#criteria.days$.pipe(take(1)).subscribe(days => {
      this.daySelectionControl.setValue(days);
    });

    this.#eventManager.sendTrackEvent('tabSelection', {});
  }

  ngAfterViewInit(): void {
    this.daySelectionControl.valueChanges
      .pipe(
        tap(days => {
          this.#criteria.setDays(days as DayByNumber[]);
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(value => {
          if (Array.isArray(value) && value.length === 2) {
            const [start, end] = value;

            const startDate = this.#temporal.formatRawDateDayFirst(start);
            const endDate = this.#temporal.formatRawDateDayFirst(end);

            this.#criteria.setDateRange({ startDate, endDate });
            this.#eventManager.sendTrackEvent('dateRange', {
              startDate,
              endDate,
            });
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
