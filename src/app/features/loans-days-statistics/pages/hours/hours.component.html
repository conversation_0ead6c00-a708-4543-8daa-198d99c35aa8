@if (vm$ | async; as context) {
  <div
    class="flex flex-col md:flex-row items-center justify-center md:justify-between gap-y-4 mt-4">
    <h3 class="mb-8 font-semibold text-center md:text-start">
      {{ context.statsTitle }}
    </h3>

    <aplz-ui-form-datepicker
      [formControl]="dateControl"
      [rangeEnabled]="true"
      [maxDate]="todayRawDateDayFirst"
      [centerText]="true"
      legend="Seleccione el rango de fechas">
    </aplz-ui-form-datepicker>
  </div>
  <div class="-mt-6 overflow-auto max-h-96">
    <aplz-ui-card>
      <table aplzSimpleTable aria-label="Stats Table by Hour">
        <tr aplzSimpleTableHeaderRow>
          <th
            aplzSimpleTableHeaderCell
            scope="col"
            class="text-center capitalize">
            {{ context.statsTableInjectedText.timePeriod }}
          </th>
          <th
            aplzSimpleTableHeaderCell
            scope="col"
            class="text-center capitalize">
            {{ context.statsTableInjectedText.salesAmount }}
          </th>
          <th
            aplzSimpleTableHeaderCell
            scope="col"
            class="text-center capitalize">
            {{ context.statsTableInjectedText.salesOrder }}
          </th>
          <th
            aplzSimpleTableHeaderCell
            scope="col"
            class="text-center capitalize">
            {{ context.statsTableInjectedText.customers }}
          </th>
          <th
            aplzSimpleTableHeaderCell
            scope="col"
            class="text-center capitalize">
            {{ context.statsTableInjectedText.salesAmountAvg }}
          </th>
        </tr>

        @for (item of context.stats.content; track item) {
          <tr aplzSimpleTableBodyRow>
            <td aplzSimpleTableBodyCell class="text-center tabular-nums">
              {{ item.timePeriod | aplazoPadWith }}
            </td>
            <td
              aplzSimpleTableBodyCell
              class="text-center text-dark tabular-nums font-semibold">
              {{ item.salesAmount | aplzDynamicPipe: 'currency' }}
            </td>
            <td aplzSimpleTableBodyCell class="tabular-nums text-center">
              {{ item.salesOrder | aplzDynamicPipe: 'decimal' }}
            </td>
            <td aplzSimpleTableBodyCell class="tabular-nums text-center">
              {{ item.customers | aplzDynamicPipe: 'decimal' }}
            </td>
            <td
              aplzSimpleTableBodyCell
              class="text-center text-dark tabular-nums font-medium">
              {{ item.salesAmountAvg | aplzDynamicPipe: 'currency' }}
            </td>
          </tr>
        }
      </table>
    </aplz-ui-card>
  </div>

  <h3 class="mt-8 mb-4 font-semibold text-center md:text-start">
    {{ context.graphsTitle }}
  </h3>

  <div class="overflow-x-auto">
    <aplz-ui-card appearance="shadow">
      <div class="mb-10">
        <aplz-ui-button-group
          [formControl]="graphTypeSelection"
          [buttons]="buttonsDescription"></aplz-ui-button-group>
      </div>

      <aplazo-hours-graph [graphData]="context.hoursGraphsData">
      </aplazo-hours-graph>
    </aplz-ui-card>
  </div>
}
