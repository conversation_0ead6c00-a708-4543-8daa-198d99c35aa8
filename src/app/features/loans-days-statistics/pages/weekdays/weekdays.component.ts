import { AsyncPipe } from '@angular/common';
import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import {
  B2BDateRange,
  LoaderService,
  NotifierService,
  TemporalService,
} from '@aplazo/merchant/shared';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormDatepickerComponent } from '@aplazo/shared-ui/forms';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import {
  Observable,
  Subject,
  combineLatest,
  combineLatestWith,
  map,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { EventManagerService } from '../../../../services/event-manger.service';
import { SharedCriteria } from '../../../../services/shared-criteria.store';
import { StatisticsByWeekdayUseCase } from '../../../loan/application/usecases/statistics-by-weekday.usecase';
import { IDayliLoansStatisticsReponseDto } from '../../../loan/domain/dtos/dayli-loans-statistics.dto';
import { MerchantLoanStatisticsByWeekdayRepository } from '../../../loan/domain/repositories/merchant-loan-statistics-by-weekday.repository';
import { AplazoButtonGroupComponent } from '../../components/button-group.component';
import { AplazoCanvasGraphComponent } from '../../components/canvas-graph.component';
import { AplazoWeekdayPipe } from '../../pipes/weekday.pipe';

@Component({
  selector: 'app-weekdays',
  templateUrl: './weekdays.component.html',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    AplazoDynamicPipe,
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoButtonGroupComponent,
    AplazoWeekdayPipe,
    AplazoCanvasGraphComponent,
    AplazoFormDatepickerComponent,
  ],
})
export class WeekdaysComponent implements OnInit, OnDestroy {
  readonly #weekdayStatsRepository: MerchantLoanStatisticsByWeekdayRepository<
    B2BDateRange,
    Observable<IDayliLoansStatisticsReponseDto[]>
  > = inject(MerchantLoanStatisticsByWeekdayRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #criteria = inject(SharedCriteria);
  readonly #i18n = inject(I18NService);
  readonly #eventManager = inject(EventManagerService);
  readonly #temporal = inject(TemporalService);
  readonly #scope = 'daily-weekdays';

  readonly #destroy$ = new Subject<void>();

  buttonsDescription: { label: string; value: string }[] = [
    { label: 'Clientes', value: 'customers' },
    { label: 'Venta', value: 'salesAmount' },
    { label: 'Ticket promedio', value: 'salesAmountAvg' },
    { label: 'Loans', value: 'salesOrder' },
  ];

  readonly graphTypeSelection = new FormControl<string>('');
  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;

  weekdayStatsTableInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    timePeriod: string;
    customers: string;
    salesAmount: string;
    salesAmountAvg: string;
    salesOrder: string;
  }>({
    key: 'table',
    scope: this.#scope,
  });

  readonly #titleUIText$ = this.#i18n
    .getTranslateObjectByKey<{
      mainTitle: string;
      actionsTitle: string;
    }>({
      key: 'headers',
      scope: this.#scope,
    })
    .pipe(shareReplay(1));

  readonly statsTitle$ = this.#titleUIText$.pipe(map(s => s.mainTitle));

  readonly graphsTitle$ = this.#titleUIText$.pipe(map(s => s.actionsTitle));

  readonly #weekdaysStatsUseCase = new StatisticsByWeekdayUseCase(
    this.#weekdayStatsRepository,
    this.#loader,
    this.#notifier
  );

  readonly stats$ = this.#criteria.criteria$.pipe(
    switchMap(criteria =>
      this.#weekdaysStatsUseCase.execute(criteria).pipe(take(1))
    ),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  readonly daysGraphData$: Observable<number[]> = this.stats$.pipe(
    map(data => data.graphs),
    combineLatestWith(
      this.graphTypeSelection.valueChanges.pipe(
        startWith(this.buttonsDescription[0].value),
        takeUntil(this.#destroy$)
      )
    ),
    tap(([, type]) => {
      this.#eventManager.sendTrackEvent('graphTypeSelection', {
        graphType: type,
      });
    }),
    map(([graphs, type]) => graphs[type]),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    stats: this.stats$,
    statsTitle: this.statsTitle$,
    graphsTitle: this.graphsTitle$,
    daysGraphData: this.daysGraphData$,
    weekdayStatsTableInjectedText: this.weekdayStatsTableInjectedText$,
  }).pipe(takeUntil(this.#destroy$));

  ngOnInit(): void {
    this.#eventManager.sendTrackEvent('tabSelection', {});

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(value => {
          if (Array.isArray(value) && value.length === 2) {
            const [start, end] = value;

            const startDate = this.#temporal.formatRawDateDayFirst(start);
            const endDate = this.#temporal.formatRawDateDayFirst(end);

            this.#criteria.setDateRange({ startDate, endDate });
            this.#eventManager.sendTrackEvent('dateRange', {
              startDate,
              endDate,
            });
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
