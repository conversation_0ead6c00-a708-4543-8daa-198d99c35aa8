import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  standalone: true,
  name: 'aplazoWeekday',
})
export class AplazoWeekdayPipe implements PipeTransform {
  readonly #weekdays = [
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Sábado',
  ];

  transform(value: number): string {
    if (
      value == null ||
      isNaN(+value) ||
      value < 0 ||
      value > this.#weekdays.length
    ) {
      return '';
    }

    return this.#weekdays[value] ?? '';
  }
}
