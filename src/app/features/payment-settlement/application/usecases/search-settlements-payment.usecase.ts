import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  Transaction,
} from '@aplazo/merchant/shared';
import { Observable, catchError, take, throwError } from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { SearchSettlementsPaymentRepository } from '../../domain/repositories/search-settlements-payment.repository';

export class SearchSettlementsPaymentUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<Transaction[]>
    >
{
  private repository: SearchSettlementsPaymentRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<Transaction[]>
  >;

  private loaderService: LoaderService;

  private notifierService: NotifierService;

  constructor(
    repository: SearchSettlementsPaymentRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<Transaction[]>
    >,
    loaderService: LoaderService,
    notifierService: NotifierService
  ) {
    this.repository = repository;
    this.loaderService = loaderService;
    this.notifierService = notifierService;
  }

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<Transaction[]> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.repository.searchByIdPhoneEmail(criteria).pipe(
        take(1),
        catchError(error => throwError(() => error))
      );
    } catch (error) {
      console.error(error);

      this.notifierService.warning({
        title: 'Ups',
        message:
          (error as any)?.message || 'Algo salió mal con la lista de pedidos',
      });
      throw error;
    } finally {
      this.loaderService.hide(idLoader);
    }
  }
}
