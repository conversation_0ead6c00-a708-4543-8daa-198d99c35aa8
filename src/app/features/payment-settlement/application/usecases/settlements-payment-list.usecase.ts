import {
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, take, throwError } from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { SettlementsPaymentResponseDto } from '../../domain/dtos/settlements-payment-response.dto';
import { SettlementsPaymentRepository } from '../../domain/repositories/settlements-payment.repository';

export class SettlementsPaymentUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<SettlementsPaymentResponseDto>
    >
{
  private repository: SettlementsPaymentRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<SettlementsPaymentResponseDto>
  >;
  private loaderService: LoaderService;
  private notifierService: NotifierService;

  constructor(
    repository: SettlementsPaymentRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<SettlementsPaymentResponseDto>
    >,
    loaderService: LoaderService,
    notifierService: NotifierService
  ) {
    this.repository = repository;
    this.loaderService = loaderService;
    this.notifierService = notifierService;
  }

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<SettlementsPaymentResponseDto> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.repository.getList(criteria).pipe(
        take(1),
        catchError(error => throwError(() => error)),
        finalize(() => this.loaderService.hide(idLoader))
      );
    } catch (error) {
      console.error(error);

      this.notifierService.warning({
        title: 'Ups',
        message:
          (error as any)?.message ||
          'Algo salió mal con la lista de pagos y liquidaciones',
      });
      throw error;
    } finally {
      this.loaderService.hide(idLoader);
    }
  }
}
