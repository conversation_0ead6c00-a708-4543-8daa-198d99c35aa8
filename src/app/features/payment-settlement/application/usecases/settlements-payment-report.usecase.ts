import {
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import {
  Observable,
  catchError,
  defer,
  map,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { SettlementsPaymentReportRepository } from '../../domain/repositories/settlements-payment-report.repository';

export class SettlementsPaymentReportUseCase
  implements
    BaseUsecase<SharedCriteriaWithBranchOfficesUIDto, Observable<string[][]>>
{
  private repository: SettlementsPaymentReportRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<string>
  >;

  private loaderAdapter: LoaderService;
  private notifierService: NotifierService;
  private fileGenerator: FileGeneratorService<Promise<string[][]>>;
  private reportResponseTransformService: CsvMapperService;

  constructor(
    repository: SettlementsPaymentReportRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<string>
    >,
    loaderAdapter: LoaderService,
    notifierService: NotifierService,
    fileGenerator: FileGeneratorService<Promise<string[][]>>,
    responseTransform: CsvMapperService
  ) {
    this.repository = repository;
    this.loaderAdapter = loaderAdapter;
    this.notifierService = notifierService;
    this.fileGenerator = fileGenerator;
    this.reportResponseTransformService = responseTransform;
  }

  public execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<string[][]> {
    const loaderId = this.loaderAdapter.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);
      const reportName = `reporte_transacciones_${criteria.dateRange.startDate}_a_${criteria.dateRange.endDate}`;

      // The endpoint server does not prepared for receive a branch offices selection
      return this.repository
        .getContentForReport({ ...criteria, branchOffices: [] })
        .pipe(
          map(response =>
            this.reportResponseTransformService.transform(response)
          ),
          take(1),
          switchMap(content => {
            if (!content || content.length === 0) {
              // Empty report
              return of([] as string[][]).pipe(
                tap(() => {
                  this.notifierService.info({
                    title: 'Reporte vacio',
                    message:
                      'No existen compras en el rango de fechas solicitado.',
                  });
                  this.loaderAdapter.hide(loaderId);
                })
              );
            }

            return defer(() =>
              this.fileGenerator.generateFileAndDownload(content, reportName)
            ).pipe(
              take(1),
              tap(() => {
                // It is important to mantain loader just before the notifier
                // because the defer action can take some time to complete
                this.loaderAdapter.hide(loaderId);
                // Success download
                this.notifierService.success({
                  title: 'Reporte generado',
                  message:
                    'El reporte se ha generado correctamente y la descarga comenzara pronto.',
                });
              })
            );
          }),

          catchError(err => {
            throw err;
          })
        );
    } catch (error) {
      this.notifierService.error({
        title: 'Error',
        message: 'Error con el servicio para obtener el reporte Excel',
      });

      this.loaderAdapter.hide(loaderId);
      throw error;
    }
  }
}
