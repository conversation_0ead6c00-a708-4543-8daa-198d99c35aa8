import {
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { Observable, finalize, map } from 'rxjs';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../shared/shared-criteria.mapper';
import { SettlementsPaymentStatistics } from '../../domain/entities/settlements-pyament-statistics';
import { SettlementsPaymentStatisticsRepository } from '../../domain/repositories/settlements-payment-statistics.repository';

export class SettlementsPaymentStatisticsUseCase
  implements
    BaseUsecase<
      SharedCriteriaWithBranchOfficesUIDto,
      Observable<SettlementsPaymentStatistics>
    >
{
  private repository: SettlementsPaymentStatisticsRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<SettlementsPaymentStatistics>
  >;

  private loaderService: LoaderService;
  private notifier: NotifierService;

  constructor(
    repository: SettlementsPaymentStatisticsRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<SettlementsPaymentStatistics>
    >,
    loaderService: LoaderService,
    notifier: NotifierService
  ) {
    this.repository = repository;
    this.loaderService = loaderService;
    this.notifier = notifier;
  }

  execute(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): Observable<SettlementsPaymentStatistics> {
    const idLoader = this.loaderService.show();

    try {
      const criteria = SharedCriteriaMapper.fromUIToRepository(args);

      return this.repository.getStatistics(criteria).pipe(
        map(response => {
          if (!response) {
            return {
              merchantId: -1,
              amountToPay: 0,
              feeRevenue: 0,
              salesAmount: 0,
              salesOrder: 0,
            };
          }
          return response;
        }),
        finalize(() => this.loaderService.hide(idLoader))
      );
    } catch (error) {
      console.error(error);
      this.loaderService.hide(idLoader);
      this.notifier.warning({
        title: 'Ups',
        message:
          (error as any)?.message || 'Algo salió mal con la lista de pedidos',
      });
      throw error;
    }
  }
}
