import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { SearchSettlementsPaymentRepository } from '../../domain/repositories/search-settlements-payment.repository';
import { SettlementsPaymentReportRepository } from '../../domain/repositories/settlements-payment-report.repository';
import { SettlementsPaymentStatisticsRepository } from '../../domain/repositories/settlements-payment-statistics.repository';
import { SettlementsPaymentRepository } from '../../domain/repositories/settlements-payment.repository';
import { SearchSettlementsPaymentRepositoryImpl } from '../repositories/search-settlements-payment-impl.repository';
import { SettlementsPaymentListRepositoryImpl } from '../repositories/settlements-payment-list-impl.repository';
import { SettlementsPaymentReportRepositoryImpl } from '../repositories/settlements-payment-report-impl.repository';
import { SettlementsPaymentStatsRepositoryImpl } from '../repositories/settlements-payment-stats-impl.repository';

export function providePaymentSettlementsRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: SearchSettlementsPaymentRepository,
      useClass: SearchSettlementsPaymentRepositoryImpl,
    },
    {
      provide: SettlementsPaymentRepository,
      useClass: SettlementsPaymentListRepositoryImpl,
    },
    {
      provide: SettlementsPaymentReportRepository,
      useClass: SettlementsPaymentReportRepositoryImpl,
    },
    {
      provide: SettlementsPaymentStatisticsRepository,
      useClass: SettlementsPaymentStatsRepositoryImpl,
    },
  ]);
}
