@if (vm$ | async; as context) {
  <section class="px-4 md:px-8 pt-4">
    <div class="flex flex-col md:flex-row flex-wrap gap-x-3 items-center">
      <aplz-ui-form-datepicker
        [formControl]="dateControl"
        [rangeEnabled]="true"
        [maxDate]="todayRawDateDayFirst"
        [centerText]="true"
        legend="Seleccione el rango de fechas">
      </aplz-ui-form-datepicker>

      <div class="flex items-center">
        <aplz-ui-select [formControl]="orderTypeControl" label="mostrar">
          @for (opt of context.orderTypes; track opt) {
            <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
          }
        </aplz-ui-select>
      </div>

      <div class="flex flex-grow-0 flex-shrink-0 items-center justify-end">
        <aplz-ui-select
          [formControl]="branchesControl"
          [multiple]="true"
          label="Sucursal"
          allSelectionLabel="Todas">
          @for (branch of context.allBranches; track branch) {
            <aplz-ui-option [ngValue]="branch.id" [label]="branch.name">
            </aplz-ui-option>
          }
        </aplz-ui-select>
      </div>
      @if (context.hasSearch === false && !context.hasDaterangeIntervalError) {
        <div
          class="mb-8 flex-grow flex-shrink-0 flex justify-center md:justify-end items-center">
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="sm"
            [rounded]="true"
            (click)="download()">
            <span class="mr-1">
              <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
            </span>
            {{ context.downloadButtonInjectedText.label ?? '' }}
          </button>
        </div>
      }
    </div>
  </section>

  <section
    class="-mt-4 pt-8 md:pt-0 px-4 md:px-8 grid gap-4 grid-cols-1 md:grid-cols-2 2xl:grid-cols-4">
    @for (stat of context.settlementsPaymentStats; track stat) {
      <aplz-ui-metric-card>
        <aplz-ui-metric-card-header>
          <div
            class="aplazo-metric-card__label"
            [class.tooltip--with-space]="
              stat.helpTooltip && stat.tooltipSpaceActive
            ">
            <span class="aplazo-metric-card__label-title text-base">
              {{ stat.statCardTitle }}
            </span>
            @if (stat.helpTooltip) {
              <aplz-ui-icon
                name="question-mark-circle"
                size="xs"
                [aplzTooltip]="stat.helpTooltip ?? ''"></aplz-ui-icon>
            }
          </div>
        </aplz-ui-metric-card-header>
        <aplz-ui-metric-card-content>
          <div class="text-2xl font-semibold mb-1">
            {{ stat.value | aplzDynamicPipe: stat.pipeName }}
          </div>
        </aplz-ui-metric-card-content>
      </aplz-ui-metric-card>
    }
  </section>

  <div
    class="px-4 md:px-8 mt-4 md:mt-0 flex flex-col md:flex-row items-center justify-center md:justify-between"
    [class.min-h-28]="!context.hasDaterangeIntervalError">
    @if (!context.hasDaterangeIntervalError) {
      <h5 class="text-center lg:text-left">
        @if (context.counterLabelTextTemplate; as counterLabelTextTemplate) {
          <span class="whitespace-nowrap font-medium text-lg">
            {{ context.loansCounting | i18nPlural: counterLabelTextTemplate }}
          </span>
        }
      </h5>
    }

    <div class="md:mt-8 max-w-full md:max-w-[310px]">
      <aplz-ui-search
        [textUI]="context.searchbarInjectedText"
        [formControl]="searchControl"
        [minLength]="minLength"
        [hidden]="hasDaterangeIntervalError"></aplz-ui-search>
    </div>
  </div>

  <section class="px-4 md:px-8 -mt-4 pb-16">
    @if (
      (hasSettlementsPayment$ | async) === true && !hasDaterangeIntervalError
    ) {
      <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
        <table aplzSimpleTable aria-label="Settlements List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.id }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.date }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.sales }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.feeRevenue }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.payMerchant }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.status }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.statusPayment }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.cartId }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.settlementsPaymentListInjectedText.branch }}
            </th>
          </tr>

          @for (item of context.settlementsPayment; track item) {
            <tr aplzSimpleTableBodyRow>
              <td
                aplzSimpleTableBodyCell
                class="font-semibold tabular-nums text-center">
                <p>
                  {{ item.id }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                <p>
                  {{ item.creationDate | aplzDynamicPipe: 'date' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                <p class="pe-2">
                  {{ item.salesAmount | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                <p class="pe-2">
                  {{ item.feeRevenue | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                <p class="pe-2">
                  {{ item.payMerchantAmount | aplzDynamicPipe: 'currency' }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                <span
                  class="font-semibold inline-block px-5 py-3 rounded-lg whitespace-nowrap"
                  [class.bg-special-success]="
                    item.status.toLowerCase() === 'aprobado'
                  "
                  [class.bg-special-warning]="
                    ['cancelado', 'no completado'].includes(
                      item.status.toLowerCase()
                    )
                  ">
                  {{ item.status }}
                </span>
              </td>
              <td aplzSimpleTableBodyCell class="text-center">No disponible</td>
              <td
                aplzSimpleTableBodyCell
                class="tabular-nums truncate text-center"
                colspan="1">
                {{ item.cartId }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.branch }}
              </td>
            </tr>
          }
        </table>
      </div>
      <div class="mt-3">
        @if (context.hasSearch === false) {
          <aplz-ui-pagination
            [totalPages]="context.pagesByLoansCounting"
            [currentPage]="context.currentPage"
            (selectedPage)="changePage($event)">
          </aplz-ui-pagination>
        }
      </div>
    } @else {
      <div
        class="bg-light py-4 rounded-lg shadow-md overflow-x-auto"
        [class.mt-12]="context.hasDaterangeIntervalError">
        @if (context.hasSearch === true) {
          @if (context.emptySearchInjectedText; as emptyMessageUIText) {
            <aplz-ui-common-message
              [i18Text]="{
                title: emptyMessageUIText.title,
                description: emptyMessageUIText.description
              }"
              imgName="emptyLoans">
            </aplz-ui-common-message>
          }
        } @else {
          @if (!context.hasDaterangeIntervalError) {
            @if (context.emptyLoansInjectedText; as emptyMessageUIText) {
              <aplz-ui-common-message
                [i18Text]="{
                  title: emptyMessageUIText.title,
                  description: emptyMessageUIText.description
                }"
                imgName="emptyLoans">
                <aplz-ui-message-actions>
                  @if (emptyMessageUIText?.button?.label) {
                    <button
                      aplzButton
                      aplzAppearance="solid"
                      aplzColor="dark"
                      size="md"
                      class="mx-auto min-w-56"
                      (click)="clickEmptyLoansButton()">
                      {{ emptyMessageUIText.button?.label }}
                    </button>
                  }
                </aplz-ui-message-actions>
              </aplz-ui-common-message>
            }
          } @else {
            <div class="pt-8">
              <aplz-ui-common-message
                [i18Text]="{
                  title: 'Error en el rango de fechas',
                  description:
                    'Las consultas del reporte solo permiten un período máximo de ' +
                    maxDaterangeIntervalInDays +
                    ' días.'
                }">
              </aplz-ui-common-message>
            </div>
          }
        }
      </div>
    }
  </section>
}
