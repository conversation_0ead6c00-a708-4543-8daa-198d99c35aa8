import { Async<PERSON>ipe, I18nPluralPipe } from '@angular/common';
import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import {
  LoaderService,
  NotifierService,
  RedirectionService,
  StatusGroupingId,
  TemporalService,
  Transaction,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { iconDownloadTray } from '@aplazo/ui-icons';
import { addDays, isWithinInterval } from 'date-fns';
import {
  BehaviorSubject,
  MonoTypeOperatorFunction,
  Observable,
  Subject,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  filter,
  map,
  pipe,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { BranchOfficesStore } from '../../../../services/branch-offices.store';
import { EventManagerService } from '../../../../services/event-manger.service';
import { SharedCriteria } from '../../../../services/shared-criteria.store';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../shared/shared-criteria';
import { SearchSettlementsPaymentUseCase } from '../../application/usecases/search-settlements-payment.usecase';
import { SettlementsPaymentUseCase } from '../../application/usecases/settlements-payment-list.usecase';
import { SettlementsPaymentReportUseCase } from '../../application/usecases/settlements-payment-report.usecase';
import { SettlementsPaymentStatisticsUseCase } from '../../application/usecases/settlements-payment-statistics.usecase';
import { SettlementsPaymentResponseDto } from '../../domain/dtos/settlements-payment-response.dto';
import { SettlementsPaymentStatistics } from '../../domain/entities/settlements-pyament-statistics';
import { SearchSettlementsPaymentRepository } from '../../domain/repositories/search-settlements-payment.repository';
import { SettlementsPaymentReportRepository } from '../../domain/repositories/settlements-payment-report.repository';
import { SettlementsPaymentStatisticsRepository } from '../../domain/repositories/settlements-payment-statistics.repository';
import { SettlementsPaymentRepository } from '../../domain/repositories/settlements-payment.repository';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';

interface IStatCardInjectedTextUI {
  statCardTitle: string;
  helpTooltip?: string;
}
interface ISettlementsPaymentStatisticsInjectedTextUi {
  salesAmount: IStatCardInjectedTextUI;
  salesOrder: IStatCardInjectedTextUI;
  amountToPay: IStatCardInjectedTextUI;
  feeRevenue: IStatCardInjectedTextUI;
}

const ORDER_TYPES: Readonly<Record<string, StatusGroupingId>> = {
  'Todos los pedidos': 'all',
  Aprobados: 'approved',
  'No completados': 'cancelled',
} as const;

@Component({
  selector: 'app-payment-settlement',
  templateUrl: './payment-settlement.component.html',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    I18nPluralPipe,
    AplazoDynamicPipe,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoPaginationComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoSearchInputComponent,
    AplazoFormDatepickerComponent,
    AplazoMetricCardComponents,
    AplazoTooltipDirective,
  ],
})
export class PaymentSettlementComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  readonly #settlementsPaymentListRepository: SettlementsPaymentRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<SettlementsPaymentResponseDto>
  > = inject(SettlementsPaymentRepository);
  readonly #settlementsPaymentStatsRepository: SettlementsPaymentStatisticsRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<SettlementsPaymentStatistics>
  > = inject(SettlementsPaymentStatisticsRepository);
  readonly #settlementsPaymentReportRepository: SettlementsPaymentReportRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<string>
  > = inject(SettlementsPaymentReportRepository);
  readonly #searchSettlementsPaymentRepository: SearchSettlementsPaymentRepository<
    SharedCriteriaWithBranchOfficesRepositoryDto,
    Observable<Transaction[]>
  > = inject(SearchSettlementsPaymentRepository);
  readonly #loader = inject(LoaderService);
  readonly #criteria = inject(SharedCriteria);
  readonly #branchOfficesStore = inject(BranchOfficesStore);
  readonly #notifier = inject(NotifierService);
  readonly #redirecter = inject(RedirectionService);
  readonly #fileGenerator = inject(FileGeneratorService<Promise<string[][]>>);
  readonly #csvMapper = inject(CsvMapperService);
  readonly #i18n = inject(I18NService);
  readonly #eventManager = inject(EventManagerService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #scope = 'settlements';
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #temporal = inject(TemporalService);

  readonly #settlementsPaymentListUseCase = new SettlementsPaymentUseCase(
    this.#settlementsPaymentListRepository,
    this.#loader,
    this.#notifier
  );

  readonly #settlementsPaymentStatsUseCase =
    new SettlementsPaymentStatisticsUseCase(
      this.#settlementsPaymentStatsRepository,
      this.#loader,
      this.#notifier
    );

  readonly #settlementsPaymentReportUseCase =
    new SettlementsPaymentReportUseCase(
      this.#settlementsPaymentReportRepository,
      this.#loader,
      this.#notifier,
      this.#fileGenerator,
      this.#csvMapper
    );

  readonly #searchSettlementsPaymentUseCase =
    new SearchSettlementsPaymentUseCase(
      this.#searchSettlementsPaymentRepository,
      this.#loader,
      this.#notifier
    );

  readonly #destroy$ = new Subject<void>();

  readonly minLength = 3;

  readonly searchControl = new FormControl<string>('');
  readonly #searchControlFac = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });
  hasSearch$ = this.searchControl.valueChanges.pipe(
    this.#searchControlFac.hasActiveSearch()
  );
  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  dateRangePickerTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    tooltip: string;
  }>({
    key: 'daterangePicker',
    scope: this.#scope,
  });
  searchbarInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: this.#scope,
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  emptyLoansInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button?: {
      label: string;
    };
  }>({
    key: 'emptyLoans',
    scope: this.#scope,
  });
  emptySearchInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: this.#scope,
  });
  branchOfficeInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    form: {
      label: string;
    };
    notification: {
      error: {
        title: string;
        message?: string;
      };
    };
    selection: {
      allSelected: string;
      emptyList: string;
    };
  }>({
    key: 'branchOffices',
    scope: this.#scope,
  });
  downloadButtonInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    label: string;
  }>({
    key: 'downloadButton',
    scope: this.#scope,
  });
  settlementsPaymentListInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    id: string;
    date: string;
    sales: string;
    feeRevenue: string;
    payMerchant: string;
    status: string;
    statusPayment: string;
    cartId: string;
    branch: string;
  }>({
    key: 'list',
    scope: this.#scope,
  });
  settlementsPaymentStatsInjectedText$ =
    this.#i18n.getTranslateObjectByKey<ISettlementsPaymentStatisticsInjectedTextUi>(
      {
        key: 'stats',
        scope: this.#scope,
      }
    );
  allLoanLabels$ = this.#i18n.getTranslateObjectByKey<{ enabled: boolean }>({
    key: 'allLoanStatusLabels',
    scope: this.#scope,
  });

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;

  orderTypes$ = this.allLoanLabels$.pipe(
    map(({ enabled }) => {
      if (enabled) {
        return Object.keys(ORDER_TYPES);
      }
      return Object.keys(ORDER_TYPES).filter(key => key.includes('Aprobados'));
    }),
    take(1)
  );

  orderTypeControl = new FormControl<string>('Aprobados', {
    nonNullable: true,
  });

  allBranches$ = this.#branchOfficesStore.branchOffices$();
  branchesControl: FormControl<number[] | null> = new FormControl<
    number[] | null
  >([]);

  counterLabelTextTemplate$: Observable<Record<string, string>> = this.#i18n
    .getTranslateObjectByKey<Record<string, string>>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      })),
      takeUntil(this.#destroy$)
    );

  maxDaterangeIntervalInDays = 90;
  hasDaterangeIntervalError = false;

  currentDateRange$ = this.#criteria.dateRange$.pipe(takeUntil(this.#destroy$));
  hasDaterangeIntervalError$ = this.#criteria.dateRange$.pipe(
    map(dateRange => {
      const startDate = this.#temporal.fromStringToDate(dateRange.startDate);
      const endDate = this.#temporal.fromStringToDate(dateRange.endDate);
      const withAddedDays = addDays(startDate, this.maxDaterangeIntervalInDays);
      const isWithin = isWithinInterval(endDate, {
        start: startDate,
        end: withAddedDays,
      });

      return !isWithin;
    }),
    takeUntil(this.#destroy$)
  );

  currentPage$ = this.#criteria.page$.pipe(takeUntil(this.#destroy$));
  selectedBranches$ = this.#criteria.selectedBranchOffices$.pipe(
    takeUntil(this.#destroy$)
  );

  readonly #loansCounting$ = new BehaviorSubject<number>(0);

  loansCounting$ = this.#loansCounting$.pipe(takeUntil(this.#destroy$));
  pagesByLoansCounting$ = this.#loansCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  readonly #statCardKeys = [
    'salesAmount',
    'salesOrder',
    'amountToPay',
    'feeRevenue',
  ];

  readonly settlementsPaymentStats$ = this.#criteria.criteria$.pipe(
    this.#filterSearchStream(),
    switchMap(criteria =>
      this.#settlementsPaymentStatsUseCase.execute(criteria).pipe(take(1))
    ),
    combineLatestWith(this.settlementsPaymentStatsInjectedText$.pipe(take(1))),
    map(([stats, i18nText]) => {
      return this.#statCardKeys.map(key => {
        const pipeName: ValidDynamicPipesNames = ['salesOrder'].includes(key)
          ? 'decimal'
          : 'currency';

        return {
          isDarkMode: false,
          statCardKey: key,
          statCardTitle: i18nText[key].statCardTitle,
          value: String(stats[key]),
          helpTooltip: i18nText[key].helpTooltip,
          tooltipSpaceActive: i18nText[key].tooltipSpaceActive ?? false,
          pipeName,
        };
      });
    }),
    takeUntil(this.#destroy$)
  );

  readonly settlementsPayment$ = this.#criteria.criteria$.pipe(
    this.#filterSearchStream(),
    switchMap(criteria => {
      if (criteria.element) {
        return this.#searchSettlementsPaymentUseCase.execute(criteria).pipe(
          take(1),
          tap(payments => {
            this.#loansCounting$.next(payments.length);
          })
        );
      }

      return this.#settlementsPaymentListUseCase.execute(criteria).pipe(
        take(1),
        tap(payments => {
          this.#loansCounting$.next(payments.totalElements);
        }),
        map(payments => payments.content)
      );
    }),
    map(list => {
      return list.map(item => {
        if (item.status.toLowerCase() === 'cancelado') {
          return {
            ...item,
            status: 'NO COMPLETADO',
          };
        }

        return item;
      });
    }),
    map(items => items.sort((a, b) => b.id - a.id)),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  readonly hasSettlementsPayment$ = this.settlementsPayment$.pipe(
    map(payments => payments.length > 0),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    loansCounting: this.loansCounting$,
    pagesByLoansCounting: this.pagesByLoansCounting$,
    settlementsPayment: this.settlementsPayment$,
    counterLabelTextTemplate: this.counterLabelTextTemplate$,
    todayRawDateDayFirst: this.todayRawDateDayFirst,
    settlementsPaymentStats: this.settlementsPaymentStats$,
    downloadButtonInjectedText: this.downloadButtonInjectedText$,
    searchbarInjectedText: this.searchbarInjectedText$,
    emptySearchInjectedText: this.emptySearchInjectedText$,
    hasSearch: this.hasSearch$,
    hasSettlementsPayment: this.hasSettlementsPayment$,
    orderTypes: this.orderTypes$,
    allBranches: this.allBranches$,
    currentPage: this.currentPage$,
    hasDaterangeIntervalError: this.hasDaterangeIntervalError$,
    currentDateRange: this.currentDateRange$,
    selectedBranches: this.selectedBranches$,
    allLoanLabels: this.allLoanLabels$,
    branchOfficeInjectedText: this.branchOfficeInjectedText$,
    emptyLoansInjectedText: this.emptyLoansInjectedText$,
    settlementsPaymentListInjectedText:
      this.settlementsPaymentListInjectedText$,
    settlementsPaymentStatsInjectedText:
      this.settlementsPaymentStatsInjectedText$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconDownloadTray]);
  }

  download(): void {
    this.#eventManager.sendTrackEvent('buttonClick', {
      buttonName: 'downloadPaymentReport',
    });
    this.#criteria.criteria$
      .pipe(
        take(1),
        switchMap(criteria => {
          return this.#settlementsPaymentReportUseCase
            .execute(criteria)
            .pipe(take(1));
        })
      )
      .subscribe();
  }

  changePage(page: number): void {
    this.#criteria.setPageNum(page);
    this.#eventManager.sendTrackEvent('pagination', { pageNum: page });
  }

  clickEmptyLoansButton(): void {
    const howItWorksLandingUrl = `${this.#environment.redirectionsLandingPage}/para-comercios`;
    this.#redirecter.externalNavigation(howItWorksLandingUrl, '_blank');
  }

  ngOnInit(): void {
    this.#criteria.selectedBranchOffices$.pipe(take(1)).subscribe(branches => {
      this.branchesControl.setValue(branches);
    });

    this.searchControl.valueChanges
      .pipe(
        startWith(this.searchControl.value),
        this.#searchControlFac.debouncedValue(),
        takeUntil(this.#destroy$)
      )
      .subscribe(value => {
        this.#criteria.setSearch(value);
        if (value?.trim()) {
          this.#eventManager.sendTrackEvent('search', { searchTerm: value });
        }
      });

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(value => {
          if (Array.isArray(value) && value.length === 2) {
            const [start, end] = value;

            const startDate = this.#temporal.formatRawDateDayFirst(start);
            const endDate = this.#temporal.formatRawDateDayFirst(end);

            this.#criteria.setDateRange({ startDate, endDate });
            this.#eventManager.sendTrackEvent('dateRange', {
              startDate,
              endDate,
            });
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();

    this.hasDaterangeIntervalError$
      .pipe(takeUntil(this.#destroy$))
      .subscribe(isWithin => {
        this.hasDaterangeIntervalError = isWithin;
      });

    this.#criteria.status$.pipe(take(1)).subscribe(status => {
      const key = Object.entries(ORDER_TYPES).find(
        ([, value]) => value === status
      )[0];

      this.orderTypeControl.setValue(key);
    });

    this.orderTypeControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(statusId => {
        const status = ORDER_TYPES[statusId];
        this.#criteria.setStatus(status);
        this.#eventManager.sendTrackEvent('status', { status });
      });
  }

  ngAfterViewInit(): void {
    this.allBranches$
      .pipe(
        combineLatestWith(this.branchesControl.valueChanges),
        takeUntil(this.#destroy$)
      )
      .subscribe(([all, branches]) => {
        if (!branches) {
          return;
        }

        const isAllSelected =
          branches != null &&
          all.length === branches.length &&
          branches.every(b => all.some(i => b === i.id)) &&
          all.every(b => branches.some(i => b.id === i));

        this.#criteria.setBranchOffices({
          selection: branches ?? [],
          allSelected: isAllSelected,
        });
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #filterSearchStream(): MonoTypeOperatorFunction<SharedCriteriaWithBranchOfficesUIDto> {
    return pipe(
      filter(
        criteria =>
          criteria.element != null &&
          (criteria.element.length === 0 ||
            criteria.element.length >= this.minLength)
      )
    );
  }
}
