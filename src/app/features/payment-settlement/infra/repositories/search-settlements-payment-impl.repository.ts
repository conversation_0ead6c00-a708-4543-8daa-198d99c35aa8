import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Transaction } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesRepositoryDto } from '../../../shared/shared-criteria';
import { SearchSettlementsPaymentRepository } from '../../domain/repositories/search-settlements-payment.repository';

@Injectable({
  providedIn: 'root',
})
export class SearchSettlementsPaymentRepositoryImpl
  implements
    SearchSettlementsPaymentRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<Transaction[]>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  searchByIdPhoneEmail(
    args: SharedCriteriaWithBranchOfficesRepositoryDto
  ): Observable<Transaction[]> {
    return this.#http.get<Transaction[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/payment/filter`,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
          element: args.search ?? '',
        },
      }
    );
  }
}
