import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesRepositoryDto } from '../../../shared/shared-criteria';
import { SettlementsPaymentResponseDto } from '../../domain/dtos/settlements-payment-response.dto';
import { SettlementsPaymentRepository } from '../../domain/repositories/settlements-payment.repository';

@Injectable({
  providedIn: 'root',
})
export class SettlementsPaymentListRepositoryImpl
  implements
    SettlementsPaymentRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<SettlementsPaymentResponseDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  getList(
    args: SharedCriteriaWithBranchOfficesRepositoryDto
  ): Observable<SettlementsPaymentResponseDto> {
    return this.#http.post<SettlementsPaymentResponseDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/payment`,
      args.branchOffices,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
          pageNum: this.pageToString(args.pageNum),
          pageSize: args.pageSize?.toString() ?? '10',
        },
      }
    );
  }

  private pageToString(page: number | undefined): string {
    if (!page) {
      return '0';
    }

    return isNaN(+page) ? '0' : page.toString();
  }
}
