import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesRepositoryDto } from '../../../shared/shared-criteria';
import { SettlementsPaymentReportRepository } from '../../domain/repositories/settlements-payment-report.repository';

@Injectable({
  providedIn: 'root',
})
export class SettlementsPaymentReportRepositoryImpl
  implements
    SettlementsPaymentReportRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<string>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  public getContentForReport(
    args: SharedCriteriaWithBranchOfficesRepositoryDto
  ): Observable<string> {
    return this.#http.post(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/payment/export`,
      args.branchOffices,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
        },
        responseType: 'text' as const,
      }
    );
  }
}
