import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { SharedCriteriaWithBranchOfficesRepositoryDto } from '../../../shared/shared-criteria';
import { SettlementsPaymentStatistics } from '../../domain/entities/settlements-pyament-statistics';
import { SettlementsPaymentStatisticsRepository } from '../../domain/repositories/settlements-payment-statistics.repository';

@Injectable({
  providedIn: 'root',
})
export class SettlementsPaymentStatsRepositoryImpl
  implements
    SettlementsPaymentStatisticsRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<SettlementsPaymentStatistics>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  public getStatistics(
    args: SharedCriteriaWithBranchOfficesRepositoryDto
  ): Observable<SettlementsPaymentStatistics> {
    return this.#http.post<SettlementsPaymentStatistics>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/report/payment/summary-between`,
      args.branchOffices,
      {
        params: {
          startDate: args.dateRange.startDate,
          endDate: args.dateRange.endDate,
          status: args.status,
        },
      }
    );
  }
}
