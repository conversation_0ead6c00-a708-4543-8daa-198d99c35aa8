import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { RefundListCriteriaDto } from '../../domain/entities/refund-list-criteria.dto';
import {
  REFUND_UI_STATUS,
  RefundListUIRequestDto,
} from '../../domain/entities/refund-request.dto';

export const emptyStatusGenericRefundErrorMessage =
  'El estatus de "Devolución" no puede estar vacio';
export const invalidStatusGenericRefundErrorMessage =
  'El estatus de "Devolución" no es válido';
export const emptyDateRangeGenericRefundErrorMessage =
  'El rango de fechas de "Devolución" no es válido.';
export const emptyStartDateGenericRefundErrorMessage =
  'La fecha de inicio de "Devolución" no es válida.';
export const emptyEndDateGenericRefundErrorMessage =
  'La fecha de fin de "Devolución" no es válida.';
export const invalidFormatStartDateGenericRefundErrorMessage =
  'El formato de la fecha de inicio de "Devolución" no es válido.';
export const invalidFormatEndDateGenericRefundErrorMessage =
  'El formato de la fecha de fin de "Devolución" no es válido.';

export class RefundListMapper {
  static fromRequestToCriteria(
    refunds: RefundListUIRequestDto
  ): RefundListCriteriaDto {
    if (!Guard.againstNullOrUndefined(refunds, 'refundsStatus').succeeded) {
      throw new RuntimeMerchantError(
        emptyStatusGenericRefundErrorMessage,
        'RefundListMapper::emptyStatus',
        'list-mapper-from-request-to-criteria'
      );
    }

    const validStatus = REFUND_UI_STATUS[refunds.refundsStatus] || [];

    if (validStatus.length === 0) {
      throw new RuntimeMerchantError(
        invalidStatusGenericRefundErrorMessage,
        'RefundListMapper::invalidStatus',
        'list-mapper-from-request-to-criteria'
      );
    }

    if (!Guard.againstNullOrUndefined(refunds, 'dateRange').succeeded) {
      throw new RuntimeMerchantError(
        emptyDateRangeGenericRefundErrorMessage,
        'RefundListMapper::invalidDateRange',
        'list-mapper-from-request-to-criteria'
      );
    }

    if (
      !Guard.againstNullOrUndefined(refunds.dateRange, 'startDate').succeeded
    ) {
      throw new RuntimeMerchantError(
        emptyStartDateGenericRefundErrorMessage,
        'RefundListMapper::invalidStartDateRange',
        'list-mapper-from-request-to-criteria'
      );
    }

    if (!Guard.againstNullOrUndefined(refunds.dateRange, 'endDate').succeeded) {
      throw new RuntimeMerchantError(
        emptyEndDateGenericRefundErrorMessage,
        'RefundListMapper::invalidEndDateRange',
        'list-mapper-from-request-to-criteria'
      );
    }

    if (
      !Guard.againstInvalidRawDateDayFirst(refunds.dateRange.startDate)
        .succeeded
    ) {
      throw new RuntimeMerchantError(
        invalidFormatStartDateGenericRefundErrorMessage,
        'RefundListMapper::invalidFormatStartDate',
        'list-mapper-from-request-to-criteria'
      );
    }

    if (
      !Guard.againstInvalidRawDateDayFirst(refunds.dateRange.endDate).succeeded
    ) {
      throw new RuntimeMerchantError(
        invalidFormatEndDateGenericRefundErrorMessage,
        'RefundListMapper::invalidFormatEndDate',
        'list-mapper-from-request-to-criteria'
      );
    }

    return {
      status: validStatus.join(','),
      pageNum: refunds.pageNum,
      pageSize: refunds.pageSize,
      startDate: refunds.dateRange.startDate,
      endDate: refunds.dateRange.endDate,
    };
  }
}
