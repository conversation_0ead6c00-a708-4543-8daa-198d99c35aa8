import {
  PartialRefundStats,
  RefundStats,
} from '../../domain/entities/refund-stats';
import { RefundStatsResponseDto } from '../../domain/entities/refund-stats-response.dto';

export class RefundStatsMapper {
  static fromResponseToUI(refunds: RefundStatsResponseDto[]): RefundStats {
    const emptyStats: PartialRefundStats = {
      refundAmount: 0,
      totalRefunds: 0,
    };

    const partialRefunded =
      refunds.find(val => val.status.toLowerCase() === 'refunded') ||
      emptyStats;

    const partialRequested =
      refunds.find(val => val.status.toLowerCase() === 'requested') ||
      emptyStats;

    const stats: RefundStats = {
      refunded: {
        refundAmount: partialRefunded.refundAmount,
        totalRefunds: partialRefunded.totalRefunds,
      },
      requested: {
        refundAmount: partialRequested.refundAmount,
        totalRefunds: partialRequested.totalRefunds,
      },
    };

    return stats;
  }
}
