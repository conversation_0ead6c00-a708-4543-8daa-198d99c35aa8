import {
  B2BDateRange,
  Guard,
  RawDateDayFirst,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import {
  RefundStatusKey,
  VALID_REFUND_STATUS,
} from '../../domain/entities/valid-refund-status';

export const emptyStatusGenericRefundsMapperErrorMessage =
  'El estatus de "Devolución" no puede estar vacio';
export const invalidStatusGenericRefundsMapperErrorMessage =
  'El estatus de "Devolución" no es válido';
export const emptyDateRangeGenericRefundsMapperErrorMessage =
  'El rango de fechas de "Devolución" no es válido.';
export const emptyStartDateGenericRefundsMapperErrorMessage =
  'La fecha de inicio de "Devolución" no es válida.';
export const emptyEndDateGenericRefundsMapperErrorMessage =
  'La fecha de fin de "Devolución" no es válida.';
export const invalidFormatStartDateGenericRefundsMapperErrorMessage =
  'El formato de la fecha de inicio de "Devolución" no es válido.';
export const invalidFormatEndDateGenericRefundsMapperErrorMessage =
  'El formato de la fecha de fin de "Devolución" no es válido.';

export class RefundsMapper {
  static fromUiRequestToRepositoryRequest(args: {
    refundsStatus: RefundStatusKey[];
    dateRange: B2BDateRange;
  }): {
    status: string;
    endDate: RawDateDayFirst;
    startDate: RawDateDayFirst;
  } {
    if (!Guard.againstNullOrUndefined(args, 'refundsStatus').succeeded) {
      throw new RuntimeMerchantError(
        emptyStatusGenericRefundsMapperErrorMessage,
        'RefundsMapper::nullStatus',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    if (args.refundsStatus.length === 0) {
      throw new RuntimeMerchantError(
        emptyStatusGenericRefundsMapperErrorMessage,
        'RefundsMapper::emptyStatus',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    const validStatus = args.refundsStatus
      .filter(refund => !!VALID_REFUND_STATUS[refund])
      .map(refund => VALID_REFUND_STATUS[refund]);

    if (validStatus.length === 0) {
      throw new RuntimeMerchantError(
        invalidStatusGenericRefundsMapperErrorMessage,
        'RefundsMapper::invalidStatus',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(args, 'dateRange').succeeded) {
      throw new RuntimeMerchantError(
        emptyDateRangeGenericRefundsMapperErrorMessage,
        'RefundsMapper::emptyDateRange',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(args.dateRange, 'startDate').succeeded) {
      throw new RuntimeMerchantError(
        emptyStartDateGenericRefundsMapperErrorMessage,
        'RefundsMapper::invalidStartDateRange',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    if (!Guard.againstNullOrUndefined(args.dateRange, 'endDate').succeeded) {
      throw new RuntimeMerchantError(
        emptyEndDateGenericRefundsMapperErrorMessage,
        'RefundsMapper::invalidEndDateRange',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    if (
      !Guard.againstInvalidRawDateDayFirst(args.dateRange.startDate).succeeded
    ) {
      throw new RuntimeMerchantError(
        invalidFormatStartDateGenericRefundsMapperErrorMessage,
        'RefundsMapper::invalidFormatStartDate',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    if (
      !Guard.againstInvalidRawDateDayFirst(args.dateRange.endDate).succeeded
    ) {
      throw new RuntimeMerchantError(
        invalidFormatEndDateGenericRefundsMapperErrorMessage,
        'RefundsMapper::invalidFormatEndDate',
        'refunds-mapper-from-ui-to-repository'
      );
    }

    return {
      status: validStatus.join(','),
      endDate: args.dateRange.endDate,
      startDate: args.dateRange.startDate,
    };
  }
}
