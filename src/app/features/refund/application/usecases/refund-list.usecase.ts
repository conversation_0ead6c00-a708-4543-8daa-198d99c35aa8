import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, of, take } from 'rxjs';
import { RefundListResponseDto } from '../../domain/entities/refund-list-response.dto';
import { RefundListUIRequestDto } from '../../domain/entities/refund-request.dto';
import { RefundListRepository } from '../../domain/repositories/refund-list.repository';
import { RefundListMapper } from '../mappers/refund-list.mapper';

export const emptyListResponse: RefundListResponseDto = {
  first: false,
  last: false,
  number: 0,
  numberOfElements: 0,
  size: 0,
  totalElements: 0,
  totalPages: 0,
  hasContent: false,
  content: [],
};

export const controlledRefundTitleError =
  'Hemos detectado un error en la consulta de devoluciones';

export const uncontrolledRefundTitleError = 'Parece que algo salió mal,';

@Injectable()
export class RefundListUseCase
  implements
    BaseUsecase<RefundListUIRequestDto, Observable<RefundListResponseDto>>
{
  constructor(
    private readonly repository: RefundListRepository,
    private readonly loader: LoaderService,
    private readonly errorHandler: UseCaseErrorHandler
  ) {}

  execute(args: RefundListUIRequestDto): Observable<RefundListResponseDto> {
    const idLoader = this.loader.show();
    try {
      const criteria = RefundListMapper.fromRequestToCriteria(args);

      return this.repository.getList(criteria).pipe(
        take(1),
        catchError(err => this.errorHandler.handle(err, of(emptyListResponse))),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error: unknown) {
      this.loader.hide(idLoader);
      return this.errorHandler.handle(error, of(emptyListResponse));
    }
  }
}
