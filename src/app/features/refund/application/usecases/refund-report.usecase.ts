import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import {
  Observable,
  catchError,
  defer,
  map,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { RefundsReportRequestUIDto } from '../../domain/entities/refund-report-request-ui.dto';
import { RefundReportRepository } from '../../domain/repositories/refund-report.repository';
import { RefundListMapper } from '../mappers/refund-list.mapper';

export const emptyRefundReportDefaultMessage =
  'No existen devoluciones en el rango de fechas solicitado.';
export const uncontrolledRefundReportTitleError = 'Error inesperado';

export const emptyRefundsDefaultResponse: string[][] = [];
export const successRefundReportDefaultMessage =
  'El reporte se ha generado correctamente y la descarga comenzara pronto.';

@Injectable()
export class RefundsReportUseCase
  implements BaseUsecase<RefundsReportRequestUIDto, Observable<string[][]>>
{
  constructor(
    private readonly repository: RefundReportRepository,
    private readonly loader: LoaderService,
    private readonly notifier: NotifierService,
    private readonly fileGenerator: FileGeneratorService<Promise<string[][]>>,
    private readonly csvMapper: CsvMapperService
  ) {}

  execute(args: RefundsReportRequestUIDto): Observable<string[][]> {
    const loaderId = this.loader.show();

    try {
      const criteria = RefundListMapper.fromRequestToCriteria({
        ...args,
        pageNum: 0,
        pageSize: 10,
      });
      const reportName = `reporte_devoluciones_${args.dateRange.startDate}_a_${args.dateRange.endDate}`;

      return this.repository.getRefundsByDaterangeAndStatus(criteria).pipe(
        map(response => this.csvMapper.transform(response)),

        map(content => {
          if (!content || content.length === 0) {
            throw new RuntimeMerchantError(
              emptyRefundReportDefaultMessage,
              'RefundsReportUseCase::emptyReport',
              'RefundsReportUseCase'
            );
          }

          return content;
        }),

        switchMap(content => {
          return defer(() =>
            this.fileGenerator.generateFileAndDownload(content, reportName)
          ).pipe(take(1));
        }),

        tap(() => {
          this.loader.hide(loaderId);
        }),

        tap(() => {
          this.notifier.success({
            title: 'Reporte generado',
            message: successRefundReportDefaultMessage,
          });
        }),

        take(1),

        catchError(err => {
          this.loader.hide(loaderId);
          return this.#handleError(err);
        })
      );
    } catch (error) {
      this.loader.hide(loaderId);
      return this.#handleError(error);
    }
  }

  #handleError(error: any): Observable<string[][]> {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);
    }

    if (
      error instanceof RuntimeMerchantError &&
      error.code === 'RefundsReportUseCase::emptyReport'
    ) {
      this.notifier.warning({
        title: 'Reporte vacío',
        message: error.message,
      });
    } else if (
      error instanceof RuntimeMerchantError &&
      error.code !== 'RefundsReportUseCase::emptyReport'
    ) {
      this.notifier.warning({
        title: error.message,
      });
    } else {
      console.error(error);
      this.notifier.warning({
        title: uncontrolledRefundReportTitleError,
        message:
          (error as any)?.message ||
          'Error con el servicio de descarga de devoluciones para obtener el reporte Excel',
      });
    }

    return of(emptyRefundsDefaultResponse);
  }
}
