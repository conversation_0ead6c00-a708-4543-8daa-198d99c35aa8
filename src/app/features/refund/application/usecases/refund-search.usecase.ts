import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, of, take } from 'rxjs';
import { Refund } from '../../domain/entities/refund';
import { RefundSearchRepository } from '../../domain/repositories/refund-search.repository';

@Injectable()
export class RefundSearchUseCase
  implements BaseUsecase<string, Observable<Refund[]>>
{
  constructor(
    private readonly repository: RefundSearchRepository,
    private readonly loader: LoaderService,
    private readonly notifier: NotifierService
  ) {}

  execute(value: string): Observable<Refund[]> {
    const idLoader = this.loader.show();

    return this.repository.findRefund(value).pipe(
      take(1),
      catchError(error => {
        console.error(error);

        this.notifier.warning({
          title: '¡Ups!',
          message:
            error?.message || 'Algo salió mal con la búsqueda de devolución',
        });

        return of([] as Refund[]);
      }),
      finalize(() => this.loader.hide(idLoader))
    );
  }
}
