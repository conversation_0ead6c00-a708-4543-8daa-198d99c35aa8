import { Injectable } from '@angular/core';
import {
  B2BDateRange,
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import { RefundStats } from '../../domain/entities/refund-stats';
import { RefundStatsRepository } from '../../domain/repositories/refund-stats.repository';
import { RefundStatsMapper } from '../mappers/refund-stats.mapper';
import { RefundsMapper } from '../mappers/refunds.mapper';

export const emptyStatsResponse: RefundStats = {
  refunded: {
    refundAmount: 0,
    totalRefunds: 0,
  },
  requested: {
    refundAmount: 0,
    totalRefunds: 0,
  },
};

export const controlledRefundStatsTitleError =
  'Hemos detectado un error en la consulta de estadísticas de devoluciones';

export const uncontrolledRefundStatsTitleError = 'Parece que algo salió mal,';

@Injectable()
export class RefundStatsUseCase
  implements BaseUsecase<B2BDateRange, Observable<RefundStats>>
{
  constructor(
    private readonly repository: RefundStatsRepository,
    private readonly loader: LoaderService,
    private readonly notifier: NotifierService
  ) {}

  execute(dateRange: B2BDateRange): Observable<RefundStats> {
    const idLoader = this.loader.show();
    try {
      const criteria = RefundsMapper.fromUiRequestToRepositoryRequest({
        refundsStatus: [
          'refunded',
          'requested',
          'failedTransformacion',
          'processing',
        ],
        dateRange,
      });

      return this.repository.getStats(criteria).pipe(
        take(1),
        map(RefundStatsMapper.fromResponseToUI),
        catchError(err => this.#handleError(err)),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error: unknown) {
      this.loader.hide(idLoader);
      return this.#handleError(error);
    }
  }

  #handleError(error: any): Observable<RefundStats> {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);

      this.notifier.warning({
        title: controlledRefundStatsTitleError,
        message: error.message,
      });
    } else {
      console.error(error);
      this.notifier.warning({
        title: uncontrolledRefundStatsTitleError,
        message:
          (error as any)?.message ||
          'Algo salió mal al obtener las estadísticas de devoluciones',
      });
    }

    return of(emptyStatsResponse);
  }
}
