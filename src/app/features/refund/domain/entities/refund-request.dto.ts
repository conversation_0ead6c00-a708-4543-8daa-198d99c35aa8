import { B2BDateRange } from '@aplazo/merchant/shared';

export const REFUND_UI_STATUS = {
  requested: ['REQUESTED', 'TRANSFORMER_FAILED', 'PROCESSING'],
  refunded: ['REFUNDED'],
} as const;

export type RefundUIStatusKey = keyof typeof REFUND_UI_STATUS;

export interface RefundListUIRequestDto {
  refundsStatus: RefundUIStatusKey;
  dateRange: B2BDateRange;
  pageNum: number;
  pageSize: number;
}
