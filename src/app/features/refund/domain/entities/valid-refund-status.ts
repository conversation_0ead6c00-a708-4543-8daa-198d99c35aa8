export const VALID_REFUND_TYPE = {
  total: 'R',
  partial: 'RP',
} as const;

export type RefundTypeKey = keyof typeof VALID_REFUND_TYPE;
export type RefundType = (typeof VALID_REFUND_TYPE)[RefundTypeKey];

export const VALID_REFUND_STATUS = {
  requested: 'REQUESTED',
  rejected: 'REJECTED',
  refunded: 'REFUNDED',
  failedTransformacion: 'TRANSFORMER_FAILED',
  processing: 'PROCESSING',
} as const;

export type RefundStatusKey = keyof typeof VALID_REFUND_STATUS;
export type RefundStatus = (typeof VALID_REFUND_STATUS)[RefundStatusKey];
