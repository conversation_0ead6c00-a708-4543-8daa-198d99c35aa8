import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { RefundListUseCase } from '../../application/usecases/refund-list.usecase';
import { RefundsReportUseCase } from '../../application/usecases/refund-report.usecase';
import { RefundSearchUseCase } from '../../application/usecases/refund-search.usecase';
import { RefundStatsUseCase } from '../../application/usecases/refund-stats.usecase';
import { RefundListRepository } from '../../domain/repositories/refund-list.repository';
import { RefundReportRepository } from '../../domain/repositories/refund-report.repository';
import { RefundSearchRepository } from '../../domain/repositories/refund-search.repository';
import { RefundStatsRepository } from '../../domain/repositories/refund-stats.repository';
import { RefundListRepositoryImpl } from '../repositories/refund-list-impl.repository';
import { RefundsReportRepositoryImpl } from '../repositories/refund-report-impl.repository';
import { RefundSearchRepositoryImpl } from '../repositories/refund-search-impl.repository';
import { RefundStatsRepositoryImpl } from '../repositories/refund-stats-impl.repository';

export function provideRefunds(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: RefundListRepository,
      useClass: RefundListRepositoryImpl,
    },
    {
      provide: RefundReportRepository,
      useClass: RefundsReportRepositoryImpl,
    },
    {
      provide: RefundSearchRepository,
      useClass: RefundSearchRepositoryImpl,
    },
    {
      provide: RefundStatsRepository,
      useClass: RefundStatsRepositoryImpl,
    },
    RefundListUseCase,
    RefundsReportUseCase,
    RefundSearchUseCase,
    RefundStatsUseCase,
  ]);
}
