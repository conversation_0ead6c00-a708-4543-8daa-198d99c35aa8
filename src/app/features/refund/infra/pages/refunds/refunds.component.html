@if (vm$ | async; as context) {
  <section class="px-4 md:px-8 pt-4">
    <h3 class="font-semibold text-center md:text-start mb-2">
      {{ context.refundsTitleText.title }}
    </h3>

    <div
      class="flex flex-col md:flex-row flex-wrap gap-x-3 items-center mt-2 md:-mt-4 pt-4 pb-2">
      <aplz-ui-form-datepicker
        [formControl]="dateControl"
        [rangeEnabled]="true"
        [maxDate]="todayRawDateDayFirst"
        [centerText]="true"
        legend="Seleccione el rango de fechas">
      </aplz-ui-form-datepicker>

      <div class="flex items-center">
        <aplz-ui-select [formControl]="refundStatusControl" label="mostrar">
          @for (opt of context.refundStatus; track opt) {
            <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
          }
        </aplz-ui-select>
      </div>

      @if (context.hasSearch === false && context.hasRefunds === true) {
        <div
          class="mb-8 flex-grow flex-shrink-0 flex justify-center md:justify-end items-center">
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="sm"
            [rounded]="true"
            (click)="download()">
            <span class="mr-1">
              <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
            </span>
            {{ context.downloadButtonInjectedText.label ?? '' }}
          </button>
        </div>
      }
    </div>
  </section>

  <section
    class="-mt-4 pt-8 md:pt-0 px-4 md:px-8 grid gap-4 grid-cols-1 md:grid-cols-2 2xl:grid-cols-4">
    @for (stat of context.refundsStats; track stat) {
      <aplz-ui-metric-card>
        <aplz-ui-metric-card-header>
          <div
            class="aplazo-metric-card__label"
            [class.tooltip--with-space]="
              stat.helpTooltip && stat.tooltipSpaceActive
            ">
            <span class="aplazo-metric-card__label-title text-base">
              {{ stat.statCardTitle }}
            </span>
            @if (stat.helpTooltip) {
              <aplz-ui-icon
                name="question-mark-circle"
                size="xs"
                [aplzTooltip]="stat.helpTooltip ?? ''"></aplz-ui-icon>
            }
          </div>
        </aplz-ui-metric-card-header>
        <aplz-ui-metric-card-content>
          <div class="text-xl font-semibold mb-1">
            {{ stat.value | aplzDynamicPipe: stat.pipeName }}
          </div>
        </aplz-ui-metric-card-content>
      </aplz-ui-metric-card>
    }
  </section>

  <section
    class="px-4 md:px-8 mt-4 md:mt-0 flex flex-col md:flex-row items-center justify-center md:justify-between">
    <h5 class="text-center lg:text-left">
      @if (context.counterLabelTextTemplate; as counterLabelTextTemplate) {
        <span class="whitespace-nowrap font-medium text-lg">
          {{ context.refundsCounting | i18nPlural: counterLabelTextTemplate }}
        </span>
      }
    </h5>

    <div class="md:mt-8 max-w-52">
      <aplz-ui-search
        [textUI]="context.searchbarInjectedText"
        [formControl]="searchControl"
        [minLength]="minLength"></aplz-ui-search>
    </div>
  </section>

  <section class="px-4 md:px-8 -mt-4 pb-16">
    @if (context.hasRefunds === true) {
      <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
        <table aplzSimpleTable aria-label="Refunds List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.refundListInjectedText.loanId }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.refundListInjectedText.saleAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.refundListInjectedText.refundAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.refundListInjectedText.merchantCancelId }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center"
              colspan="1">
              {{ context.refundListInjectedText.cartId }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center"
              colspan="2">
              {{ context.refundListInjectedText.reason }}
            </th>
            <th
              aplzSimpleTableHeaderCell
              scope="col"
              class="text-center"
              colspan="1">
              {{ context.refundListInjectedText.created }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.refundListInjectedText.statusRefund }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center">
              {{ context.refundListInjectedText.refundType }}
            </th>
          </tr>

          @for (item of context.refunds; track item) {
            <tr aplzSimpleTableBodyRow>
              <td
                aplzSimpleTableBodyCell
                class="font-semibold tabular-nums text-center">
                {{ item.loanId }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.saleAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.refundAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.merchantCancelId }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center" colspan="1">
                <p
                  class="truncate max-w-28 mx-auto"
                  [aplzTooltip]="item.cartId">
                  {{ item.cartId }}
                </p>
              </td>
              <td aplzSimpleTableBodyCell class="text-center" colspan="2">
                <p class="min-w-32 font-semibold">
                  {{ item.reason }}
                </p>
              </td>
              <td
                aplzSimpleTableBodyCell
                class="tabular-nums text-center"
                colspan="1">
                {{ item.created | aplzDynamicPipe: 'date' }}
              </td>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.statusRefund }}
              </td>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.requestType === 'R' ? 'Total' : 'Parcial' }}
              </td>
            </tr>
          }
        </table>
      </div>
      <div class="my-3">
        @if (context.hasSearch === false) {
          <aplz-ui-pagination
            [totalPages]="context.pagesByRefundsCounting"
            [currentPage]="context.currentPage"
            (selectedPage)="changePage($event)">
          </aplz-ui-pagination>
        }
      </div>
    } @else {
      <div class="bg-light py-4 rounded-lg shadow-md overflow-x-auto">
        @if (context.hasSearch === false) {
          @if (context.emptyRedundsInjectedText; as emptyMessageUIText) {
            <aplz-ui-common-message
              [i18Text]="{
                title: emptyMessageUIText.title,
                description: emptyMessageUIText.description
              }"
              imgName="emptyLoans">
            </aplz-ui-common-message>
          }
        } @else {
          @if (context.emptySearchInjectedText; as emptyMessageUIText) {
            <aplz-ui-common-message
              [i18Text]="{
                title: emptyMessageUIText.title,
                description: emptyMessageUIText.description
              }"
              imgName="emptySearch">
            </aplz-ui-common-message>
          }
        }
      </div>
    }
  </section>
}
