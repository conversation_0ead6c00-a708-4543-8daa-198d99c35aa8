import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { TemporalService } from '@aplazo/merchant/shared';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { iconDownloadTray } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  MonoTypeOperatorFunction,
  Observable,
  Subject,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  filter,
  map,
  pipe,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { EventManagerService } from '../../../../../services/event-manger.service';
import { RefundListUseCase } from '../../../application/usecases/refund-list.usecase';
import { RefundsReportUseCase } from '../../../application/usecases/refund-report.usecase';
import { RefundSearchUseCase } from '../../../application/usecases/refund-search.usecase';
import { RefundStatsUseCase } from '../../../application/usecases/refund-stats.usecase';
import { Refund } from '../../../domain/entities/refund';
import { RefundUIStatusKey } from '../../../domain/entities/refund-request.dto';
import {
  CriteriaRefundService,
  IRefundCriteriaDto,
} from '../../services/criteria-refund.service';

interface IRefundListInjectedTextUI {
  loanId: string;
  saleAmount: string;
  refundAmount: string;
  merchantCancelId: string;
  cartId: string;
  reason: string;
  created: string;
  statusRefund: string;
  refundType: string;
}

interface IStatCardInjectedTextUI {
  statCardTitle: string;
  helpTooltip?: string;
}

interface IRefundStatsInjectedTextUi {
  requested: {
    refundAmount: IStatCardInjectedTextUI;
    totalRefunds: IStatCardInjectedTextUI;
  };
  refunded: {
    refundAmount: IStatCardInjectedTextUI;
    totalRefunds: IStatCardInjectedTextUI;
  };
}

export const REFUND_STATUS: Record<string, RefundUIStatusKey> = {
  Pendiente: 'requested',
  Devuelto: 'refunded',
} as const;

@Component({
  selector: 'app-refunds',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    I18nPluralPipe,
    AplazoDynamicPipe,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoPaginationComponent,
    AplazoTooltipDirective,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoSearchInputComponent,
    AplazoFormDatepickerComponent,
    AplazoMetricCardComponents,
  ],
  providers: [CriteriaRefundService],
  templateUrl: './refunds.component.html',
})
export class RefundsComponent implements OnInit, OnDestroy {
  readonly #criteria = inject(CriteriaRefundService);
  readonly #i18n = inject(I18NService);
  readonly #eventManager = inject(EventManagerService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #temporal = inject(TemporalService);
  readonly #scope = 'refunds';

  readonly #refundsUseCase = inject(RefundListUseCase);
  readonly #refundStatsUseCase = inject(RefundStatsUseCase);
  readonly #refundSearchUseCase = inject(RefundSearchUseCase);
  readonly #refundsReportUseCase = inject(RefundsReportUseCase);

  readonly #destroy$ = new Subject<void>();

  readonly minLength = 3;

  readonly searchControl = new FormControl<string>('');
  readonly #searchControlFac = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });
  hasSearch$ = this.searchControl.valueChanges.pipe(
    this.#searchControlFac.hasActiveSearch()
  );
  readonly dateControl = new FormControl<Date[] | null>(
    [
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().startDate
      ),
      this.#temporal.fromStringToDate(
        this.#criteria.getDateRangeSync().endDate
      ),
    ],
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  readonly refundListInjectedText$ =
    this.#i18n.getTranslateObjectByKey<IRefundListInjectedTextUI>({
      key: 'list',
      scope: this.#scope,
    });
  readonly refundStatsInjectedText$ =
    this.#i18n.getTranslateObjectByKey<IRefundStatsInjectedTextUi>({
      key: 'stats',
      scope: this.#scope,
    });
  readonly emptyRedundsInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button?: {
      label: string;
    };
  }>({
    key: 'emptyLoans',
    scope: this.#scope,
  });
  readonly searchbarInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: this.#scope,
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  readonly emptySearchInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: this.#scope,
  });
  readonly downloadButtonInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    label: string;
  }>({
    key: 'downloadButton',
    scope: this.#scope,
  });
  readonly refundsTitleText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
  }>({
    key: 'header',
    scope: this.#scope,
  });

  readonly #refundsCounting$ = new BehaviorSubject<number>(0);

  refundsCounting$ = this.#refundsCounting$.pipe(takeUntil(this.#destroy$));
  pagesByRefundsCounting$ = this.#refundsCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  counterLabelTextTemplate$: Observable<Record<string, string>> = this.#i18n
    .getTranslateObjectByKey<{
      empty: string;
      singular: string;
      plural: string;
    }>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      })),
      takeUntil(this.#destroy$)
    );

  todayRawDateDayFirst = this.#temporal.todayRawDayFirst;
  sevenDaysAgoRawDateDayFirst = this.#temporal.sevenDaysAgo;

  readonly #refundTypes$ = new BehaviorSubject<string[]>(
    Object.keys(REFUND_STATUS)
  );

  refundStatus$ = this.#refundTypes$.asObservable();

  refundStatusControl = new FormControl<string>('');

  currentPage$ = this.#criteria.getPageNum$();
  dateRange$ = this.#criteria.getDateRange$();

  readonly #statCardKeys = ['refundAmount', 'totalRefunds'];

  readonly refundsStats$ = this.#criteria.getRefundCriteria$().pipe(
    this.#filterSearchStream(),
    switchMap(({ dateRange }) => this.#refundStatsUseCase.execute(dateRange)),
    combineLatestWith(this.refundStatsInjectedText$.pipe(take(1))),
    map(([stats, i18nText]) => {
      // Iterate over refund status to get the
      // data from usecase response
      return this.#refundTypes$.value
        .map(item => REFUND_STATUS[item])
        .flatMap(status => {
          // iterate over each status
          // to map into each card info
          return this.#statCardKeys.map(key => {
            const pipeName: ValidDynamicPipesNames = ['totalRefunds'].includes(
              key
            )
              ? 'decimal'
              : 'currency';

            return {
              isDarkMode: false,
              statCardKey: key,
              statCardTitle: i18nText[status][key].statCardTitle,
              value: String(stats[status][key]),
              helpTooltip: i18nText[status][key].helpTooltip,
              tooltipSpaceActive:
                i18nText[status][key].tooltipSpaceActive ?? false,
              pipeName,
            };
          });
        });
    }),
    takeUntil(this.#destroy$)
  );

  refunds$: Observable<Refund[]> = this.#criteria.getRefundCriteria$().pipe(
    this.#filterSearchStream(),
    switchMap(({ dateRange, pageNum, pageSize, searchValue, status }) => {
      if (searchValue) {
        return this.#refundSearchUseCase.execute(searchValue).pipe(
          tap(response => {
            this.#refundsCounting$.next(response.length);
          })
        );
      }

      return this.#refundsUseCase
        .execute({
          refundsStatus: status,
          dateRange,
          pageNum,
          pageSize,
        })
        .pipe(
          tap(response => {
            this.#refundsCounting$.next(response.totalElements);
          }),

          map(response => response.content)
        );
    }),
    map(items => [...items].sort((a, b) => b.loanId - a.loanId)),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  hasRefunds$ = this.refunds$.pipe(
    map(refunds => refunds.length > 0),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    refunds: this.refunds$,
    refundsCounting: this.refundsCounting$,
    pagesByRefundsCounting: this.pagesByRefundsCounting$,
    counterLabelTextTemplate: this.counterLabelTextTemplate$,
    todayRawDateDayFirst: this.todayRawDateDayFirst,
    sevenDaysAgoRawDateDayFirst: this.sevenDaysAgoRawDateDayFirst,
    refundsStats: this.refundsStats$,
    refundsTitleText: this.refundsTitleText$,
    refundListInjectedText: this.refundListInjectedText$,
    downloadButtonInjectedText: this.downloadButtonInjectedText$,
    emptyRedundsInjectedText: this.emptyRedundsInjectedText$,
    searchbarInjectedText: this.searchbarInjectedText$,
    emptySearchInjectedText: this.emptySearchInjectedText$,
    hasSearch: this.hasSearch$,
    hasRefunds: this.hasRefunds$,
    refundStatus: this.refundStatus$,
    currentPage: this.currentPage$,
    dateRange: this.dateRange$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconDownloadTray]);
  }

  changePage(page: number): void {
    this.#criteria.setPageNum(page);
    this.#eventManager.sendTrackEvent('pagination', { pageNum: page });
  }

  download(): void {
    this.#criteria
      .getRefundCriteria$()
      .pipe(
        take(1),
        switchMap(criteria => {
          return this.#refundsReportUseCase
            .execute({
              dateRange: criteria.dateRange,
              refundsStatus: criteria.status,
            })
            .pipe(take(1));
        }),
        tap(() => {
          this.#eventManager.sendTrackEvent('buttonClick', {
            buttonName: 'downloadRefundsReport',
          });
        })
      )
      .subscribe();
  }

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        startWith(this.searchControl.value),
        this.#searchControlFac.debouncedValue(),
        takeUntil(this.#destroy$)
      )
      .subscribe(value => {
        this.#criteria.setSearchValue(value);
        if (value?.trim()) {
          this.#eventManager.sendTrackEvent('search', { searchTerm: value });
        }
      });

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),

        tap(value => {
          if (Array.isArray(value) && value.length === 2) {
            const [start, end] = value;

            const startDate = this.#temporal.formatRawDateDayFirst(start);
            const endDate = this.#temporal.formatRawDateDayFirst(end);

            this.#criteria.setDateRange({ startDate, endDate });
            this.#eventManager.sendTrackEvent('dateRange', {
              startDate,
              endDate,
            });
          }
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();

    this.#criteria
      .getStatus$()
      .pipe(take(1))
      .subscribe(status => {
        const key = Object.entries(REFUND_STATUS).find(
          ([, value]) => value === status
        )[0];

        this.refundStatusControl.setValue(key);

        if (this.#refundTypes$.value.length <= 1) {
          this.refundStatusControl.disable();
        } else {
          this.refundStatusControl.enable();
        }
      });

    this.refundStatusControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(statusId => {
        const status = REFUND_STATUS[statusId];
        this.#criteria.setStatus(status);
        this.#eventManager.sendTrackEvent('status', { status });
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
    this.#criteria.clearCriteria();
  }

  #filterSearchStream(): MonoTypeOperatorFunction<IRefundCriteriaDto> {
    return pipe(
      filter(
        criteria =>
          criteria.searchValue != null &&
          (criteria.searchValue.length === 0 ||
            criteria.searchValue.length >= this.minLength)
      )
    );
  }
}
