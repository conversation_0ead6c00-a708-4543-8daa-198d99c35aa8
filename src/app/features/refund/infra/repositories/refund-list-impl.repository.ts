import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { RefundListCriteriaDto } from '../../domain/entities/refund-list-criteria.dto';
import { RefundListResponseDto } from '../../domain/entities/refund-list-response.dto';
import { RefundListRepository } from '../../domain/repositories/refund-list.repository';

@Injectable({
  providedIn: 'root',
})
export class RefundListRepositoryImpl implements RefundListRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);

  getList(args: RefundListCriteriaDto): Observable<RefundListResponseDto> {
    return this.#http.get<RefundListResponseDto>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/refund/page`,
      {
        params: {
          listStatus: args.status,
          pageNum: args.pageNum.toString(),
          pageSize: args.pageSize.toString(),
          startDate: args.startDate,
          endDate: args.endDate,
        },
      }
    );
  }
}
