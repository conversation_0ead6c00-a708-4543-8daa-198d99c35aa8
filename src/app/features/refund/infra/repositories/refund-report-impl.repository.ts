import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IRefundsReportRepositoryParamsDto } from '../../domain/entities/refund-report-repository-params.dto';
import { RefundReportRepository } from '../../domain/repositories/refund-report.repository';

@Injectable({ providedIn: 'root' })
export class RefundsReportRepositoryImpl implements RefundReportRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);

  getRefundsByDaterangeAndStatus(
    args: IRefundsReportRepositoryParamsDto
  ): Observable<string> {
    return this.#http.get(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/refund/export`,
      {
        params: {
          startDate: args.startDate,
          endDate: args.endDate,
          listStatus: args.status,
        },
        responseType: 'text' as const,
      }
    );
  }
}
