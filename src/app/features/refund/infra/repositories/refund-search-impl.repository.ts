import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { Refund } from '../../domain/entities/refund';
import { RefundSearchRepository } from '../../domain/repositories/refund-search.repository';

@Injectable({
  providedIn: 'root',
})
export class RefundSearchRepositoryImpl implements RefundSearchRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);

  findRefund(value: string): Observable<Refund[]> {
    return this.#http.get<Refund[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v1/merchant/refund/filter`,
      {
        params: {
          element: value,
        },
      }
    );
  }
}
