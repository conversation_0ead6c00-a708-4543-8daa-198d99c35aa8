import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../config/merchant-core.environment';
import { IRefundStatsCriteriaDto } from '../../domain/entities/refund-stats-criteria.dto';
import { RefundStatsResponseDto } from '../../domain/entities/refund-stats-response.dto';
import { RefundStatsRepository } from '../../domain/repositories/refund-stats.repository';

@Injectable({
  providedIn: 'root',
})
export class RefundStatsRepositoryImpl implements RefundStatsRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);

  getStats(
    args: IRefundStatsCriteriaDto
  ): Observable<RefundStatsResponseDto[]> {
    return this.#http.get<RefundStatsResponseDto[]>(
      `${this.#environment.apiMicroserviceBaseUrl}api/v2/merchant/refund/summary`,
      {
        params: {
          listStatus: args.status,
          startDate: args.startDate,
          endDate: args.endDate,
        },
      }
    );
  }
}
