import { inject, Injectable } from '@angular/core';
import { B2BDateRange, TemporalService } from '@aplazo/merchant/shared';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { RefundUIStatusKey } from '../../domain/entities/refund-request.dto';
import { RefundStatusKey } from '../../domain/entities/valid-refund-status';

export interface IRefundCriteriaDto {
  pageNum: number;
  pageSize: number;
  searchValue: string;
  dateRange: B2BDateRange;
  status: RefundUIStatusKey;
}

@Injectable({ providedIn: 'root' })
export class CriteriaRefundService {
  readonly #temporal = inject(TemporalService);
  readonly #initialCriteriaRefunds: IRefundCriteriaDto = {
    pageNum: 0,
    pageSize: 10,
    searchValue: '',
    dateRange: {
      startDate: this.#temporal.sevenDaysAgo,
      endDate: this.#temporal.todayRawDayFirst,
    },
    status: 'refunded',
  } as const;

  readonly #refundCriteria$ = new BehaviorSubject<IRefundCriteriaDto>(
    this.#initialCriteriaRefunds
  );

  readonly initialRefundCriteria = Object.seal(
    Object.freeze(this.#initialCriteriaRefunds)
  );

  getRefundCriteria$(): Observable<IRefundCriteriaDto> {
    return this.#refundCriteria$.asObservable();
  }

  getPageNum$(): Observable<number> {
    return this.#refundCriteria$.pipe(map(criteria => criteria.pageNum));
  }

  getPageSize$(): Observable<number> {
    return this.#refundCriteria$.pipe(map(criteria => criteria.pageSize));
  }

  getSearchValue$(): Observable<string> {
    return this.#refundCriteria$.pipe(map(criteria => criteria.searchValue));
  }

  getDateRange$(): Observable<B2BDateRange> {
    return this.#refundCriteria$.pipe(map(criteria => criteria.dateRange));
  }

  getDateRangeSync(): B2BDateRange {
    return this.#refundCriteria$.getValue().dateRange;
  }

  getStatus$(): Observable<RefundStatusKey> {
    return this.#refundCriteria$.pipe(map(criteria => criteria.status));
  }

  setPageNum(pageNum: number): void {
    if (this.#refundCriteria$.value.pageNum == pageNum) {
      return;
    }

    this.#refundCriteria$.next({
      ...this.#refundCriteria$.value,
      pageNum,
    });
  }

  setPageSize(pageSize: number): void {
    if (this.#refundCriteria$.value.pageSize == pageSize) {
      return;
    }

    this.#refundCriteria$.next({
      ...this.#refundCriteria$.value,
      pageSize,
      pageNum: 0,
      searchValue: '',
    });
  }

  setSearchValue(searchValue: string): void {
    if (this.#refundCriteria$.value.searchValue === searchValue) {
      return;
    }

    this.#refundCriteria$.next({
      ...this.#refundCriteria$.value,
      pageNum: 0,
      pageSize: 10,
      searchValue,
    });
  }

  setDateRange(dateRange: B2BDateRange): void {
    if (
      this.#refundCriteria$.value.dateRange.startDate === dateRange.startDate &&
      this.#refundCriteria$.value.dateRange.endDate === dateRange.endDate
    ) {
      return;
    }

    this.#refundCriteria$.next({
      ...this.#refundCriteria$.value,
      pageNum: 0,
      pageSize: 10,
      searchValue: '',
      dateRange,
    });
  }

  setStatus(status: RefundUIStatusKey): void {
    if (this.#refundCriteria$.value.status === status) {
      return;
    }

    this.#refundCriteria$.next({
      ...this.#refundCriteria$.value,
      pageNum: 0,
      pageSize: 10,
      searchValue: '',
      status,
    });
  }

  clearCriteria(): void {
    this.#refundCriteria$.next(this.#initialCriteriaRefunds);
  }
}
