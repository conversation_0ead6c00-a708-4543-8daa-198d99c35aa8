import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  AplazoFormFieldDirectives,
  NoopValueAccesorDirective,
  ngControlInjector,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSearch, iconXMark } from '@aplazo/ui-icons';

@Component({
  selector: 'app-ui-search',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AplazoFormFieldDirectives,
    AplazoIconComponent,
    ReactiveFormsModule,
  ],
  hostDirectives: [NoopValueAccesorDirective],
  template: `
    <aplz-ui-form-field [borderless]="true">
      <input
        type="text"
        aplzFormInput
        [formControl]="searchControl"
        [placeholder]="searchbarUIText?.placeholder ?? ''" />
      <button aplzButton aplzInputPrefix>
        <aplz-ui-icon name="search" size="sm"></aplz-ui-icon>
      </button>

      @if (searchControl?.value) {
        <button aplzButton aplzInputSuffix (click)="resetSearch()">
          <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
        </button>
      }

      <ng-container aplzFormError>
        @if (hasSearchMinLengthError) {
          <p>
            {{
              searchbarUIText?.minLengthError ??
                'Para una búsqueda ingrese mínimo ' + minLength + ' caracteres'
            }}
          </p>
        }
      </ng-container>
    </aplz-ui-form-field>
  `,
})
export class SearchComponent {
  readonly #ngControl = ngControlInjector();
  readonly #iconRegister = inject(AplazoIconRegistryService);

  @Input()
  searchbarUIText: { placeholder: string; minLengthError: string } | undefined;

  /**
   * Minimum length of search
   * @type {number}
   * @default 3
   */
  @Input()
  minLength = 3;

  get searchControl() {
    return this.#ngControl?.control as FormControl<string>;
  }

  get hasSearchMinLengthError(): boolean {
    return (
      (this.searchControl &&
        (this.searchControl.touched || this.searchControl.dirty) &&
        this.searchControl.hasError('minlength')) ??
      false
    );
  }

  constructor() {
    this.#iconRegister.registerIcons([iconSearch, iconXMark]);
  }

  resetSearch() {
    if (this.searchControl?.value) {
      this.searchControl.reset();
    }
  }
}
