import {
  Directive,
  inject,
  Input,
  OnInit,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { UserStoreService } from '../../user/src/application/services/user-store.service';
import { ValidMerchantRoles } from '../../user/src/domain/entities/valid-roles';

@Directive({
  standalone: true,
  selector: '[aplazoIfValidRole]',
})
export class IfValidRoleDirective implements OnInit {
  readonly #viewContainerRef = inject(ViewContainerRef);
  readonly #templateRef = inject(TemplateRef);
  readonly #store = inject(UserStoreService);

  #roles: ValidMerchantRoles[] = [];
  @Input('aplazoIfValidRole')
  set roles(roles: ValidMerchantRoles[] | null) {
    this.#roles = roles ?? [];
  }

  ngOnInit() {
    const currentRole = this.#store.getRole() as ValidMerchantRoles;

    this.#viewContainerRef.clear();

    if (
      this.#roles &&
      Array.isArray(this.#roles) &&
      this.#roles.length > 0 &&
      this.#roles.includes(currentRole)
    ) {
      this.#viewContainerRef.createEmbeddedView(this.#templateRef, {
        $implicit: this.#roles,
      });
    }
  }
}
