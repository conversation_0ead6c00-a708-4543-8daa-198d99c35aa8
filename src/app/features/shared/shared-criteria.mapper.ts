import {
  Guard,
  RawLoanStatus,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from './shared-criteria';

export class SharedCriteriaMapper {
  static fromUIToRepository(
    args: SharedCriteriaWithBranchOfficesUIDto
  ): SharedCriteriaWithBranchOfficesRepositoryDto {
    if (
      !Guard.againstNullOrUndefined(args, 'status', 'shared-criteria-mapper')
        .succeeded
    ) {
      throw new RuntimeMerchantError(
        'No se pudo realizar la consulta debido a que no se recibió un status válido',
        'SharedCriteriaMapper::invalidStatus',
        'shared-criteria-mapper'
      );
    }

    if (
      !Guard.againstNullOrUndefined(args, 'dateRange', 'shared-criteria-mapper')
        .succeeded
    ) {
      throw new RuntimeMerchantError(
        'No se pudo realizar la consulta debido a que no se recibió un rango de fechas válido',
        'SharedCriteriaMapper::invalidDateRange',
        'shared-criteria-mapper'
      );
    }

    if (
      !Guard.againstInvalidRawDateDayFirst(args.dateRange.startDate).succeeded
    ) {
      throw new RuntimeMerchantError(
        'No se pudo realizar la consulta debido a que no se recibió una fecha inicial válida',
        'PaymentBalanceListUsecase::invalidDate',
        'get-balance-list-error'
      );
    }

    if (
      !Guard.againstInvalidRawDateDayFirst(args.dateRange.endDate).succeeded
    ) {
      throw new RuntimeMerchantError(
        'No se pudo realizar la consulta debido a que no se recibió una fecha final válida',
        'PaymentBalanceListUsecase::invalidDate',
        'get-balance-list-error'
      );
    }

    const mappedStatus = RawLoanStatus.create(args.status);

    const mappedBranchOffices = args.isAllBranchesSelected
      ? []
      : args.branchOffices;

    const mappedDays = args.days
      ? Array.from(new Set(args.days))
          .sort((a, b) => a - b)
          .join(',')
      : undefined;

    return {
      status: mappedStatus.status,
      dateRange: args.dateRange,
      branchOffices: mappedBranchOffices,
      days: mappedDays,
      pageNum: args.pageNum,
      pageSize: args.pageSize,
      search: args.element,
    };
  }
}
