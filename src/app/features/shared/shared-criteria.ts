import {
  B2B<PERSON>ateRang<PERSON>,
  <PERSON><PERSON>y<PERSON><PERSON>ber,
  StatusGroupingId,
} from '@aplazo/merchant/shared';

export interface SharedCriteriaWithBranchOfficesUIDto {
  status: StatusGroupingId;
  dateRange: B2BDateRange;
  branchOffices: number[];
  pageNum: number;
  pageSize: number;
  element: string;
  isAllBranchesSelected?: boolean;
  days?: DayByNumber[];
}

export interface SharedCriteriaWithBranchOfficesRepositoryDto {
  status: string;
  dateRange: B2BDateRange;
  branchOffices?: number[];
  pageNum?: number;
  pageSize?: number;
  search?: string;
  days?: string;
}
