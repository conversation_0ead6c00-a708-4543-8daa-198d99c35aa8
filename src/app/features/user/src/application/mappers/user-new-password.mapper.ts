import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { UserEmail } from '../../domain/entities/user-email';
import { UserPassword } from '../../domain/entities/user-password';
import { IUserRepositorySetNewPasswordDto } from '../dtos/repository-set-new-password.dto';
import { IUserSetNewPasswordDto } from '../dtos/set-new-password.dto';

export const emptyTokenNewPasswordDefaultError =
  'El token no puede estar vacio y no es válido';
export const emptyPasswordNewPasswordDefaultError =
  'El password no puede estar vacio y no es válido';

export class UserForgotPasswordMapper {
  static toNewRolesRepository(args: IUserSetNewPasswordDto): {
    code: string;
    password: string;
  } {
    if (!Guard.againstNullOrUndefined(args, 'token').succeeded) {
      throw new RuntimeMerchantError(
        emptyTokenNewPasswordDefaultError,
        'UserForgotPasswordMapper::emptyToken'
      );
    }
    if (!Guard.againstNullOrUndefined(args, 'password').succeeded) {
      throw new RuntimeMerchantError(
        emptyPasswordNewPasswordDefaultError,
        'UserForgotPasswordMapper::emptyPassword'
      );
    }

    const validatedPassword = UserPassword.create(args.password);

    return {
      code: args.token,
      password: validatedPassword.value,
    };
  }

  static toRepositorySetNewPasswordDto(
    args: IUserSetNewPasswordDto
  ): IUserRepositorySetNewPasswordDto {
    const dto: IUserRepositorySetNewPasswordDto = {
      token: args.token,
      password: UserPassword.create(args.password),
    };

    return dto;
  }

  static toSetPasswordFormData(
    args: IUserRepositorySetNewPasswordDto
  ): FormData {
    const formData = new FormData();

    formData.append('token', args.token);
    formData.append('password', args.password.value);

    return formData;
  }

  static toSendEmailFormData(args: UserEmail): FormData {
    const formData = new FormData();

    formData.append('email', args.value);

    return formData;
  }
}
