import { IUserJwt } from '@aplazo/merchant/shared';
import { User } from '../../domain/entities/user';
import { UserEmail } from '../../domain/entities/user-email';
import { IUserPersistenceDTO } from '../dtos/user-persistence.dto';

export class UserMapper {
  static loginToDomain(
    username: string,
    user: IUserJwt,
    email?: UserEmail
  ): User {
    const newUser = User.create({
      username: username,
      merchantId: user.merchantId,
      merchantName: user.companyName ?? user.name ?? user.username ?? '',
      role: user.role,
    });

    if (email) {
      newUser.setEmail(email);
    }

    return newUser;
  }

  static persistanceToDomain(user: IUserPersistenceDTO): User {
    const newUser = User.create({
      username: user.username,
      merchantId: user.merchantId,
      merchantName: user.merchantName ?? '',
      role: user.role,
    });

    if (user.role === 'ROLE_MERCHANT' && user.email) {
      newUser.setEmail(UserEmail.create(user.email));
    }

    newUser.setAccessToken(user.accessToken);

    return newUser;
  }

  static domainToPersistence(user: User): IUserPersistenceDTO {
    return {
      username: user.username,
      email: user.email,
      merchantId: user.merchantId,
      merchantName: user.merchantName,
      role: user.role,
      accessToken: user.accessToken,
      lastLogin: user.lastLogin.toISOString(),
      isLoggedIn: user.isLoggedIn,
    };
  }
}
