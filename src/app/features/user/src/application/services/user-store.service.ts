import { Observable } from 'rxjs';
import { IUserPersistenceDTO } from '../dtos/user-persistence.dto';

export abstract class UserStoreService {
  abstract user$: Observable<IUserPersistenceDTO>;
  abstract userEmail$: Observable<string>;
  abstract username$: Observable<string>;
  abstract merchantId$: Observable<number>;
  abstract merchantName$: Observable<string>;
  abstract merchant$: Observable<{
    name: string;
    id: string;
    email: string;
  }>;
  abstract role$: Observable<string>;
  abstract isLoggedIn$: Observable<boolean>;
  abstract tokenSession$: Observable<string>;
  abstract setUser(user: IUserPersistenceDTO): void;
  abstract setFailure(error: unknown): void;
  abstract clearUserStore(): void;
  abstract getRole(): string;
}
