import {
  IUserJwt,
  JwtDecoderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import {
  EMPTY,
  MonoTypeOperatorFunction,
  OperatorFunction,
  catchError,
  defer,
  exhaustMap,
  map,
  of,
  pipe,
  take,
  zip,
} from 'rxjs';
import { User } from '../../domain/entities/user';
import { UserEmail } from '../../domain/entities/user-email';
import { ValidMerchantRoles } from '../../domain/entities/valid-roles';
import { UserMapper } from '../mappers/user.mapper';
import { DashUserPersistenceService } from '../services/local-persistence.service';
import { UserStoreService } from '../services/user-store.service';

export abstract class BaseLoginUseCase {
  constructor(
    protected readonly jwtDecoder: JwtDecoderService<IUserJwt>,
    protected readonly persistenceService: DashUserPersistenceService,
    protected readonly notifier: NotifierService,
    protected readonly store: UserStoreService
  ) {}

  protected fromTokenToUser(
    rolesToValidate: ValidMerchantRoles[],
    username: string,
    email?: UserEmail
  ): OperatorFunction<string, User> {
    return pipe(
      this.decodeToken(),
      this.validateRoles(rolesToValidate),
      this.mapFromServiceToDomain(username, email),
      this.persistUser()
    );
  }

  protected handleAsyncError(): MonoTypeOperatorFunction<User> {
    return pipe(
      catchError(error => {
        this.store.setFailure({ error });

        if (error instanceof RuntimeMerchantError) {
          console.warn(error.code);
          this.notifier.warning({
            title: '¡Ups! Parece que ha ocurrido un error',
            message: error.message,
          });
        } else {
          console.error(error);
          this.notifier.warning({
            title: '¡Ups! Parece que la información no es correcta.',
            message: 'Verifique su correo / contraseña',
          });
        }

        return EMPTY;
      }),
      take(1)
    );
  }

  private decodeToken(): OperatorFunction<string, [IUserJwt, string]> {
    return pipe(
      exhaustMap(jwtToken => {
        if (!jwtToken) {
          throw new RuntimeMerchantError(
            'Verifique su usuario / contraseña',
            'Login::emptyToken',
            'UserLogin'
          );
        }

        return zip([
          defer(() => this.jwtDecoder.decodeToken(jwtToken)),
          of(jwtToken),
        ]);
      })
    );
  }

  private validateRoles(
    rolesToValidate: ValidMerchantRoles[]
  ): MonoTypeOperatorFunction<[IUserJwt, string]> {
    return pipe(
      map(([userJwt, token]) => {
        if (!rolesToValidate.includes(userJwt.role as ValidMerchantRoles)) {
          throw new RuntimeMerchantError(
            'El usuario no tiene privilegios para iniciar sesión',
            'Login::roles',
            'UserLogin'
          );
        }
        return [userJwt, token];
      })
    );
  }

  private mapFromServiceToDomain(
    username: string,
    email?: UserEmail
  ): OperatorFunction<[IUserJwt, string], User> {
    return pipe(
      map(([userJwt, jwtToken]) => {
        const newUser = UserMapper.loginToDomain(username, userJwt, email);
        newUser.setAccessToken(jwtToken);
        return newUser;
      })
    );
  }

  private persistUser(): MonoTypeOperatorFunction<User> {
    return pipe(
      exhaustMap(user =>
        of(this.store.setUser(UserMapper.domainToPersistence(user))).pipe(
          map(() => user)
        )
      ),
      exhaustMap(user =>
        defer(() =>
          this.persistenceService.saveAuthenticatedUser(
            UserMapper.domainToPersistence(user)
          )
        ).pipe(
          take(1),
          map(() => user),
          catchError(() => {
            throw new RuntimeMerchantError(
              'No se pudo guardar la información del usuario',
              'Login::persistence',
              'UserLogin'
            );
          })
        )
      )
    );
  }
}
