import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  IUserJwt,
  JwtDecoderService,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { EMPTY, Observable, finalize, take } from 'rxjs';
import { Credentials } from '../../domain/entities/credentials';
import { User } from '../../domain/entities/user';
import { UserEmail } from '../../domain/entities/user-email';
import { ValidMerchantRoles } from '../../domain/entities/valid-roles';
import { UserRepository } from '../../domain/repositories/user.repository';
import { DashUserPersistenceService } from '../services/local-persistence.service';
import { UserStoreService } from '../services/user-store.service';
import { BaseLoginUseCase } from './base-login-usecase';

@Injectable({ providedIn: 'root' })
export class DashUserLoginUseCase
  extends BaseLoginUseCase
  implements BaseUsecase<Credentials, Observable<User>>
{
  readonly #rolesWithEmail: ValidMerchantRoles[] = [
    'ROLE_MERCHANT',
    'ROLE_ADMIN_INC',
    'ROLE_PANEL_MANAGER',
  ];
  readonly #rolesWithoutEmail: ValidMerchantRoles[] = [
    'ROLE_MERCHANT',
    'ROLE_PANEL_ADMIN',
    'ROLE_PANEL_FINANCE',
    'ROLE_PANEL_MARKETING',
    'ROLE_PANEL_SUPPORT',
    'ROLE_ADMIN_INC',
    'ROLE_PANEL_MANAGER',
  ];

  constructor(
    private readonly repository: UserRepository<Observable<string>>,
    private readonly loader: LoaderService,
    notifier: NotifierService,
    persistenceService: DashUserPersistenceService,
    jwtDecoder: JwtDecoderService<IUserJwt>,
    store: UserStoreService
  ) {
    super(jwtDecoder, persistenceService, notifier, store);
  }

  execute(credentials: Credentials): Observable<User> {
    const loaderId = this.loader.show();
    const sanitizedUsername = credentials.username.trim().replaceAll(' ', '');

    try {
      const email = UserEmail.create(sanitizedUsername);
      return this.repository
        .loginWithEmail({
          password: credentials.password,
          username: email.value,
        })
        .pipe(
          this.fromTokenToUser(this.#rolesWithEmail, sanitizedUsername, email),
          this.handleAsyncError(),
          take(1),
          finalize(() => this.loader.hide(loaderId))
        );
    } catch (error: unknown) {
      if (!(error instanceof RuntimeMerchantError)) {
        console.error(error);
        this.loader.hide(loaderId);

        return EMPTY;
      }

      if (error.code === 'UserEmail::entityCreation') {
        console.warn(error.code);
        return this.repository
          .loginWithoutEmail({
            password: credentials.password,
            username: sanitizedUsername,
          })
          .pipe(
            this.fromTokenToUser(this.#rolesWithoutEmail, sanitizedUsername),
            this.handleAsyncError(),
            take(1),
            finalize(() => this.loader.hide(loaderId))
          );
      }
      console.warn(error.code);
      return EMPTY;
    }
  }
}
