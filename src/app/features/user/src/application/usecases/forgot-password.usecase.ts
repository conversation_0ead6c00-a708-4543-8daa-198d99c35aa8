import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, catchError, forkJoin, map, of, take } from 'rxjs';
import { UserEmail } from '../../domain/entities/user-email';
import { UserForgotPasswordResetRepository } from '../../domain/repositories/user-forgot-password-v2.repository';
import { UserForgotPasswordRepository } from '../../domain/repositories/user-forgot-password.repository';
import { UserForgotPasswordNewRolesDto } from '../dtos/forgot-password-v2.dto';
import { IUserForgotPasswordDto } from '../dtos/forgot-password.dto';
import { UserForgotPasswordMapper } from '../mappers/user-new-password.mapper';

@Injectable({ providedIn: 'root' })
export class ForgotPasswordUseCase
  implements BaseUsecase<IUserForgotPasswordDto, Observable<void>>
{
  constructor(
    private readonly repository: UserForgotPasswordRepository<
      FormData,
      Observable<void>
    >,
    private readonly repositoryV2: UserForgotPasswordResetRepository<
      string,
      Observable<UserForgotPasswordNewRolesDto>
    >,
    private readonly loader: LoaderService,
    private readonly notifier: NotifierService,
    private readonly usecaseErrorHandler: UseCaseErrorHandler
  ) {}

  execute(args: IUserForgotPasswordDto): Observable<void> {
    const idLoader = this.loader.show();

    try {
      const email = UserEmail.create(args.email);

      return forkJoin([
        this.repository
          .requestReset(UserForgotPasswordMapper.toSendEmailFormData(email))
          .pipe(
            catchError(() => of(undefined)),
            take(1)
          ),
        this.repositoryV2.reset(email.value).pipe(take(1)),
      ]).pipe(
        map(() => {
          this.loader.hide(idLoader);
          this.#successMessage();
          return undefined;
        }),

        catchError(e => {
          console.warn(e);
          this.loader.hide(idLoader);
          this.#successMessage();

          return of(undefined);
        }),
        take(1)
      );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.usecaseErrorHandler.handle(error, of(undefined));
    }
  }

  #successMessage(): void {
    this.notifier.success({
      title: 'El enlace ha sido enviado',
      message:
        'Verifique su buzón de correo electrónico, debería recibir un enlace de reinicio que será válido 5 minutos, en caso contrario, solicite nuevamente la recuperación de contraseña.',
    });
  }
}
