import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, of, take, tap } from 'rxjs';
import { UserForgottenPasswordVerificationTokenRepository } from '../../domain/repositories/user-forgotten-password-verification-token.repository';
import { IUserForgottenPasswordVerificationTokenDto } from '../dtos/verification-token.dto';

@Injectable({ providedIn: 'root' })
export class UserForgottenPasswordVerificationTokenUseCase
  implements
    BaseUsecase<
      IUserForgottenPasswordVerificationTokenDto,
      Observable<{ status: boolean }>
    >
{
  constructor(
    private repository: UserForgottenPasswordVerificationTokenRepository<
      IUserForgottenPasswordVerificationTokenDto,
      Observable<{ status: boolean }>
    >,
    private loaderService: LoaderService,
    private notifierService: NotifierService
  ) {}

  execute(
    args: IUserForgottenPasswordVerificationTokenDto
  ): Observable<{ status: boolean }> {
    const idLoader = this.loaderService.show();

    if (!args.token) {
      this.notifierService.warning({
        title: 'Enlace no válido',
      });
      return of({ status: false }).pipe(
        take(1),
        finalize(() => this.loaderService.hide(idLoader))
      );
    }

    return this.repository.validateToken({ token: args.token }).pipe(
      tap(({ status }) => {
        if (status) {
          this.notifierService.success({
            title: 'Correo verificado',
          });
          return;
        }

        this.notifierService.warning({
          title: 'Enlace no válido',
        });
      }),
      catchError(error => {
        console.error(error);
        this.notifierService.warning({
          title: 'Ups',
          message:
            error.message || 'Algo salió mal al intentar validar el token.',
        });
        return of({ status: false });
      }),
      take(1),
      finalize(() => this.loaderService.hide(idLoader))
    );
  }
}
