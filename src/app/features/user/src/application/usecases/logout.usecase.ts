import { Injectable } from '@angular/core';
import { BaseUsecase, RedirectionService } from '@aplazo/merchant/shared';
import { defer, Observable, of, switchMap, take, tap } from 'rxjs';
import { DashUserPersistenceService } from '../services/local-persistence.service';
import { UserStoreService } from '../services/user-store.service';

@Injectable({ providedIn: 'root' })
export class UserLogoutUseCase
  implements BaseUsecase<string | string[], Observable<void>>
{
  constructor(
    private persistenceService: DashUserPersistenceService,
    private storeService: UserStoreService,
    private redirectionService: RedirectionService
  ) {}

  execute(urlToRedirect: string | string[]): Observable<void> {
    return defer(() => this.persistenceService.deAuthenticateUser()).pipe(
      switchMap(() => of(this.storeService.clearUserStore()).pipe(take(1))),
      tap(() => {
        this.redirectionService.internalNavigation(urlToRedirect);
      }),
      take(1)
    );
  }
}
