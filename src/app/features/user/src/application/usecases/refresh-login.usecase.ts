import { inject, Injectable } from '@angular/core';
import { BaseUsecase, RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  catchError,
  defer,
  map,
  Observable,
  of,
  switchMap,
  withLatestFrom,
} from 'rxjs';
import { User } from '../../domain/entities/user';
import { UserMapper } from '../mappers/user.mapper';
import { DashUserPersistenceService } from '../services/local-persistence.service';
import { UserStoreService } from '../services/user-store.service';

@Injectable({ providedIn: 'root' })
export class UserRefreshLoginUseCase
  implements BaseUsecase<any, Observable<User | null>>
{
  readonly #persistenceService = inject(DashUserPersistenceService);
  readonly #storeService = inject(UserStoreService);

  execute(): Observable<User | null> {
    return defer(() => this.#persistenceService.getAuthenticatedUser()).pipe(
      withLatestFrom(this.#storeService.isLoggedIn$),

      switchMap(([userOrNull, hasStoreLoggedIn]) => {
        if (userOrNull?.isLoggedIn && !hasStoreLoggedIn) {
          this.#storeService.setUser(userOrNull);
        }

        return of(userOrNull);
      }),

      map(userOrNull => {
        if (!userOrNull) {
          return null;
        }

        return UserMapper.persistanceToDomain(userOrNull);
      }),

      catchError(error => {
        this.#storeService.setFailure({ error });
        if (error instanceof RuntimeMerchantError) {
          console.warn(error);
        }
        return of(null);
      })
    );
  }
}
