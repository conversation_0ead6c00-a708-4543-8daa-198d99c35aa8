import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import {
  EMPTY,
  Observable,
  OperatorFunction,
  catchError,
  map,
  pipe,
  take,
  tap,
} from 'rxjs';
import {
  UserNewRolesSetNewPasswordRepository,
  UserSetNewPasswordRepository,
} from '../../domain/repositories/user-set-new-password.repository';
import { NewRoleSetPasswordDto } from '../dtos/new-role-set-password.dto';
import { IUserSetNewPasswordDto } from '../dtos/set-new-password.dto';
import { UserForgotPasswordMapper } from '../mappers/user-new-password.mapper';

export const setNewPasswordDefaultControlledErrorTitle =
  '¡Hemos detectado un error!';
export const setNewPasswordDefaultUncontrolledErrorTitle =
  '¡Parece que algo salió mal!';
export const expiredCodeErrorMessage =
  'La contraseña ya fue verificada o el link no es válido';

@Injectable({ providedIn: 'root' })
export class UserSetNewPasswordUseCase
  implements BaseUsecase<IUserSetNewPasswordDto, Observable<void>>
{
  constructor(
    private readonly setPasswordRepository: UserSetNewPasswordRepository<
      FormData,
      Observable<void>
    >,
    private readonly setPasswordNewRolesRepository: UserNewRolesSetNewPasswordRepository<
      { code: string; password: string },
      Observable<NewRoleSetPasswordDto>
    >,
    private readonly loader: LoaderService,
    private readonly notifier: NotifierService
  ) {}

  execute(args: IUserSetNewPasswordDto): Observable<void> {
    const idLoader = this.loader.show();

    try {
      if (args.newRoles) {
        const dto = UserForgotPasswordMapper.toNewRolesRepository(args);

        return this.setPasswordNewRolesRepository
          .setPassword(dto)
          .pipe(
            this.#showNotificationByResponse<NewRoleSetPasswordDto>(idLoader)
          );
      }

      const repositoryDto =
        UserForgotPasswordMapper.toRepositorySetNewPasswordDto(args);

      const formData =
        UserForgotPasswordMapper.toSetPasswordFormData(repositoryDto);

      return this.setPasswordRepository
        .setPassword(formData)
        .pipe(this.#showNotificationByResponse<void>(idLoader));
    } catch (error) {
      this.loader.hide(idLoader);

      this.#handleError(error);

      throw error;
    }
  }

  #showNotificationByResponse<T>(idLoader: string): OperatorFunction<T, void> {
    return pipe(
      tap(() => {
        this.notifier.success({
          title: 'Contraseña guardada correctamente',
        });
      }),

      map(() => undefined),

      tap(() => {
        this.loader.hide(idLoader);
      }),

      catchError(error => {
        this.loader.hide(idLoader);

        const errorCode = error.status ?? 500;

        if (errorCode === 400) {
          this.#handleError(
            new RuntimeMerchantError(
              expiredCodeErrorMessage,
              'SetNewPassword::expiredToken',
              'set-new-password-usecase'
            )
          );
        } else {
          this.#handleError(error);
        }

        return EMPTY;
      }),

      take(1)
    );
  }

  #handleError(error: unknown): void {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);

      this.notifier.warning({
        title: setNewPasswordDefaultControlledErrorTitle,
        message: error.message,
      });
    } else {
      console.error(error);

      this.notifier.warning({
        title: setNewPasswordDefaultUncontrolledErrorTitle,
        message:
          typeof error !== 'string'
            ? (error as any)?.error?.message ??
              (error as any)?.error?.error ??
              (error as any)?.message ??
              'Algo salió mal al procesar la solicitud de cambio de contraseña.'
            : error,
      });
    }
  }
}
