import { EMAIL_REGEXP, RuntimeMerchantError } from '@aplazo/merchant/shared';

export const invalidEmailErrorMessage =
  'Ingrese correo electrónico válido, Ej: <EMAIL>';

export class UserEmail {
  #email: string;

  private constructor(email: string) {
    this.#email = email;
  }

  get value(): string {
    return this.#email;
  }

  static #isValidEmail(email: string): boolean {
    return email.match(EMAIL_REGEXP) !== null;
  }

  static #format(email: string): string {
    return email.trim().toLowerCase();
  }

  static create(email: string): UserEmail {
    if (!this.#isValidEmail(email)) {
      throw new RuntimeMerchantError(
        invalidEmailErrorMessage,
        'UserEmail::entityCreation',
        'UserEmail'
      );
    }

    return new UserEmail(this.#format(email));
  }
}
