import {
  AT_LEAST_ONE_LOWERCASE_LETTER_REGEXP,
  AT_LEAST_ONE_NUMBER_REGEXP,
  AT_LEAST_ONE_UPPERCASE_LETTER_REGEXP,
  AT_LEAST_ONE_VALID_CHARACTER_NOT_LETTER_OR_NUMBER_REGEXP,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';

export const invalidLengthPasswordErrorMessage =
  'La contraseña deben tener al menos 8 caracteres';
export const notUppercasePasswordErrorMessage =
  'La contraseña debe tener al menos una letra mayúscula';
export const notLowercasePasswordErrorMessage =
  'La contraseña debe tener al menos una letra minúscula';
export const notNumberPasswordErrorMessage =
  'La contraseña debe tener al menos un número';
export const notValidCharacterPasswordErrorMessage =
  'La contraseña debe tener al menos un caracter no alfanumérico';

export class UserPassword {
  #password: string;

  private constructor(password: string) {
    this.#password = password;
  }

  get value(): string {
    return this.#password;
  }

  static create(password: string): UserPassword {
    if (!this.#isValidPasswordLength(password)) {
      throw new RuntimeMerchantError(
        invalidLengthPasswordErrorMessage,
        'UserPassword::entityCreation::invalidLength',
        'UserPassword'
      );
    }

    if (!this.#hasUppercase(password)) {
      throw new RuntimeMerchantError(
        notUppercasePasswordErrorMessage,
        'UserPassword::entityCreation::notUppercase',
        'UserPassword'
      );
    }

    if (!this.#hasLowercase(password)) {
      throw new RuntimeMerchantError(
        notLowercasePasswordErrorMessage,
        'UserPassword::entityCreation::notLowercase',
        'UserPassword'
      );
    }

    if (!this.#hasNumbers(password)) {
      throw new RuntimeMerchantError(
        notNumberPasswordErrorMessage,
        'UserPassword::entityCreation::notNumber',
        'UserPassword'
      );
    }

    if (!this.#hasValidCharacters(password)) {
      throw new RuntimeMerchantError(
        notValidCharacterPasswordErrorMessage,
        'UserPassword::entityCreation::notValidCharacter',
        'UserPassword'
      );
    }

    return new UserPassword(password);
  }

  static #isValidPasswordLength(password: string): boolean {
    const passwordMinLength = 8;
    return password.length >= passwordMinLength;
  }

  static #hasUppercase(password: string): boolean {
    return password.match(AT_LEAST_ONE_UPPERCASE_LETTER_REGEXP) !== null;
  }

  static #hasLowercase(password: string): boolean {
    return password.match(AT_LEAST_ONE_LOWERCASE_LETTER_REGEXP) !== null;
  }

  static #hasNumbers(password: string): boolean {
    return password.match(AT_LEAST_ONE_NUMBER_REGEXP) !== null;
  }

  static #hasValidCharacters(password: string): boolean {
    return (
      password.match(
        AT_LEAST_ONE_VALID_CHARACTER_NOT_LETTER_OR_NUMBER_REGEXP
      ) !== null
    );
  }
}
