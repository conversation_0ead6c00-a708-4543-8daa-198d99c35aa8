import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { UserEmail } from './user-email';

export interface IUserProps {
  merchantId: number;
  username: string;
  role: string;
  merchantName: string;
  email?: UserEmail;
  accessToken?: string;
  lastLogin?: Date;
}

export const userNullishMerchIdDefaultErrorMessage =
  'El ID del merchant no puede ser nulo';
export const userNullishUsernameDefaultErrorMessage =
  'El nombre del representante no puede ser nulo';
export const userNullishRoleDefaultErrorMessage = 'El rol no puede ser nulo';
export const userNullishMerchantNameDefaultErrorMessage =
  'El nombre del merchant no puede ser nulo';

export class User {
  readonly #epochDate = new Date('January 1, 1970 00:00:00 UTC');
  readonly username: string;
  readonly role: string;
  readonly merchantId: number;
  readonly merchantName: string;
  #email: UserEmail | undefined;
  #accessToken: string | undefined;
  #lastLogin: Date | undefined;

  static create(user: IUserProps): User {
    if (!Object.hasOwn(user, 'merchantId')) {
      throw new RuntimeMerchantError(
        userNullishMerchIdDefaultErrorMessage,
        'NewUser::createUser::nullishMerchanId',
        'UserCreate'
      );
    }

    if (!Object.hasOwn(user, 'username')) {
      throw new RuntimeMerchantError(
        userNullishUsernameDefaultErrorMessage,
        'NewUser::createUser::nullishUsername',
        'UserCreate'
      );
    }

    if (!Object.hasOwn(user, 'role')) {
      throw new RuntimeMerchantError(
        userNullishRoleDefaultErrorMessage,
        'NewUser::createUser::nullishRole',
        'UserCreate'
      );
    }

    if (!Object.hasOwn(user, 'merchantName')) {
      throw new RuntimeMerchantError(
        userNullishMerchantNameDefaultErrorMessage,
        'NewUser::createUser::nullishMerchantName',
        'UserCreate'
      );
    }

    const userAttributes = Object.keys(user);

    const unsucceededList = userAttributes
      .map(key => Guard.againstNullOrUndefined(user, key))
      .filter(guardResult => !guardResult.succeeded);

    if (unsucceededList.length > 0) {
      const message = unsucceededList.reduce((accum, current) => {
        return accum + ` | <${current.message}> `;
      }, 'Error al crear usuario: ');
      throw new RuntimeMerchantError(
        message,
        'NewUser::createUser::nullish',
        'UserCreate'
      );
    }

    const emptiedList = userAttributes
      .map(key => Guard.againstEmptyValue(user, key))
      .filter(guardResult => !guardResult.succeeded);

    if (emptiedList.length > 0) {
      const message = emptiedList.reduce((accum, current) => {
        return accum + ` | <${current.message}> `;
      }, 'Error al crear usuario: ');

      throw new RuntimeMerchantError(
        message,
        'NewUser::createUser::empty',
        'UserCreate'
      );
    }

    return new User(user);
  }

  private constructor(user: IUserProps) {
    this.username = user.username;
    this.role = user.role;
    this.merchantId = user.merchantId;
    this.merchantName = user.merchantName;
    this.#email = user.email;
    this.#accessToken = user.accessToken;
    this.#lastLogin = user.lastLogin;
  }

  get isLoggedIn(): boolean {
    return Boolean(this.#accessToken?.trim());
  }

  get accessToken(): string {
    return this.#accessToken ?? '';
  }

  get lastLogin(): Date {
    return this.#lastLogin ?? this.#epochDate;
  }

  get email(): string {
    return this.#email?.value ?? '';
  }

  setEmail(email: UserEmail): void {
    this.#email = email;
  }

  setAccessToken(token: string): void {
    this.#accessToken = token;
    this.#lastLogin = new Date();
  }
}
