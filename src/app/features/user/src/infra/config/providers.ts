import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { DashUserPersistenceService } from '../../application/services/local-persistence.service';
import { UserStoreService } from '../../application/services/user-store.service';
import { UserForgotPasswordResetRepository } from '../../domain/repositories/user-forgot-password-v2.repository';
import { UserForgotPasswordRepository } from '../../domain/repositories/user-forgot-password.repository';
import { UserForgottenPasswordVerificationTokenRepository } from '../../domain/repositories/user-forgotten-password-verification-token.repository';
import {
  UserNewRolesSetNewPasswordRepository,
  UserSetNewPasswordRepository,
} from '../../domain/repositories/user-set-new-password.repository';
import { UserRepository } from '../../domain/repositories/user.repository';
import { UserForgotPasswordRepositoryImpl } from '../repositories/user-forgot-password-impl.repository';
import { UserForgotPasswordResetV2WithHttp } from '../repositories/user-forgot-password-v2.repository';
import { UserForgottenPasswordVerificationTokenRepositoryImpl } from '../repositories/user-forgotten-password-verification-token.repository';
import { UserNewRolesSetNewPasswordWithHttp } from '../repositories/user-new-roles-set-password-with-http.repository';
import { UserSetNewPasswordRepositoryImpl } from '../repositories/user-set-new-password.repository';
import { UserRepositoryImpl } from '../repositories/user.repository';
import { DashUserSimpleStore } from '../services/dash-user-simple-store.service';
import { DashPersistenceWithNativeStorage } from '../services/persistence-implementation.service';
import { LoginCentralizedRepository } from '../../domain/repositories/login-centralized.repository';
import { LoginCentralizedRepositoryImpl } from '../repositories/login-centralized-repository-impl';

export function provideUserRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: UserRepository,
      useClass: UserRepositoryImpl,
    },
    {
      provide: UserForgotPasswordRepository,
      useClass: UserForgotPasswordRepositoryImpl,
    },
    {
      provide: UserForgottenPasswordVerificationTokenRepository,
      useClass: UserForgottenPasswordVerificationTokenRepositoryImpl,
    },
    {
      provide: UserSetNewPasswordRepository,
      useClass: UserSetNewPasswordRepositoryImpl,
    },
    {
      provide: UserForgotPasswordResetRepository,
      useClass: UserForgotPasswordResetV2WithHttp,
    },
    {
      provide: UserNewRolesSetNewPasswordRepository,
      useClass: UserNewRolesSetNewPasswordWithHttp,
    },
    {
      provide: LoginCentralizedRepository,
      useClass: LoginCentralizedRepositoryImpl,
    },
  ]);
}

export function provideUserServices(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: UserStoreService,
      useClass: DashUserSimpleStore,
    },
    {
      provide: DashUserPersistenceService,
      useClass: DashPersistenceWithNativeStorage,
    },
  ]);
}
