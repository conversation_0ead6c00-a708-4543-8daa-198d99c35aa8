import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { JWTToken } from '@aplazo/merchant/shared';
import { Observable, map } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { LoginCentralizedRepository } from '../../domain/repositories/login-centralized.repository';
import { CredentialsLoginCentralized } from '../../domain/entities/credentials-login-centralized';

@Injectable({ providedIn: 'root' })
export class LoginCentralizedRepositoryImpl
  implements LoginCentralizedRepository<Observable<string>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  execute(credentials: CredentialsLoginCentralized): Observable<JWTToken> {
    return this.#http
      .post<{ authToken: string }>(
        `${this.#environment.exposedPublicApiUrl}api/v1/auth/merchant/login`,
        {
          authType: credentials.authType,
          merchantPassword: credentials.merchantPassword,
          merchantUsername: credentials.merchantUsername,
        }
      )
      .pipe(map(response => response.authToken.replace('Bearer ', '')));
  }
}
