import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { UserForgotPasswordRepository } from '../../domain/repositories/user-forgot-password.repository';

@Injectable({
  providedIn: 'root',
})
export class UserForgotPasswordRepositoryImpl
  implements UserForgotPasswordRepository<FormData, Observable<void>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  requestReset(args: FormData): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.apiBaseUrl}merchant/forgot-password`,
      args
    );
  }
}
