import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { UserForgotPasswordNewRolesDto } from '../../application/dtos/forgot-password-v2.dto';
import { UserForgotPasswordResetRepository } from '../../domain/repositories/user-forgot-password-v2.repository';

@Injectable({
  providedIn: 'root',
})
export class UserForgotPasswordResetV2WithHttp
  implements
    UserForgotPasswordResetRepository<
      string,
      Observable<UserForgotPasswordNewRolesDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  reset(email: string): Observable<UserForgotPasswordNewRolesDto> {
    return this.#http.post<UserForgotPasswordNewRolesDto>(
      `${this.#environment.apiMerchantAccessBaseUrl}/auth/generate-reset-code`,
      {
        email,
      }
    );
  }
}
