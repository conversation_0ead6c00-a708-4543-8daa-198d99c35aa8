import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { IUserForgottenPasswordVerificationTokenDto } from '../../application/dtos/verification-token.dto';
import { UserForgottenPasswordVerificationTokenRepository } from '../../domain/repositories/user-forgotten-password-verification-token.repository';

@Injectable({
  providedIn: 'root',
})
export class UserForgottenPasswordVerificationTokenRepositoryImpl
  implements
    UserForgottenPasswordVerificationTokenRepository<
      IUserForgottenPasswordVerificationTokenDto,
      Observable<{ status: boolean }>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  public validateToken(
    args: IUserForgottenPasswordVerificationTokenDto
  ): Observable<{ status: boolean }> {
    return this.#http.get<{ status: boolean }>(
      `${this.#environment.apiBaseUrl}merchant/forgot-verify`,
      {
        params: {
          token: args.token,
        },
      }
    );
  }
}
