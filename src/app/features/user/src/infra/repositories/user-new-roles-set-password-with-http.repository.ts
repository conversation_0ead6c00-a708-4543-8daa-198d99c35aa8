import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { NewRoleSetPasswordDto } from '../../application/dtos/new-role-set-password.dto';
import { UserNewRolesSetNewPasswordRepository } from '../../domain/repositories/user-set-new-password.repository';

@Injectable({
  providedIn: 'root',
})
export class UserNewRolesSetNewPasswordWithHttp
  implements
    UserNewRolesSetNewPasswordRepository<
      { code: string; password: string },
      Observable<NewRoleSetPasswordDto>
    >
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  setPassword(args: {
    code: string;
    password: string;
  }): Observable<NewRoleSetPasswordDto> {
    return this.#http.post<NewRoleSetPasswordDto>(
      `${this.#environment.apiMerchantAccessBaseUrl}/auth/change-password`,
      args
    );
  }
}
