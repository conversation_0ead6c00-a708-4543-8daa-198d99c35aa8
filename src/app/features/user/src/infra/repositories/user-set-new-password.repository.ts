import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { UserSetNewPasswordRepository } from '../../domain/repositories/user-set-new-password.repository';

@Injectable({
  providedIn: 'root',
})
export class UserSetNewPasswordRepositoryImpl
  implements UserSetNewPasswordRepository<FormData, Observable<void>>
{
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  setPassword(args: FormData): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.apiBaseUrl}merchant/forgot-change-password`,
      args
    );
  }
}
