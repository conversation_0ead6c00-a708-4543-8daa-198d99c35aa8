import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { JWTToken } from '@aplazo/merchant/shared';
import { Observable, map } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../config/merchant-core.environment';
import { Credentials } from '../../domain/entities/credentials';
import { UserRepository } from '../../domain/repositories/user.repository';

@Injectable({ providedIn: 'root' })
export class UserRepositoryImpl implements UserRepository<Observable<string>> {
  readonly #environment = inject(MERCHANT_CORE_ENVIRONMENT);
  readonly #http = inject(HttpClient);

  loginWithoutEmail(credentials: Credentials): Observable<JWTToken> {
    return this.#http
      .post<{ content: { token: string }; error: unknown; code: number }>(
        `${this.#environment.apiMerchantAccessBaseUrl}/auth/login`,
        {
          login: credentials.username,
          password: credentials.password,
          username: credentials.username,
        }
      )
      .pipe(map(r => r.content.token));
  }

  loginWithEmail(credentials: Credentials): Observable<JWTToken> {
    return this.#http
      .post<{ Authorization: string }>(`${this.#environment.apiBaseUrl}login`, {
        login: credentials.username,
        password: credentials.password,
      })
      .pipe(
        map(r => r.Authorization),
        map(authToken => authToken.replace('Bearer ', ''))
      );
  }
}
