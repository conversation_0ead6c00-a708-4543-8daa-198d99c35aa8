import { Injectable } from '@angular/core';
import { BehaviorSubject, map } from 'rxjs';
import { IUserPersistenceDTO } from '../../application/dtos/user-persistence.dto';
import { UserStoreService } from '../../application/services/user-store.service';

export const emptyUser: IUserPersistenceDTO = {
  username: '',
  email: '',
  merchantId: 0,
  merchantName: '',
  role: '',
  accessToken: '',
  lastLogin: '',
  isLoggedIn: false,
};

@Injectable({ providedIn: 'root' })
export class DashUserSimpleStore implements UserStoreService {
  readonly #user$ = new BehaviorSubject<IUserPersistenceDTO>(emptyUser);
  readonly #error$ = new BehaviorSubject<unknown>(null);

  user$ = this.#user$.asObservable();

  tokenSession$ = this.user$.pipe(map(user => user.accessToken));

  userEmail$ = this.user$.pipe(map(user => user.email));

  username$ = this.user$.pipe(map(user => user.username));

  merchantId$ = this.user$.pipe(
    map(user => (isNaN(+user.merchantId) ? 0 : user.merchantId))
  );

  merchantName$ = this.user$.pipe(map(user => user.merchantName));

  merchant$ = this.user$.pipe(
    map(user => ({
      name: user.merchantName,
      id: user.merchantId?.toString() ?? '0',
      email: user.email,
    }))
  );

  role$ = this.user$.pipe(map(user => user.role));

  isLoggedIn$ = this.user$.pipe(map(user => user.isLoggedIn));

  setUser(user: IUserPersistenceDTO): void {
    this.#error$.next(null);
    if (user == null) {
      this.#user$.next(emptyUser);
      return;
    }
    this.#user$.next(user);
  }

  setFailure(error: unknown): void {
    this.#error$.next(error);
    this.#user$.next(emptyUser);
  }

  getRole(): string {
    return this.#user$.getValue().role;
  }

  clearUserStore(): void {
    this.#error$.next(null);
    this.#user$.next(emptyUser);
  }
}
