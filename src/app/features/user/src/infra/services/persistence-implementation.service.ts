import { Injectable, inject, DOCUMENT } from '@angular/core';
import { IUserPersistenceDTO } from '../../application/dtos/user-persistence.dto';
import { DashUserPersistenceService } from '../../application/services/local-persistence.service';
@Injectable({
  providedIn: 'root',
})
export class DashPersistenceWithNativeStorage
  implements DashUserPersistenceService
{
  readonly #document = inject(DOCUMENT);
  readonly #appStorageKey = 'AplazoToken';
  readonly #sessionStorage = this.#document.defaultView?.sessionStorage;

  async getAuthenticatedUser(): Promise<IUserPersistenceDTO> {
    const rawUser = this.#sessionStorage.getItem(this.#appStorageKey);

    if (rawUser) {
      const userPersistence = JSON.parse(rawUser) as IUserPersistenceDTO;

      return userPersistence;
    }

    throw new Error('There is no user saved');
  }

  async saveAuthenticatedUser(user: IUserPersistenceDTO): Promise<void> {
    this.#sessionStorage.setItem(this.#appStorageKey, JSON.stringify(user));
  }

  async deAuthenticateUser(): Promise<void> {
    this.#sessionStorage.removeItem(this.#appStorageKey);
  }
}
