import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { StatsigService } from '@statsig/angular-bindings';
import { DASH_ROUTES } from '../config/app-route-core';

export const isUnderMaintenanceGuard: CanActivateFn = (_, state) => {
  const service = inject(StatsigService);
  const router = inject(Router);

  const isMaintenanceEnabled = service.checkGate(
    'b2b_front_maintenance_screen_enabled'
  );
  const isMaintenanceScreen = state.url.includes(DASH_ROUTES.unavailable);

  if (isMaintenanceEnabled && !isMaintenanceScreen) {
    return router.parseUrl(`/${DASH_ROUTES.unavailable}`);
  }

  if (!isMaintenanceEnabled && isMaintenanceScreen) {
    return router.parseUrl(`/${DASH_ROUTES.authentication}`);
  }

  return true;
};
