import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map } from 'rxjs';
import { DASH_ROUTES } from '../config/app-route-core';
import { UserStoreService } from '../features/user/src/application/services/user-store.service';

export const preventNavigationByUnauthenticatedUser: CanActivateFn = () => {
  const userStore = inject(UserStoreService);
  const router = inject(Router);

  return userStore.isLoggedIn$.pipe(
    map(isLoggedIn => {
      if (isLoggedIn) {
        return router.parseUrl(`/${DASH_ROUTES.rootApp}`);
      }

      return true;
    })
  );
};
