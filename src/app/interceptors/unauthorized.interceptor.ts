import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { take, tap } from 'rxjs';
import { DASH_ROUTES } from '../config/app-route-core';
import { MERCHANT_CORE_ENVIRONMENT } from '../config/merchant-core.environment';
import { UserLogoutUseCase } from '../features/user/src/application/usecases/logout.usecase';

export const unauthorizedInterceptor: HttpInterceptorFn = (request, next) => {
  const notifier = inject(NotifierService);
  const environment = inject(MERCHANT_CORE_ENVIRONMENT);
  const logoutUsecase = inject(UserLogoutUseCase);

  return next(request).pipe(
    tap({
      error: (event: HttpErrorResponse) => {
        if (
          event instanceof HttpErrorResponse &&
          [403].includes(event.status) &&
          request.url !== `${environment.apiBaseUrl}login`
        ) {
          logoutUsecase
            .execute(`/${DASH_ROUTES.authentication}`)
            .pipe(take(1))
            .subscribe();
          notifier.warning({
            title: 'La sesión ha expirado',
            message: 'Por favor, inicie sesión nuevamente',
          });
        }
      },
    })
  );
};
