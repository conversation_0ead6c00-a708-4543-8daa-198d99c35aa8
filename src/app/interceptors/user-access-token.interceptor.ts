import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { map, mergeMap, withLatestFrom } from 'rxjs';
import { UserStoreService } from '../features/user/src/application/services/user-store.service';

export const userAccessTokenInterceptor: HttpInterceptorFn = (
  request,
  next
) => {
  const userStore = inject(UserStoreService);
  const isFileSkippedByExtension = request.url.match(
    /\.svg$|\.png$|\.jpg$|\.webp$|\.jpeg$|\.gif$|\.mp4$|\.json$/
  );

  if (isFileSkippedByExtension) {
    return next(request);
  }

  return userStore.isLoggedIn$.pipe(
    withLatestFrom(userStore.tokenSession$),
    map(([isLoggedIn, token]) =>
      isLoggedIn
        ? request.clone({
            headers: request.headers.set(
              'Authorization',
              `Bearer ${token.replace('Bearer ', '')}`
            ),
          })
        : request
    ),
    mergeMap(newReq => next(newReq))
  );
};
