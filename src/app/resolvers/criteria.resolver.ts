import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { take, tap } from 'rxjs';
import { IBranchOfficeUIDto } from '../features/branch-offices/src/domain/entities/branch-office';
import { BranchOfficesStore } from '../services/branch-offices.store';
import { SharedCriteria } from '../services/shared-criteria.store';

export const loadBranchOfficesIntoCriteria: ResolveFn<
  IBranchOfficeUIDto[]
> = () => {
  const criteria = inject(SharedCriteria);
  const branchOfficesStore = inject(BranchOfficesStore);

  return branchOfficesStore.branchOffices$().pipe(
    tap(branches => {
      criteria.setBranchOffices({
        selection: branches.map(branch => branch.id),
        allSelected: true,
      });
    }),
    take(1)
  );
};
