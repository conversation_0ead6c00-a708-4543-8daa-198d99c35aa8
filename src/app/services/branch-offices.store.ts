import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { IBranchOfficeUIDto } from '../features/branch-offices/src/domain/entities/branch-office';

@Injectable({ providedIn: 'root' })
export class BranchOfficesStore {
  readonly #branchOffices$ = new BehaviorSubject<IBranchOfficeUIDto[]>([]);

  branchOffices$() {
    return this.#branchOffices$.asObservable();
  }

  setBranchOffices(branchOffices: IBranchOfficeUIDto[]): void {
    const current = this.#branchOffices$.getValue();

    if (
      current.length === branchOffices.length &&
      branchOffices.every(branch => current.some(b => b.id === branch.id)) &&
      current.every(branch => branchOffices.some(b => b.id === branch.id))
    ) {
      return;
    }

    this.#branchOffices$.next(branchOffices);
  }

  clearBranchOffices(): void {
    this.#branchOffices$.next([]);
  }
}
