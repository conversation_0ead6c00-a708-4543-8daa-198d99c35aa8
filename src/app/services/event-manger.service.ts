import { Injectable, inject } from '@angular/core';
import {
  GtmMerchantEventDescription,
  TagManagerService,
} from '@aplazo/front-analytics/tag-manager';
import { take } from 'rxjs';
import { UserStoreService } from '../features/user/src/application/services/user-store.service';

export const GTM_MERCHANT_EVENT_NAMES = [
  'pageView',
  'buttonClick',
  'dateRange',
  'selection',
  'status',
  'search',
  'pagination',
  'tabSelection',
  'graphTypeSelection',
  'success',
  'failure',
  'customClick',
  'loginRefreshed',
  'balancePageView',
  'downloadSuccess',
  'downloadFailure',
  'refundButtonClick',
  'applyRefundEvent',
  'account_statement_quick_access_download_button_click',
  'account_statement_quick_access_download_transactions_click',
  'account_statement_quick_access_download_receipts_click',
  'account_statement_quick_access_download_invoices_click',
] as const;

export type GTMMerchantEventName = (typeof GTM_MERCHANT_EVENT_NAMES)[number];

@Injectable({
  providedIn: 'root',
})
export class EventManagerService {
  readonly #userStore = inject(UserStoreService);
  readonly #tagManager = inject(TagManagerService);

  sendTrackEvent(
    event: GTMMerchantEventName,
    description: Partial<GtmMerchantEventDescription>
  ) {
    this.#userStore.merchant$.pipe(take(1)).subscribe(merchant => {
      const initialDescription: GtmMerchantEventDescription = {
        startDate: '',
        endDate: '',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
        buttonName: '',
        genericInfo: '',
        url: '',
        title: '',
        category: '',
        label: '',
        timestamp: undefined,
      };

      const newEvent = {
        event,
        merchantId: isNaN(+merchant?.id) ? 0 : +merchant.id,
        merchantName: merchant?.name ?? '',
        description: { ...initialDescription, ...description },
      };

      this.#tagManager.trackEvent(newEvent);
    });
  }
}
