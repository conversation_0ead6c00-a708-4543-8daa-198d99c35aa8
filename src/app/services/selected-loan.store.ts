import { Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { BehaviorSubject } from 'rxjs';
import { IMerchantLoanUiDto } from '../features/loan/domain/dtos/merchant-loan-ui.dto';
import { Refund } from '../features/refund/domain/entities/refund';
export const emptyLoanInfodefaultError =
  'No se esta proporcionando información de pedido.';

@Injectable({
  providedIn: 'root',
})
export class SelectedLoanStore {
  readonly #loan$ = new BehaviorSubject<IMerchantLoanUiDto | null>(null);
  readonly #refunds$ = new BehaviorSubject<Refund[]>([]);

  readonly loan$ = this.#loan$.asObservable();
  readonly refunds$ = this.#refunds$.asObservable();

  get hasSelectedLoan(): boolean {
    return this.#loan$?.value != null;
  }

  loanAmount(): number {
    return this.#loan$?.value?.loan?.total ?? 0;
  }

  setRefunds(refunds: Refund[]): void {
    if (refunds == null || !Array.isArray(refunds)) {
      return;
    }

    this.#refunds$.next(refunds);
  }

  setInfo(loan: IMerchantLoanUiDto): void {
    if (loan == null) {
      throw new RuntimeMerchantError(
        emptyLoanInfodefaultError,
        'SelectedLoanStore::setInfo::emptyLoan'
      );
    }

    if (loan.loan.id == this.#loan$.getValue()?.loan.id) {
      return;
    }

    this.#loan$.next(loan);
  }

  clearLoan(): void {
    this.#loan$.next(null);
  }
}
