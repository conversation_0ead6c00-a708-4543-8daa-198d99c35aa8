import { Injectable } from '@angular/core';
import {
  B2BDateRange,
  DayByNumber,
  RawDateDayFirst,
  StatusGroupingId,
} from '@aplazo/merchant/shared';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { SharedCriteriaWithBranchOfficesUIDto } from '../features/shared/shared-criteria';

const config = {
  locale: es,
};
const today = new Date();
const todayAsRawDateDayFirst = format(
  today,
  'dd/MM/yyyy',
  config
) as RawDateDayFirst;

export const initialSharedCriteria: SharedCriteriaWithBranchOfficesUIDto = {
  dateRange: {
    startDate: todayAsRawDateDayFirst,
    endDate: todayAsRawDateDayFirst,
  },
  pageNum: 0,
  pageSize: 10,
  element: '',
  status: 'approved',
  branchOffices: [],
  isAllBranchesSelected: false,
  days: [],
};

@Injectable({
  providedIn: 'root',
})
export class SharedCriteria {
  readonly #criteria$ =
    new BehaviorSubject<SharedCriteriaWithBranchOfficesUIDto>(
      initialSharedCriteria
    );

  readonly criteria$ = this.#criteria$.asObservable();

  selectedBranchOffices$ = this.criteria$.pipe(
    map(criteria => {
      return criteria.branchOffices;
    })
  );

  dateRange$: Observable<B2BDateRange> = this.criteria$.pipe(
    map(criteria => {
      return criteria.dateRange;
    })
  );

  page$: Observable<number> = this.criteria$.pipe(
    map(criteria => {
      return criteria.pageNum;
    })
  );

  itemsToShow$: Observable<number> = this.criteria$.pipe(
    map(criteria => {
      return criteria.pageSize;
    })
  );

  search$: Observable<string> = this.criteria$.pipe(
    map(criteria => {
      return criteria.element;
    })
  );

  status$: Observable<string> = this.criteria$.pipe(
    map(criteria => {
      return criteria.status;
    })
  );

  days$: Observable<number[]> = this.criteria$.pipe(
    map(criteria => {
      return criteria.days;
    })
  );

  getDateRangeSync(): B2BDateRange {
    return this.#criteria$.getValue().dateRange;
  }

  setCriteria(criteria: SharedCriteriaWithBranchOfficesUIDto): void {
    if (criteria == null) {
      this.#criteria$.next(initialSharedCriteria);
      return;
    }

    this.#criteria$.next(criteria);
  }

  clearCriteria(): void {
    this.#criteria$.next(initialSharedCriteria);
  }

  setDateRange(dateRange: B2BDateRange): void {
    const criteria = this.#criteria$.getValue();

    this.#criteria$.next({
      ...criteria,
      pageNum: 0,
      dateRange: {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      },
      element: '',
    });
  }

  setPageNum(pageNum: number): void {
    const criteria = this.#criteria$.getValue();

    if (criteria.pageNum == pageNum) {
      return;
    }

    this.#criteria$.next({
      ...criteria,
      pageNum,
      element: '',
    });
  }

  setPageSize(pageSize: number): void {
    const criteria = this.#criteria$.getValue();

    if (criteria.pageSize == pageSize) {
      return;
    }

    this.#criteria$.next({
      ...criteria,
      pageNum: 0,
      element: '',
      pageSize,
    });
  }

  setStatus(status: StatusGroupingId): void {
    const criteria = this.#criteria$.getValue();

    if (criteria.status === status) {
      return;
    }

    this.#criteria$.next({
      ...criteria,
      pageNum: 0,
      status,
      element: '',
    });
  }

  setBranchOffices(value: { selection: number[]; allSelected: boolean }): void {
    const criteria = this.#criteria$.getValue();

    if (
      !value.selection ||
      !Array.isArray(value.selection) ||
      (criteria.branchOffices.every(b => value.selection.includes(b)) &&
        value.selection.every(b => criteria.branchOffices.includes(b)) &&
        criteria.isAllBranchesSelected === value.allSelected)
    ) {
      return;
    }

    this.#criteria$.next({
      ...criteria,
      pageNum: 0,
      element: '',
      branchOffices: value.selection,
      isAllBranchesSelected: value.allSelected,
    });
  }

  setSearch(search: string): void {
    const criteria = this.#criteria$.getValue();

    if (criteria.element == search) {
      return;
    }

    this.#criteria$.next({
      ...criteria,
      pageNum: 0,
      element: search,
    });
  }

  async setDays(days: DayByNumber[]): Promise<void> {
    const criteria = this.#criteria$.getValue();

    if (
      days == null ||
      !Array.isArray(days) ||
      (criteria.days.every(d => days.includes(d)) &&
        days.every(d => criteria.days.includes(d)))
    ) {
      return;
    }

    this.#criteria$.next({
      ...criteria,
      pageNum: 0,
      element: '',
      days,
    });
  }
}
