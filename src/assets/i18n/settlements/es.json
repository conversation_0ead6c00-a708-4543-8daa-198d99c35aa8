{"list": {"id": "Loan ID", "date": "<PERSON><PERSON>", "sales": "Monto de venta", "feeRevenue": "Total fee con impuestos", "payMerchant": "Total monto a pagar", "status": "<PERSON><PERSON><PERSON>", "statusPayment": "<PERSON><PERSON><PERSON>", "cartId": "ID carrito", "branch": "Sucursal"}, "stats": {"amountToPay": {"statCardTitle": "TOTAL MONTO A PAGAR", "helpTooltip": "Monto de venta menos fee menos IVA. Puede variar por reembolsos y cancelaciones"}, "salesAmount": {"statCardTitle": "TOTAL DE VENTAS", "helpTooltip": "Total de ventas realizadas con Aplazo en el período de tiempo seleccionado incluye impuestos"}, "feeRevenue": {"statCardTitle": "TOTAL FEE + IMPUESTOS", "helpTooltip": "Total comisión de Aplazo incluyendo impuestos para el período seleccionado"}, "salesOrder": {"statCardTitle": "NÚMERO DE VENTAS", "helpTooltip": "Es el total de tus pedidos  con Aplazo en el período de tiempo seleccionado"}}, "downloadButton": {"label": "<PERSON><PERSON><PERSON>"}, "daterangePicker": {"tooltip": "Selecciona un rango de fecha"}, "branchOffices": {"form": {"label": "Sucursal"}, "notification": {"error": {"title": "Error", "message": "Se requiere por lo menos una sucursal para obtener información"}}, "selection": {"allSelected": "<PERSON><PERSON>", "emptyList": "<PERSON> sucursales"}}, "counterLabel": {"empty": "VENTAS", "singular": "VENTA", "plural": "VENTAS"}, "searchbar": {"placeholder": "Buscar ID, Email, Teléfono, Cart ID", "minlengthError": "Para una búsqueda ingrese mínimo {{ minLength }} caracteres", "requiredError": "Para una búsqueda ingrese mínimo {{ minLength }} caracteres"}, "loansStatus": {"notification": {"error": {"title": "Error", "message": "Se requiere por lo menos un estatus para obtener información"}}, "form": {"label": "Mostrar"}, "tooltip": "Selecciona el estatus de las ventas"}, "emptyLoans": {"title": "Sin resultados para mostrar", "description": "Selecciona una fecha de inicio y fin de tu reporte, así como los estatus a consultar. Podrás descargar un archivo .xlsx (Microsoft Excel)", "button": {"label": ""}}, "emptySearch": {"title": "Ups, no encontramos una venta que coincida con esa b<PERSON>queda.", "description": "Prueba con una combinación diferente de fechas o datos de venta."}, "allLoanStatusLabels": {"enabled": true}}