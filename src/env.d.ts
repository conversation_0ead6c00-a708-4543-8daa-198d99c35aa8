// Define the type of the environment variables.
declare interface Env {
  readonly NODE_ENV: string;
  readonly NG_APP_PRODUCTION: boolean;
  readonly NG_APP_API_URL: string;
  readonly NG_APP_API_MICROSERVICE_URL: string;
  readonly NG_APP_MERCHANT_ACCESS_BASE_URL: string;
  readonly NG_APP_LANDING_URL: string;
  readonly NG_APP_CUSTOMER_LOGIN_URL: string;
  readonly NG_APP_CUSTOMER_REGISTER_URL: string;
  readonly NG_APP_POSUI_URL: string;
  readonly NG_APP_POSUI_REGISTER_URL: string;
  readonly NG_APP_POS_API_URL: string;
  readonly NG_APP_MERCHANT_LOGIN_URL: string;
  readonly NG_APP_MERCHANT_REGISTER_URL: string;
  readonly NG_APP_SALESFORCE_REGISTER_URL: string;
  readonly NG_APP_MERCHANT_PATH: string;
  readonly NG_APP_I18N_URL: string;
  readonly NG_APP_FLAGS_AUTHORIZATION_KEY: string;
  readonly NG_APP_FLAGS_ENV: string;
  readonly NG_APP_GTM_ID: string;
  readonly NG_APP_WEBCHAT_API_KEY: string;
  readonly NG_APP_WEBCHAT_BRAND_ID: string;
  readonly NG_APP_DATADOG_APPLICATION_ID: string;
  readonly NG_APP_DATADOG_CLIENT_TOKEN: string;
  readonly NG_APP_DATADOG_ENV: string;
  readonly NG_APP_DATADOG_SERVICE: string;
  readonly NG_APP_EXPOSED_PUBLIC_API_URL: string;
  readonly NG_APP_PROMO_API_URL: string;
}

// 1. Use import.meta.env.YOUR_ENV_VAR in your code. (conventional)
declare interface ImportMeta {
  readonly env: Env;
}
