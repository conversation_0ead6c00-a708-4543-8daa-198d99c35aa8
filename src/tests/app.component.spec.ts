import { Async<PERSON>ipe } from '@angular/common';
import { Component } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { provideRouter, RouterOutlet } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { LoaderService, WebchatService } from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';
import { of } from 'rxjs';
import { AppComponent } from '../app/app.component';
import { appConfig } from '../app/app.config';
import { UserStoreService } from '../app/features/user/src/application/services/user-store.service';
import { EventManagerService } from '../app/services/event-manger.service';

@Component({
  selector: 'app-auth-test',
  template: '',
  standalone: true,
})
export class TestAuthComponent {}

@Component({
  selector: 'app-home-test',
  template: '',
  standalone: true,
})
export class TestHomeComponent {}

let routerHarness: RouterTestingHarness;
let component: AppComponent;
let webchatSpy: jasmine.SpyObj<WebchatService<any>>;
let tagManagerSpy: jasmine.SpyObj<EventManagerService>;

const setup = async (userMock: unknown) => {
  await TestBed.configureTestingModule(
    Object.assign({}, appConfig, {
      imports: [
        RouterOutlet,
        AppComponent,
        AplazoPillLoaderComponent,
        AsyncPipe,
      ],
      providers: [
        provideRouter([{ path: '**', component: AppComponent }]),

        {
          provide: LoaderService,
          useValue: {
            isLoading$: of(false),
            loaderMessage$: of(''),
          },
        },
        {
          provide: WebchatService,
          useValue: jasmine.createSpyObj('WebChatService', ['hide', 'show']),
        },
        {
          provide: EventManagerService,
          useValue: jasmine.createSpyObj('EventManagerService', [
            'sendTrackEvent',
          ]),
        },
        {
          provide: UserStoreService,
          useValue: userMock,
        },
      ],
    })
  ).compileComponents();

  routerHarness = await RouterTestingHarness.create();
  component = await routerHarness.navigateByUrl('/', AppComponent);
  routerHarness.detectChanges();
  webchatSpy = TestBed.inject(WebchatService) as jasmine.SpyObj<
    WebchatService<any>
  >;
  tagManagerSpy = TestBed.inject(
    EventManagerService
  ) as jasmine.SpyObj<EventManagerService>;
};

describe('AppComponent', () => {
  it('should be created', async () => {
    await setup({ merchant$: of({}) });

    expect(component).toBeTruthy();
  });

  it('should track pageView event and show webchat on NavigationEnd ', fakeAsync(async () => {
    await setup({
      merchant$: of({ id: 1, name: 'test' }),
    });
    tagManagerSpy.sendTrackEvent.and.returnValue(undefined);
    webchatSpy.show.and.returnValue(undefined);
    webchatSpy.hide.and.returnValue(undefined);

    routerHarness.navigateByUrl('/not-auth');

    tick();

    expect(tagManagerSpy.sendTrackEvent).toHaveBeenCalledTimes(1);
    expect(webchatSpy.show).toHaveBeenCalledTimes(1);

    expect(webchatSpy.hide).toHaveBeenCalledTimes(0);
  }));

  it('should hide webchat on NavigationEnd ', fakeAsync(async () => {
    await setup({
      merchant$: of({ id: 1, name: 'test' }),
    });
    tagManagerSpy.sendTrackEvent.and.callThrough();
    webchatSpy.show.and.returnValue(undefined);
    webchatSpy.hide.and.returnValue(undefined);

    routerHarness.navigateByUrl('/authorization');

    tick();

    expect(tagManagerSpy.sendTrackEvent).toHaveBeenCalledTimes(1);
    expect(webchatSpy.hide).toHaveBeenCalledTimes(1);

    expect(webchatSpy.show).toHaveBeenCalledTimes(0);
  }));

  it('should call trackEvent with null values when merchant is undefined', fakeAsync(async () => {
    await setup({
      merchant$: of(undefined),
    });
    tagManagerSpy.sendTrackEvent.and.callThrough();
    webchatSpy.show.and.returnValue(undefined);
    webchatSpy.hide.and.returnValue(undefined);

    routerHarness.navigateByUrl('/authorization');

    tick();

    expect(tagManagerSpy.sendTrackEvent).toHaveBeenCalledTimes(1);
    expect(tagManagerSpy.sendTrackEvent).toHaveBeenCalledWith('pageView', {});
  }));
});
