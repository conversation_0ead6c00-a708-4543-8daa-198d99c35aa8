import { MerchantMapper } from 'src/app/features/account/application/mappers/merchant.mapper';
import { IMerchantWithoutBillingDto } from '../../../../../app/features/account/application/dtos/merchant-without-billing.dto';
import { Merchant } from '../../../../../app/features/account/domain/entities/merchant';

describe('MerchantMapper', () => {
  const dto: IMerchantWithoutBillingDto = {
    address: 'Siempreviva 33 Springfield Massachusetts 01103 Estados Unidos',
    email: '<EMAIL>',
    name: 'Zapaterias Aplazo Test',
    representativeName: '<PERSON>',
    representativePhoneNumber: '**********',
    representativeRole: 'Representante Legal',
    website: 'zapateria.aplazo.mx',
  };

  const domain: Omit<Merchant, 'billingInfo'> = {
    address: 'Siempreviva 33 Springfield Massachusetts 01103 Estados Unidos',
    email: '<EMAIL>',
    name: 'Zapaterias Aplazo Test',
    website: 'zapateria.aplazo.mx',
    legalRepresentative: {
      name: '<PERSON>',
      phoneNumber: '**********',
      role: 'Representante Legal',
    },
  };

  it('should map from dto to domain', () => {
    const result = MerchantMapper.fromDtoToDomain(dto);

    expect(result).toEqual(domain);
  });

  it('should map from domain to dto', () => {
    const result = MerchantMapper.fromDomainToDto(domain);

    expect(result).toEqual(dto);
  });
});
