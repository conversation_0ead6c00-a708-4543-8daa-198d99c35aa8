import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';
import {
  NotifierService,
  UseCaseErrorHandler,
  LoaderService,
} from '@aplazo/merchant/shared';
import { DeleteContactUseCase } from 'src/app/features/account/application/usecases/delete-contact.usecase';
import { MerchantContactsRepository } from 'src/app/features/account/domain/repositories/contact-info.repository';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';

describe('DeleteContactUseCase', () => {
  let usecase: DeleteContactUseCase;
  let contactsRepository: jasmine.SpyObj<MerchantContactsRepository>;
  let notifierService: jasmine.SpyObj<NotifierService>;
  let errorHandler: jasmine.SpyObj<UseCaseErrorHandler>;
  let loaderService: jasmine.SpyObj<LoaderService>;

  const mockContactId = 123;

  beforeEach(() => {
    const contactsRepoSpy = jasmine.createSpyObj('MerchantContactsRepository', [
      'deleteOne',
    ]);
    const notifierSpy = jasmine.createSpyObj('NotifierService', ['success']);
    const errorHandlerSpy = jasmine.createSpyObj('UseCaseErrorHandler', [
      'handle',
    ]);
    const loaderSpy = jasmine.createSpyObj('LoaderService', ['show', 'hide']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        DeleteContactUseCase,
        { provide: MerchantContactsRepository, useValue: contactsRepoSpy },
        { provide: NotifierService, useValue: notifierSpy },
        { provide: UseCaseErrorHandler, useValue: errorHandlerSpy },
        { provide: LoaderService, useValue: loaderSpy },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    usecase = TestBed.inject(DeleteContactUseCase);
    contactsRepository = TestBed.inject(
      MerchantContactsRepository
    ) as jasmine.SpyObj<MerchantContactsRepository>;
    notifierService = TestBed.inject(
      NotifierService
    ) as jasmine.SpyObj<NotifierService>;
    errorHandler = TestBed.inject(
      UseCaseErrorHandler
    ) as jasmine.SpyObj<UseCaseErrorHandler>;
    loaderService = TestBed.inject(
      LoaderService
    ) as jasmine.SpyObj<LoaderService>;
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  describe('execute', () => {
    it('should delete contact successfully', fakeAsync(() => {
      loaderService.show.and.returnValue('loader-id');
      contactsRepository.deleteOne.and.returnValue(of(void 0));

      let completed = false;
      let error: any;

      usecase.execute(mockContactId).subscribe({
        next: () => {},
        complete: () => (completed = true),
        error: err => (error = err),
      });

      tick(); // Simulate passage of time until all async tasks complete

      expect(error).toBeUndefined();
      expect(completed).toBeTrue();
      expect(contactsRepository.deleteOne).toHaveBeenCalledWith(mockContactId);
      expect(notifierService.success).toHaveBeenCalledWith({
        title: 'Contacto eliminado exitosamente',
      });
      expect(loaderService.show).toHaveBeenCalled();
      expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
    }));

    it('should handle repository errors', fakeAsync(() => {
      const errorMessage = 'Delete failed';
      loaderService.show.and.returnValue('loader-id');
      contactsRepository.deleteOne.and.returnValue(
        throwError(() => new Error(errorMessage))
      );
      errorHandler.handle.and.returnValue(
        throwError(() => new Error(errorMessage))
      );

      let completed = false;
      let error: any;

      usecase.execute(mockContactId).subscribe({
        next: () => {},
        complete: () => (completed = true),
        error: err => (error = err),
      });

      tick();

      expect(completed).toBeFalse();
      expect(error).toBeTruthy();
      expect(contactsRepository.deleteOne).toHaveBeenCalled();
      expect(errorHandler.handle).toHaveBeenCalled();
      expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
    }));

    it('should validate contactId before deleting', fakeAsync(() => {
      const invalidContactId = NaN;
      errorHandler.handle.and.returnValue(
        throwError(() => new Error('El identificador del contacto es inválido'))
      );

      let error: any;

      usecase.execute(invalidContactId).subscribe({
        next: () => fail('Should have failed'),
        error: err => (error = err),
      });

      tick();

      expect(error).toBeTruthy();
      expect(error.message).toContain(
        'El identificador del contacto es inválido'
      );
      expect(contactsRepository.deleteOne).not.toHaveBeenCalled();
    }));

    it('should validate contactId is positive', fakeAsync(() => {
      const invalidContactId = -5;
      errorHandler.handle.and.returnValue(
        throwError(() => new Error('El identificador del contacto es inválido'))
      );

      let error: any;

      usecase.execute(invalidContactId).subscribe({
        next: () => fail('Should have failed'),
        error: err => (error = err),
      });

      tick();

      expect(error).toBeTruthy();
      expect(error.message).toContain(
        'El identificador del contacto es inválido'
      );
      expect(contactsRepository.deleteOne).not.toHaveBeenCalled();
    }));

    it('should validate contactId is not zero', fakeAsync(() => {
      const invalidContactId = 0;
      errorHandler.handle.and.returnValue(
        throwError(() => new Error('El identificador del contacto es inválido'))
      );

      let error: any;

      usecase.execute(invalidContactId).subscribe({
        next: () => fail('Should have failed'),
        error: err => (error = err),
      });

      tick();

      expect(error).toBeTruthy();
      expect(error.message).toContain(
        'El identificador del contacto es inválido'
      );
      expect(contactsRepository.deleteOne).not.toHaveBeenCalled();
    }));
  });
});
