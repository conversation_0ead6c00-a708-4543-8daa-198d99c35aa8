import { TestBed } from '@angular/core/testing';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import {
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { GetContactInfoUseCase } from 'src/app/features/account/application/usecases/get-contact.usecase';
import { MerchantContactsRepository } from 'src/app/features/account/domain/repositories/contact-info.repository';

describe('GetContactInfoUseCase', () => {
  const setup = (args?: {
    repositoryResponse?: any;
    repositoryError?: HttpErrorResponse | Error;
  }) => {
    const repositorySpy = jasmine.createSpyObj('MerchantContactsRepository', [
      'getInfo',
    ]);
    const loaderSpy = jasmine.createSpyObj('LoaderService', ['show', 'hide']);
    const errorHandlerSpy = jasmine.createSpyObj('UseCaseErrorHandler', [
      'handle',
    ]);

    if (args?.repositoryResponse) {
      repositorySpy.getInfo.and.returnValue(of(args.repositoryResponse));
    } else if (args?.repositoryError) {
      repositorySpy.getInfo.and.returnValue(
        throwError(() => args.repositoryError)
      );
    } else {
      repositorySpy.getInfo.and.returnValue(of([]));
    }

    loaderSpy.show.and.returnValue('loader-id');
    errorHandlerSpy.handle.and.returnValue(of([]));

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        GetContactInfoUseCase,
        { provide: MerchantContactsRepository, useValue: repositorySpy },
        { provide: LoaderService, useValue: loaderSpy },
        { provide: UseCaseErrorHandler, useValue: errorHandlerSpy },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    return {
      useCase: TestBed.inject(GetContactInfoUseCase),
      repository: repositorySpy,
      loader: loaderSpy,
      errorHandler: errorHandlerSpy,
    };
  };

  it('should be created', () => {
    const { useCase } = setup();
    expect(useCase).toBeTruthy();
  });

  it('should show and hide loader', () => {
    const { useCase, loader } = setup();
    useCase.execute().subscribe();
    expect(loader.show).toHaveBeenCalled();
    expect(loader.hide).toHaveBeenCalledWith('loader-id');
  });

  it('should return contact data when repository call is successful', done => {
    const mockContacts = [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        updateAt: '2023-01-01T00:00:00Z',
        role: 'Comercial',
        businessArea: 'Sales',
        phone: '1234567890',
        businessAreaId: 1,
      },
    ];

    const { useCase, repository } = setup({ repositoryResponse: mockContacts });

    useCase.execute().subscribe({
      next: result => {
        expect(result.length).toBe(1);
        expect(result[0].id).toBe(1);
        expect(result[0].name).toBe('John Doe');
        expect(repository.getInfo).toHaveBeenCalled();
        done();
      },
    });
  });

  it('should sort contacts by updateAt in descending order', done => {
    const mockContacts = [
      {
        id: 1,
        name: 'John',
        updateAt: '2023-01-01T00:00:00Z',
        email: '<EMAIL>',
        role: 'Soporte',
        businessArea: 'Sales',
        phone: '1234567890',
        businessAreaId: 1,
      },
      {
        id: 2,
        name: 'Jane',
        updateAt: '2023-02-01T00:00:00Z',
        email: '<EMAIL>',
        role: 'Soporte',
        businessArea: 'Sales',
        phone: '1234567890',
        businessAreaId: 1,
      },
    ];

    const { useCase } = setup({ repositoryResponse: mockContacts });

    useCase.execute().subscribe({
      next: result => {
        expect(result[0].id).toBe(2);
        expect(result[1].id).toBe(1);
        done();
      },
    });
  });

  it('should handle contacts with undefined updateAt', done => {
    const mockContacts = [
      {
        id: 1,
        name: 'John',
        updateAt: undefined,
        email: '<EMAIL>',
        role: 'Soporte',
        businessArea: 'Sales',
        phone: '1234567890',
        businessAreaId: 1,
      },
    ];

    const { useCase } = setup({ repositoryResponse: mockContacts });

    useCase.execute().subscribe({
      next: result => {
        expect(result[0].updateAt).toBeUndefined();
        done();
      },
    });
  });

  it('should return empty array for 404 errors', done => {
    const mockError = new HttpErrorResponse({
      status: 404,
      error: { error: 'Not found' },
    });
    const { useCase, errorHandler } = setup({ repositoryError: mockError });

    useCase.execute().subscribe({
      next: result => {
        expect(result).toEqual([]);
        expect(errorHandler.handle).not.toHaveBeenCalled();
        done();
      },
    });
  });

  it('should handle server errors', done => {
    const mockError = new HttpErrorResponse({
      status: 500,
      error: { error: 'Server error' },
    });
    const { useCase, errorHandler } = setup({ repositoryError: mockError });

    useCase.execute().subscribe({
      next: result => {
        expect(result).toEqual([]);
        expect(errorHandler.handle).toHaveBeenCalled();
        const error = errorHandler.handle.calls.first().args[0];
        expect((error as Error).message).toBe(
          'Error en el servidor. Por favor, intente más tarde.'
        );
        expect((error as any).code).toBe('GetContactInfoUseCase::serverError');
        done();
      },
    });
  });
});
