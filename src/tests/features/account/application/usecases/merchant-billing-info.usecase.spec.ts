import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import {
  merchantBillingDefault,
  MerchantBillingInfoUseCase,
} from '../../../../../app/features/account/application/usecases/merchant-billing-info.usecase';
import { MerchantBillingInfo } from '../../../../../app/features/account/domain/entities/merchant-billing-info';
import { MerchantBillingRepository } from '../../../../../app/features/account/domain/repositories/merchant-billing.repository';

describe('MerchantBillingInfoUsecase', () => {
  let usecase: MerchantBillingInfoUseCase;
  let billingRepository: jasmine.SpyObj<
    MerchantBillingRepository<Observable<MerchantBillingInfo>>
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantBillingRepository,
          useValue: jasmine.createSpyObj('MerchantBillingRepository', [
            'getMerchantBilling',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },

        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        MerchantBillingInfoUseCase,
      ],
    });

    usecase = TestBed.inject(MerchantBillingInfoUseCase);
    billingRepository = TestBed.inject(
      MerchantBillingRepository
    ) as jasmine.SpyObj<
      MerchantBillingRepository<Observable<MerchantBillingInfo>>
    >;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    );
  });

  it('should create an instance of MerchantBillingInfoUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(MerchantBillingInfoUseCase);
  });

  it('should execute successfully', async () => {
    const mockInfo = {
      accountNumber: '*********',
      bankName: 'Bank of America',
      rfc: '*********',
    };

    billingRepository.getMerchantBilling.and.returnValue(of(mockInfo));

    const billingInfo = await lastValueFrom(usecase.execute());

    expect(billingInfo).toEqual(mockInfo);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error and return default billing info', async () => {
    billingRepository.getMerchantBilling.and.returnValue(
      throwError(() => new Error('error test'))
    );
    usecaseErrorHandlerSpy.and.callThrough();

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual(merchantBillingDefault);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should return default billing info for empty response', async () => {
    billingRepository.getMerchantBilling.and.returnValue(of(null));

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual(merchantBillingDefault);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(0);
  });

  it('should replace empty fields with default values', async () => {
    const mockInfo = {
      accountNumber: '122212',
      bankName: '',
      rfc: '',
    };

    billingRepository.getMerchantBilling.and.returnValue(of(mockInfo));

    const billingInfo = await lastValueFrom(usecase.execute());

    expect(billingInfo).toEqual({
      accountNumber: '122212',
      bankName: 'No disponible',
      rfc: 'No disponible',
    });
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(0);
  });
});
