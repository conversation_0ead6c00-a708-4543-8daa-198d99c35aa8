import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, Observable, of } from 'rxjs';
import { IChangePasswordDto } from '../../../../../app/features/account/application/dtos/merchant-change-password.dto';
import { MerchantChangePasswordUseCase } from '../../../../../app/features/account/application/usecases/merchant-change-password.usecase';
import { MerchantChangePasswordRepository } from '../../../../../app/features/account/domain/repositories/merchant-change-password.repository';

describe('MerchantChangePasswordUsecase', () => {
  let usecase: MerchantChangePasswordUseCase;
  let repository: jasmine.SpyObj<
    MerchantChangePasswordRepository<IChangePasswordDto, Observable<void>>
  >;
  let usecaseErrorHandler: UseCaseErrorHandler;
  let loaderService: LoaderService;
  let notifierService: NotifierService;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantChangePasswordRepository,
          useValue: jasmine.createSpyObj('MerchantChangePasswordRepository', [
            'changePassword',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        MerchantChangePasswordUseCase,
      ],
    });

    usecase = TestBed.inject(MerchantChangePasswordUseCase);
    repository = TestBed.inject(
      MerchantChangePasswordRepository
    ) as jasmine.SpyObj<
      MerchantChangePasswordRepository<IChangePasswordDto, Observable<void>>
    >;
    usecaseErrorHandler = TestBed.inject(UseCaseErrorHandler);
    loaderService = TestBed.inject(LoaderService);
    notifierService = TestBed.inject(NotifierService);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    successNotifierSpy = spyOn(notifierService, 'success').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(MerchantChangePasswordUseCase);
  });

  it('should change password successfully', async () => {
    const args: IChangePasswordDto = {
      confirmNewPassword: 'newPassword',
      oldPassword: 'oldPassword',
      newPassword: 'newPassword',
    };
    const repoSpy = repository.changePassword.and.returnValue(of(undefined));
    const usecaseErrorSpy = spyOn(
      usecaseErrorHandler,
      'handle'
    ).and.callThrough();

    const result = await lastValueFrom(usecase.execute(args));

    expect(result).toBeUndefined();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(repoSpy).toHaveBeenCalledWith(args);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(0);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error', async () => {
    const args: IChangePasswordDto = {
      confirmNewPassword: 'newPassword',
      oldPassword: 'oldPassword',
      newPassword: 'newPassword',
    };
    const repoSpy = repository.changePassword.and.throwError('error');
    const usecaseErrorSpy = spyOn(
      usecaseErrorHandler,
      'handle'
    ).and.callThrough();

    const isEmpyWithoutStream = await lastValueFrom(
      usecase.execute(args).pipe(isEmpty())
    );

    expect(isEmpyWithoutStream).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(repoSpy).toHaveBeenCalledWith(args);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error on invalid new password confirmation', async () => {
    const args: IChangePasswordDto = {
      confirmNewPassword: 'invalidPassword',
      oldPassword: 'oldPassword',
      newPassword: 'newPassword',
    };

    const repoSpy = repository.changePassword.and.callThrough();
    const usecaseErrorSpy = spyOn(
      usecaseErrorHandler,
      'handle'
    ).and.callThrough();

    const isEmpyWithoutStream = await lastValueFrom(
      usecase.execute(args).pipe(isEmpty())
    );

    expect(isEmpyWithoutStream).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should handel error on same new password and old password', async () => {
    const args: IChangePasswordDto = {
      confirmNewPassword: 'oldPassword',
      oldPassword: 'oldPassword',
      newPassword: 'oldPassword',
    };

    const repoSpy = repository.changePassword.and.callThrough();
    const usecaseErrorSpy = spyOn(
      usecaseErrorHandler,
      'handle'
    ).and.callThrough();

    const isEmpyWithoutStream = await lastValueFrom(
      usecase.execute(args).pipe(isEmpty())
    );

    expect(isEmpyWithoutStream).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error on null old password', async () => {
    const args: IChangePasswordDto = {
      confirmNewPassword: 'oldPassword',
      oldPassword: null,
      newPassword: 'newPassword',
    };

    const repoSpy = repository.changePassword.and.callThrough();
    const usecaseErrorSpy = spyOn(
      usecaseErrorHandler,
      'handle'
    ).and.callThrough();

    const isEmpyWithoutStream = await lastValueFrom(
      usecase.execute(args).pipe(isEmpty())
    );

    expect(isEmpyWithoutStream).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error on null new password', async () => {
    const args: IChangePasswordDto = {
      confirmNewPassword: null,
      oldPassword: 'oldPassword',
      newPassword: null,
    };

    const repoSpy = repository.changePassword.and.callThrough();
    const usecaseErrorSpy = spyOn(
      usecaseErrorHandler,
      'handle'
    ).and.callThrough();

    const isEmpyWithoutStream = await lastValueFrom(
      usecase.execute(args).pipe(isEmpty())
    );

    expect(isEmpyWithoutStream).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });
});
