import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { IMerchantWithoutBillingDto } from '../../../../../app/features/account/application/dtos/merchant-without-billing.dto';
import {
  MerchantWithoutBillingInfoUseCase,
  merchantWoBillingDefault,
} from '../../../../../app/features/account/application/usecases/merchant-without-billing.usecase';
import { MerchantWithoutBillingRepository } from '../../../../../app/features/account/domain/repositories/merchant-without-billing.repository';

describe('MerchantWithoutBillingUsecase', () => {
  let usecase: MerchantWithoutBillingInfoUseCase;
  let billingRepository: jasmine.SpyObj<
    MerchantWithoutBillingRepository<Observable<IMerchantWithoutBillingDto>>
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        MerchantWithoutBillingInfoUseCase,
        {
          provide: MerchantWithoutBillingRepository,
          useValue: jasmine.createSpyObj('MerchantWithoutBillingRepository', [
            'getMerchantWithoutBilling',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
      ],
    });

    usecase = TestBed.inject(MerchantWithoutBillingInfoUseCase);
    billingRepository = TestBed.inject(
      MerchantWithoutBillingRepository
    ) as jasmine.SpyObj<
      MerchantWithoutBillingRepository<Observable<IMerchantWithoutBillingDto>>
    >;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should create an instance of MerchantWithoutBillingInfoUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(MerchantWithoutBillingInfoUseCase);
  });

  it('should retrieve merchant without billing info', async () => {
    const merchantWithoutBilling = {
      name: 'Test Name',
      address: 'Test Address',
      website: 'Test Website',
      representativeRole: 'Test Role',
      representativeName: 'Test Representative',
      representativePhoneNumber: 'Test Phone Number',
      email: 'Test Email',
    };
    billingRepository.getMerchantWithoutBilling.and.returnValue(
      of(merchantWithoutBilling)
    );

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual(merchantWithoutBilling);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error and return default merchant without billing info', async () => {
    billingRepository.getMerchantWithoutBilling.and.returnValue(
      throwError(() => 'error')
    );

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual(merchantWoBillingDefault);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(1);
  });

  it('should return default merchant without billing info for empty response', async () => {
    billingRepository.getMerchantWithoutBilling.and.returnValue(of(null));

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual(merchantWoBillingDefault);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(0);
  });

  it('should replace empty fields with default values', async () => {
    const merchantWithoutBilling = {
      name: 'Test Name',
      address: '',
      website: '',
      representativeRole: '',
      representativeName: '',
      representativePhoneNumber: '',
      email: '',
    };
    billingRepository.getMerchantWithoutBilling.and.returnValue(
      of(merchantWithoutBilling)
    );

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual({
      name: 'Test Name',
      address: 'No disponible',
      website: 'No disponible',
      representativeRole: 'No disponible',
      representativeName: 'No disponible',
      representativePhoneNumber: 'No disponible',
      email: 'No disponible',
    });
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(0);
  });
});
