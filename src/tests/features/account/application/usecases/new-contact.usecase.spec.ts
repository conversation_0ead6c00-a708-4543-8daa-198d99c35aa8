import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { CreateOneContactUseCase } from '../../../../../app/features/account/application/usecases/new-contact.usecase';
import { MerchantContactsRepository } from '../../../../../app/features/account/domain/repositories/contact-info.repository';
import {
  BusinessArea,
  ContactUI,
  RetrievedContactResponse,
  fromUIToRepository,
} from '../../../../../app/features/account/application/dtos/contact.dto';
import { of, throwError } from 'rxjs';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';

describe('CreateOneContactUseCase', () => {
  let usecase: CreateOneContactUseCase;
  let repository: jasmine.SpyObj<MerchantContactsRepository>;
  let notifier: jasmine.SpyObj<NotifierService>;
  let errorHandler: jasmine.SpyObj<UseCaseErrorHandler>;

  beforeEach(() => {
    const repositorySpy = jasmine.createSpyObj('MerchantContactsRepository', [
      'createOne',
    ]);
    const loaderSpy = jasmine.createSpyObj('LoaderService', ['show', 'hide']);
    const notifierSpy = jasmine.createSpyObj('NotifierService', ['success']);
    const errorHandlerSpy = jasmine.createSpyObj('UseCaseErrorHandler', [
      'handle',
    ]);

    TestBed.configureTestingModule({
      providers: [
        CreateOneContactUseCase,
        { provide: MerchantContactsRepository, useValue: repositorySpy },
        { provide: LoaderService, useValue: loaderSpy },
        { provide: NotifierService, useValue: notifierSpy },
        { provide: UseCaseErrorHandler, useValue: errorHandlerSpy },
      ],
    });

    usecase = TestBed.inject(CreateOneContactUseCase);
    repository = TestBed.inject(
      MerchantContactsRepository
    ) as jasmine.SpyObj<MerchantContactsRepository>;
    notifier = TestBed.inject(
      NotifierService
    ) as jasmine.SpyObj<NotifierService>;
    errorHandler = TestBed.inject(
      UseCaseErrorHandler
    ) as jasmine.SpyObj<UseCaseErrorHandler>;
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should create a new contact successfully', fakeAsync(() => {
    const contactData: ContactUI = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '**********',
      role: 'Manager',
      businessArea: 'Finance' as BusinessArea,
      // merchantId: 123, // Removed as it's not part of the data passed for creation
    };
    const repositoryRequest = fromUIToRepository(contactData);
    const mockResponse: RetrievedContactResponse = {
      id: 123,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '**********',
      role: 'Manager',
      businessArea: 'Finance',
      merchantId: 123,
      businessAreaId: 1,
    };

    repository.createOne.and.returnValue(of(mockResponse));

    let result: RetrievedContactResponse | undefined;
    usecase.execute(contactData).subscribe(response => {
      result = response;
    });

    tick();

    expect(result).toEqual(mockResponse);
    expect(repository.createOne).toHaveBeenCalledWith(repositoryRequest);
    expect(notifier.success).toHaveBeenCalledWith({
      title: 'Contacto creado exitosamente',
    });
  }));

  it('should handle repository errors', fakeAsync(() => {
    const contactData: ContactUI = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '**********',
      role: 'Manager',
      businessArea: 'Finance' as BusinessArea,
      // merchantId: 123, // Removed as it's not part of the data passed for creation
    };
    const error = new Error('Repository error');
    repository.createOne.and.returnValue(throwError(() => error));

    const handledError = new Error('Handled error');
    errorHandler.handle.and.returnValue(throwError(() => handledError));

    let errorResult: any;
    usecase.execute(contactData).subscribe({
      next: () => fail('Should have failed with the repository error'),
      error: err => (errorResult = err),
    });

    tick();

    expect(errorHandler.handle).toHaveBeenCalled();
    expect(errorResult).toBeDefined();
  }));

  it('should handle exceptions during execution', fakeAsync(() => {
    const contactData: ContactUI = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '**********',
      role: 'Manager',
      businessArea: 'Finance' as BusinessArea,
      // merchantId: 123, // Removed as it's not part of the data passed for creation
    };
    const error = new Error('Execution error');
    repository.createOne.and.throwError(error);

    const handledError = new Error('Handled error');
    errorHandler.handle.and.returnValue(throwError(() => handledError));

    let errorResult: any;
    usecase.execute(contactData).subscribe({
      next: () => fail('Should have failed with execution error'),
      error: err => (errorResult = err),
    });

    tick();

    expect(errorHandler.handle).toHaveBeenCalled();
    expect(errorResult).toBeDefined();
  }));
});
