import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';
import {
  NotifierService,
  UseCaseErrorHandler,
  LoaderService,
} from '@aplazo/merchant/shared';
import {
  ContactUI,
  BUSINESS_AREA,
  RetrievedContactResponse,
} from 'src/app/features/account/application/dtos/contact.dto';
import { UpdateOneContactUseCase } from 'src/app/features/account/application/usecases/update-contact.usecase';
import { MerchantContactsRepository } from 'src/app/features/account/domain/repositories/contact-info.repository';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';

describe('UpdateContactUsecase', () => {
  let usecase: UpdateOneContactUseCase;
  let contactsRepository: jasmine.SpyObj<MerchantContactsRepository>;
  let notifierService: jasmine.SpyObj<NotifierService>;
  let errorHandler: jasmine.SpyObj<UseCaseErrorHandler>;
  let loaderService: jasmine.SpyObj<LoaderService>;

  const mockContactUI: ContactUI = {
    id: 123,
    email: '<EMAIL>',
    name: 'John Doe',
    role: 'Manager',
    phone: '**********',
    businessArea: 'Finance',
    updateAt: '2023-01-01T00:00:00Z',
  };

  const mockResponse: RetrievedContactResponse = {
    id: 123,
    email: '<EMAIL>',
    name: 'John Doe',
    role: 'Manager',
    businessArea: 'Finance',
    phone: '**********',
    businessAreaId: BUSINESS_AREA.Finance,
    createdAt: '2022-01-01T00:00:00Z',
    updateAt: '2023-01-01T00:00:00Z',
  };

  beforeEach(() => {
    const contactsRepoSpy = jasmine.createSpyObj('MerchantContactsRepository', [
      'updateOne',
    ]);
    const notifierSpy = jasmine.createSpyObj('NotifierService', ['success']);
    const errorHandlerSpy = jasmine.createSpyObj('UseCaseErrorHandler', [
      'handle',
    ]);
    const loaderSpy = jasmine.createSpyObj('LoaderService', ['show', 'hide']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        UpdateOneContactUseCase,
        { provide: MerchantContactsRepository, useValue: contactsRepoSpy },
        { provide: NotifierService, useValue: notifierSpy },
        { provide: UseCaseErrorHandler, useValue: errorHandlerSpy },
        { provide: LoaderService, useValue: loaderSpy },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    usecase = TestBed.inject(UpdateOneContactUseCase);
    contactsRepository = TestBed.inject(
      MerchantContactsRepository
    ) as jasmine.SpyObj<MerchantContactsRepository>;
    notifierService = TestBed.inject(
      NotifierService
    ) as jasmine.SpyObj<NotifierService>;
    errorHandler = TestBed.inject(
      UseCaseErrorHandler
    ) as jasmine.SpyObj<UseCaseErrorHandler>;
    loaderService = TestBed.inject(
      LoaderService
    ) as jasmine.SpyObj<LoaderService>;
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  describe('execute', () => {
    it('should update contact info successfully', fakeAsync(() => {
      loaderService.show.and.returnValue('loader-id');
      contactsRepository.updateOne.and.returnValue(of(mockResponse));

      let result: RetrievedContactResponse | undefined;
      let error: any;

      usecase.execute(mockContactUI).subscribe({
        next: res => (result = res),
        error: err => (error = err),
      });

      tick(); // Simulate passage of time until all async tasks complete

      expect(error).toBeUndefined();
      expect(result).toEqual(mockResponse);
      expect(contactsRepository.updateOne).toHaveBeenCalled();
      expect(notifierService.success).toHaveBeenCalledWith({
        title: 'Contacto actualizado exitosamente',
      });
    }));

    it('should handle errors when updating contact info fails', fakeAsync(() => {
      const errorMessage = 'Update failed';
      loaderService.show.and.returnValue('loader-id');
      contactsRepository.updateOne.and.returnValue(
        throwError(() => new Error(errorMessage))
      );
      errorHandler.handle.and.returnValue(
        throwError(() => new Error(errorMessage))
      );

      let result: RetrievedContactResponse | undefined;
      let error: any;

      usecase.execute(mockContactUI).subscribe({
        next: res => (result = res),
        error: err => (error = err),
      });

      tick(); // Simulate passage of time until all async tasks complete

      expect(result).toBeUndefined();
      expect(error).toBeTruthy();
      expect(contactsRepository.updateOne).toHaveBeenCalled();
      expect(errorHandler.handle).toHaveBeenCalled();
    }));

    it('should validate business area before updating', fakeAsync(() => {
      const invalidContactInfo = {
        ...mockContactUI,
        businessArea: 'InvalidArea' as any,
      };

      errorHandler.handle.and.returnValue(
        throwError(() => new Error('El área de negocio no es válida'))
      );

      let error: any;

      usecase.execute(invalidContactInfo).subscribe({
        next: () => fail('Should have failed'),
        error: err => (error = err),
      });

      tick();

      expect(error).toBeTruthy();
      expect(error.message).toContain('El área de negocio no es válida');
      expect(contactsRepository.updateOne).not.toHaveBeenCalled();
    }));

    it('should validate contact ID before updating', fakeAsync(() => {
      const invalidContactInfo = {
        ...mockContactUI,
        id: undefined,
      };

      errorHandler.handle.and.returnValue(
        throwError(
          () => new Error('El id del contacto debe ser un número entero válido')
        )
      );

      let error: any;

      usecase.execute(invalidContactInfo).subscribe({
        next: () => fail('Should have failed'),
        error: err => (error = err),
      });

      tick();

      expect(error).toBeTruthy();
      expect(error.message).toContain(
        'El id del contacto debe ser un número entero válido'
      );
      expect(contactsRepository.updateOne).not.toHaveBeenCalled();
    }));
  });
});
