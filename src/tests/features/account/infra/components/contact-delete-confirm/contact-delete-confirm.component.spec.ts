import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { DialogRef } from '@ngneat/dialog';

import { ContactDeleteConfirmComponent } from 'src/app/features/account/infra/components/contact-delete-confirm/contact-delete-confirm.component';

describe('ContactDeleteConfirmComponent', () => {
  let component: ContactDeleteConfirmComponent;
  let fixture: ComponentFixture<ContactDeleteConfirmComponent>;
  let dialogRefMock: jasmine.SpyObj<DialogRef<any, boolean>>;
  let notifierMock: jasmine.SpyObj<NotifierService>;

  beforeEach(() => {
    dialogRefMock = jasmine.createSpyObj('DialogRef', ['close'], {
      data: {
        contactName: 'Test User',
        contactId: 123,
      },
    });

    notifierMock = jasmine.createSpyObj('NotifierService', ['info']);

    TestBed.configureTestingModule({
      imports: [
        ContactDeleteConfirmComponent,
        AplazoCardComponent,
        AplazoButtonComponent,
      ],
      providers: [
        { provide: DialogRef, useValue: dialogRefMock },
        { provide: NotifierService, useValue: notifierMock },
      ],
    });

    fixture = TestBed.createComponent(ContactDeleteConfirmComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close with true when confirm button is clicked', () => {
    const confirmButton = fixture.debugElement.queryAll(By.css('button'))[1];
    confirmButton.triggerEventHandler('click', null);

    expect(dialogRefMock.close).toHaveBeenCalledWith(true);
  });

  it('should show cancel notification and close with false when cancel button is clicked', () => {
    const cancelButton = fixture.debugElement.queryAll(By.css('button'))[0];
    cancelButton.triggerEventHandler('click', null);

    expect(notifierMock.info).toHaveBeenCalledWith({
      title: 'Eliminación cancelada',
    });
    expect(dialogRefMock.close).toHaveBeenCalledWith(false);
  });
});
