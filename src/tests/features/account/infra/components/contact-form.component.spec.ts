import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ContactFormComponent } from '../../../../../app/features/account/infra/components/contact-form/contact-form.component';
import { DialogRef } from '@ngneat/dialog';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { MerchantStoreService } from '../../../../../app/features/account/infra/services/merchant-store.service';

describe('ContactFormComponent', () => {
  let component: ContactFormComponent;
  let fixture: ComponentFixture<ContactFormComponent>;
  let dialogRefMock: jasmine.SpyObj<DialogRef<any, any>>;
  let merchantStoreMock: jasmine.SpyObj<MerchantStoreService>;

  const setup = (args?: {
    dialogData?: {
      id?: number | null;
      name?: string;
      email?: string;
      phone?: string;
      role?: string;
      businessArea?: string;
      existingRoles?: string[];
    };
  }) => {
    const defaultData = {
      id: null,
      name: 'Test Name',
      email: '<EMAIL>',
      phone: '**********',
      role: 'Commercial',
      businessArea: 'Commercial',
      existingRoles: ['IT', 'Finance'],
    };

    const config = {
      dialogData: {
        ...defaultData,
        ...args?.dialogData,
      },
    };

    dialogRefMock = jasmine.createSpyObj('DialogRef', ['close']);
    Object.defineProperty(dialogRefMock, 'data', {
      get: () => config.dialogData,
    });

    merchantStoreMock = jasmine.createSpyObj('MerchantStoreService', [
      'getExistingRoles',
    ]);
    merchantStoreMock.getExistingRoles.and.returnValue(['IT', 'Finance']);

    TestBed.configureTestingModule({
      imports: [
        ContactFormComponent,
        ReactiveFormsModule,
        AplazoCardComponent,
        AplazoButtonComponent,
        AplazoFormFieldDirectives,
      ],
      providers: [
        { provide: DialogRef, useValue: dialogRefMock },
        { provide: MerchantStoreService, useValue: merchantStoreMock },
      ],
    }).overrideComponent(ContactFormComponent, {
      remove: {
        imports: [
          AplazoTrimSpacesDirective,
          AplazoTruncateLengthDirective,
          OnlyNumbersDirective,
        ],
      },
    });

    fixture = TestBed.createComponent(ContactFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    return {
      component,
      fixture,
      dialogRefMock,
      merchantStoreMock,
    };
  };

  it('should create', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should initialize form with data from dialog', () => {
    const { component } = setup();
    expect(component.form).toBeDefined();
    expect(component.name.value).toBe('Test Name');
    expect(component.email.value).toBe('<EMAIL>');
    expect(component.phone.value).toBe('**********');
    expect(component.businessArea.value).toBe('Commercial');
    expect(component.role.value).toBe('Commercial');
    expect(component.id.value).toBeNull();
    expect(component.id.disabled).toBeTrue();
  });

  it('should validate required fields', () => {
    const { component } = setup();
    component.name.setValue('');
    component.email.setValue('');
    component.businessArea.setValue(null as any);
    component.role.setValue('');

    expect(component.name.valid).toBeFalsy();
    expect(component.email.valid).toBeFalsy();
    expect(component.businessArea.valid).toBeFalsy();
    expect(component.role.valid).toBeFalsy();
  });

  it('should validate email format', () => {
    const { component } = setup();
    component.email.setValue('invalid-email');
    expect(component.email.valid).toBeFalsy();

    component.email.setValue('<EMAIL>');
    expect(component.email.valid).toBeTruthy();
  });

  it('should close dialog without confirmation', () => {
    const { component, dialogRefMock } = setup();
    component.close();
    expect(dialogRefMock.close).toHaveBeenCalledWith({
      hasConfirmation: false,
    });
  });

  it('should not submit when form is invalid', () => {
    const { component, dialogRefMock } = setup();
    component.name.setValue('');
    component.submit();

    expect(component.form.valid).toBeFalsy();
    expect(dialogRefMock.close).not.toHaveBeenCalled();
  });

  it('should submit form with valid data', () => {
    const { component, dialogRefMock } = setup();
    component.form.markAsTouched();
    component.submit();

    expect(dialogRefMock.close).toHaveBeenCalledWith(
      jasmine.objectContaining({
        hasConfirmation: true,
        role: 'Commercial',
        email: '<EMAIL>',
        name: 'Test Name',
        phone: '**********',
        businessArea: 'Commercial',
      })
    );
  });

  it('should filter roles for new contact', () => {
    const { component } = setup();
    Object.defineProperty(component, 'isEditMode', { value: false });
    component.ngOnInit();

    expect(component.filteredRoleOptions.length).toBeLessThan(
      component.roleOptions.length
    );
    expect(
      component.filteredRoleOptions.some(role => role.value === 'IT')
    ).toBeFalsy();
    expect(
      component.filteredRoleOptions.some(role => role.value === 'Finance')
    ).toBeFalsy();
  });

  it('should include current role when editing', () => {
    const { component } = setup({
      dialogData: {
        id: 1,
        role: 'IT',
        businessArea: 'IT',
      },
    });

    expect(component.isEditMode).toBeTrue();
    expect(
      component.filteredRoleOptions.some(role => role.value === 'IT')
    ).toBeTrue();
  });

  it('should clear duplicate role error on role change', () => {
    const { component } = setup();
    component.duplicateRoleError = 'Some error';
    component.role.setValue('Commercial');
    expect(component.duplicateRoleError).toBeNull();
  });

  it('should handle duplicate role for new contact', () => {
    const { component, dialogRefMock } = setup();
    Object.defineProperty(component, 'isEditMode', { value: false });
    component.ngOnInit();
    component.role.setValue('IT');
    component.form.markAllAsTouched();
    component.submit();

    expect(component.duplicateRoleError).toBeTruthy();
    expect(dialogRefMock.close).not.toHaveBeenCalled();
  });
});
