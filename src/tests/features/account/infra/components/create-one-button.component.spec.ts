import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CreateOneButtonComponent } from '../../../../../app/features/account/infra/components/create-one-button.component';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';

describe('CreateOneButtonComponent', () => {
  let component: CreateOneButtonComponent;
  let fixture: ComponentFixture<CreateOneButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CreateOneButtonComponent,
        NoopAnimationsModule,
        AplazoButtonComponent,
        AplazoIconComponent,
      ],
      providers: [AplazoIconRegistryService],
    }).compileComponents();

    fixture = TestBed.createComponent(CreateOneButtonComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('title', 'Test Title');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display the required title', () => {
    const titleElement = fixture.debugElement.query(By.css('p'));
    expect(titleElement).toBeTruthy();
    expect(titleElement.nativeElement.textContent.trim()).toContain(
      'Test Title'
    );
  });

  it('should display the default button label when buttonLabel input is not provided', () => {
    fixture.detectChanges();

    const buttonElement = fixture.debugElement.query(By.css('button'));
    const buttonLabelSpan = buttonElement.query(By.css('span.ml-2'));
    expect(buttonLabelSpan).toBeTruthy();
    expect(buttonLabelSpan.nativeElement.textContent.trim()).toBe(
      'Agregar Nuevo Contacto'
    );
  });

  it('should display the provided button label', () => {
    const customLabel = 'Create New Item';
    fixture.componentRef.setInput('buttonLabel', customLabel);
    fixture.detectChanges();

    const buttonElement = fixture.debugElement.query(By.css('button'));
    const buttonLabelSpan = buttonElement.query(By.css('span.ml-2'));
    expect(buttonLabelSpan).toBeTruthy();
    expect(buttonLabelSpan.nativeElement.textContent.trim()).toBe(customLabel);
  });

  it('should emit createOne event when the button is clicked', () => {
    const createOneSpy = spyOn(component.createOne, 'emit');

    const buttonElement = fixture.debugElement.query(By.css('button'));
    buttonElement.triggerEventHandler('click', null);
    fixture.detectChanges();

    expect(createOneSpy).toHaveBeenCalledTimes(1);
  });
});
