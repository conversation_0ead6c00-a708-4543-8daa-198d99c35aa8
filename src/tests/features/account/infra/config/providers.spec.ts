import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Observable } from 'rxjs';
import { provideCompanyRepositories } from 'src/app/features/account/infra/config/providers';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { IChangePasswordDto } from '../../../../../app/features/account/application/dtos/merchant-change-password.dto';
import { IMerchantWithoutBillingDto } from '../../../../../app/features/account/application/dtos/merchant-without-billing.dto';
import { MerchantBillingInfo } from '../../../../../app/features/account/domain/entities/merchant-billing-info';
import { MerchantBillingRepository } from '../../../../../app/features/account/domain/repositories/merchant-billing.repository';
import { MerchantChangePasswordRepository } from '../../../../../app/features/account/domain/repositories/merchant-change-password.repository';
import { MerchantWithoutBillingRepository } from '../../../../../app/features/account/domain/repositories/merchant-without-billing.repository';
import { MerchantBillingInfoRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-billing-info-impl.repository';
import { MerchantChangePasswordRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-change-password-impl.repository';
import { MerchantWithoutBillingRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-without-billing-repository.impl';

describe('account providers', () => {
  let billingService: MerchantBillingRepository<
    Observable<MerchantBillingInfo>
  >;
  let changePasswordService: MerchantChangePasswordRepository<
    IChangePasswordDto,
    Observable<void>
  >;
  let withoutBillingService: MerchantWithoutBillingRepository<
    Observable<IMerchantWithoutBillingDto>
  >;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideCompanyRepositories(),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'http://localhost:3000/',
          },
        },
      ],
    });

    billingService = TestBed.inject(MerchantBillingRepository);
    changePasswordService = TestBed.inject(MerchantChangePasswordRepository);
    withoutBillingService = TestBed.inject(MerchantWithoutBillingRepository);
  });

  it('should return an instance of MerchantBillingRepository', () => {
    expect(billingService).toBeInstanceOf(MerchantBillingInfoRepositoryImpl);
  });

  it('should return an instance of MerchantChangePasswordRepository', () => {
    expect(changePasswordService).toBeInstanceOf(
      MerchantChangePasswordRepositoryImpl
    );
  });

  it('should return an instance of MerchantWithoutBillingRepository', () => {
    expect(withoutBillingService).toBeInstanceOf(
      MerchantWithoutBillingRepositoryImpl
    );
  });
});
