import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideRouter, Router, RouterOutlet } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoTabGroupComponent,
  AplazoTabsComponents,
} from '@aplazo/shared-ui/tabs';
import { of } from 'rxjs';
import {
  AccountComponent,
  accountTablabels,
} from '../../../../../app/features/account/infra/layout/account.component';
import { UserStoreService } from '../../../../../app/features/user/src/application/services/user-store.service';

@Component({
  standalone: true,
  selector: 'app-test',
  template: `<h1>test</h1>`,
})
class TestComponent {}

const setup = async (userMock: unknown) => {
  TestBed.configureTestingModule({
    imports: [
      AplazoTabsComponents,
      AplazoCardComponent,
      NgIf,
      AsyncPipe,
      RouterOutlet,
      AccountComponent,
    ],
    providers: [
      {
        provide: UserStoreService,
        useValue: userMock,
      },
      provideRouter([
        {
          path: '',
          component: AccountComponent,
          children: [
            {
              path: '',
              redirectTo: accountTablabels['DATOS DEL COMERCIO'],
              pathMatch: 'full',
            },
            {
              path: accountTablabels['DATOS DEL COMERCIO'],
              component: TestComponent,
            },
            {
              path: accountTablabels['DATOS BANCARIOS'],
              component: TestComponent,
            },
            {
              path: accountTablabels['CAMBIAR CONTRASEÑA'],
              component: TestComponent,
            },
            {
              path: accountTablabels['DATOS DE CONTACTOS'],
              component: TestComponent,
            },
          ],
        },
      ]),
    ],
  });

  const routerHarness = await RouterTestingHarness.create();
  const component = await routerHarness.navigateByUrl('/', AccountComponent);
  const router = TestBed.inject(Router);

  return { component, routerHarness, router };
};

describe('AccountComponent', () => {
  it('should be created', async () => {
    const userMock = { role$: of('ROLE_MERCHANT') };

    const { component } = await setup(userMock);

    expect(component).toBeTruthy();
  });

  it('should have 3 tabs for ROLE_MERCHANT (no access to DATOS DE CONTACTOS)', async () => {
    const userMock = { role$: of('ROLE_MERCHANT') };

    const { routerHarness } = await setup(userMock);

    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    expect(tabs.length).toBe(3);
  });

  it('should have 3 tabs for other roles (no access to CAMBIAR CONTRASEÑA)', async () => {
    const userMock = { role$: of('ROLE_ANOTHER') };

    const { routerHarness } = await setup(userMock);

    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    expect(tabs.length).toBe(3);
  });

  it('should have 4 tabs for admin roles', async () => {
    const userMock = { role$: of('ROLE_PANEL_ADMIN') };

    const { routerHarness } = await setup(userMock);

    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    expect(tabs.length).toBe(4);
  });

  it('should have 1 tab active only', async () => {
    const userMock = { role$: of('ROLE_MERCHANT') };

    const { routerHarness } = await setup(userMock);

    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    const activeTabs = tabs
      .flatMap(tab =>
        tab.nativeElement.classList
          .values()
          .some(val => val === 'aplazo-tab--active')
      )
      .reduce((acc, val) => {
        if (val) {
          acc += 1;
        }
        return acc;
      }, 0);

    expect(activeTabs).toBe(1);
  });

  it('should navigate when tab is clicked', fakeAsync(async () => {
    const userMock = { role$: of('ROLE_MERCHANT') };

    const { routerHarness, router } = await setup(userMock);

    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    expect(router.url).toContain(accountTablabels['DATOS DEL COMERCIO']);

    const tab = tabs[1];
    tab.nativeElement.click();

    routerHarness.detectChanges();
    tick();

    expect(router.url).toContain(accountTablabels['DATOS BANCARIOS']);
  }));
});
