/* eslint-disable @typescript-eslint/no-unused-vars */
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { I18NService, I18NTranslator } from '@aplazo/i18n';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { of } from 'rxjs';
import { MerchantBillingInfoUseCase } from '../../../../../app/features/account/application/usecases/merchant-billing-info.usecase';
import { MerchantBillingRepository } from '../../../../../app/features/account/domain/repositories/merchant-billing.repository';
import {
  BankingInfoComponent,
  IMerchantBillingInfoInjectedTextUI,
} from '../../../../../app/features/account/infra/pages/banking-info.component';

describe('BankingInfoComponent', () => {
  let fixture: ComponentFixture<BankingInfoComponent>;
  let component: BankingInfoComponent;
  let usecase: MerchantBillingInfoUseCase;
  let repository: MerchantBillingRepository<any>;
  let i18n: I18NTranslator;

  const textUIMock: IMerchantBillingInfoInjectedTextUI = {
    rfc: {
      sectionTitle: 'RFC',
      rfcLabel: 'RFC',
      rfcValueDefault: 'RFC',
    },
    bankAccount: {
      sectionTitle: 'Bank Account',
      bankNameLabel: 'Bank Name',
      bankNameValueDefault: 'Bank Name',
      clabeLabel: 'CLABE',
      clabeValueDefault: 'CLABE',
    },
    message: {
      content: 'Message',
      supportEmail: '<EMAIL>',
      whatsappURL: 'https://wa.me/**********',
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AplazoIconComponent, BankingInfoComponent],
      providers: [
        AplazoIconRegistryService,
        {
          provide: MerchantBillingRepository,
          useValue: {
            getMerchantBilling: () =>
              of({
                rfc: 'RFC',
                bankName: 'Bank Name',
                accountNumber: 'CLABE',
              }),
          },
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: I18NService,
          useValue: {
            getTranslateObjectByKey: () => of(textUIMock),
          },
        },
        MerchantBillingInfoUseCase,
      ],
    });

    fixture = TestBed.createComponent(BankingInfoComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    i18n = TestBed.inject(I18NService);
    usecase = TestBed.inject(MerchantBillingInfoUseCase);
    repository = TestBed.inject(MerchantBillingRepository);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should have readonly inputs with the correct values', () => {
    const paragraphs = fixture.debugElement.queryAll(By.css('p'));
    const [rfc, bankName, clabe] = paragraphs;

    expect(paragraphs.length)
      .withContext('3 text dynamic paragraphs to show billing info')
      .toBe(3);
    expect(rfc?.nativeElement?.textContent?.trim()).toBe('RFC');
    expect(bankName?.nativeElement?.textContent?.trim()).toBe('Bank Name');
    expect(clabe?.nativeElement?.textContent?.trim()).toBe('CLABE');
  });
});
