import { Async<PERSON><PERSON><PERSON>, NgClass, NgIf } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoPasswordControlComponent } from '@aplazo/shared-ui/merchant';
import { TranslocoService } from '@jsverse/transloco';
import { of } from 'rxjs';
import { MerchantChangePasswordUseCase } from '../../../../../app/features/account/application/usecases/merchant-change-password.usecase';
import { MerchantChangePasswordRepository } from '../../../../../app/features/account/domain/repositories/merchant-change-password.repository';
import { ChangePasswordComponent } from '../../../../../app/features/account/infra/pages/change-password.component';

describe('ChangePasswordComponent', () => {
  let fixture: ComponentFixture<ChangePasswordComponent>;
  let component: ChangePasswordComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        NgClass,
        AsyncPipe,
        ReactiveFormsModule,
        AplazoPasswordControlComponent,
        AplazoButtonComponent,
        ChangePasswordComponent,
      ],
      providers: [
        MerchantChangePasswordUseCase,
        {
          provide: MerchantChangePasswordRepository,
          useValue: {
            changePassword: () => {
              void 0;
            },
          },
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: TranslocoService,
          useValue: {
            selectTranslateObject: (key: string) => {
              const obj = {
                errors: {
                  required: 'required',
                  maxLength: 'maxLength',
                  pattern: 'pattern',
                  comparePassword: 'comparePassword',
                },
                form: {
                  oldPassword: {
                    label: 'oldPassword',
                    placeholder: 'oldPassword',
                  },
                  newPassword: {
                    label: 'newPassword',
                    placeholder: 'newPassword',
                  },
                  confirmPassword: {
                    label: 'confirmPassword',
                    placeholder: 'confirmPassword',
                    minLenghtDescription: 'minLengthDescription',
                    digitDescription: 'digitDescription',
                    uppercaseDescription: 'uppercaseDescription',
                  },
                  submitButton: 'submitButton',
                },
              };

              return of(obj[key]);
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(ChangePasswordComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should have 3 aplazo-password-control', () => {
    const controls = fixture.debugElement.queryAll(
      By.directive(AplazoPasswordControlComponent)
    );

    expect(controls.length)
      .withContext(
        'inner change password should have 3 aplazo-password-control'
      )
      .toBe(3);
  });

  it('should start errorDescriptor$ with all values as false', done => {
    let allValuesAreFalse = true;

    component.errorDescriptor$.subscribe(desc => {
      allValuesAreFalse = Object.values(desc).every(value => !value);
    });

    expect(allValuesAreFalse)
      .withContext(
        'change password errorDescriptor$ should start with all values as false'
      )
      .toBeTrue();

    done();
  });

  it('should change errorDescriptor$ values', done => {
    let descriptor = null;

    component.errorDescriptor$.subscribe(desc => {
      descriptor = desc;
    });

    component.confirmPassword.setValue('12345678');

    expect(descriptor)
      .withContext('change password errorDescriptor$ should change values')
      .toEqual({
        hasMinLength: true,
        hasDigit: true,
        hasUppercase: false,
      });

    done();
  });

  it('should reflect changes in hasError$', done => {
    let result;

    component.hasError$.subscribe(value => {
      result = value;
    });

    expect(result)
      .withContext('change password hasError$ should start with true value')
      .toBeTrue();

    component.oldPassword.setValue('Abcd1234');
    component.newPassword.setValue('Abcd12345');
    component.confirmPassword.setValue('Abcd12345');

    expect(result)
      .withContext('change password hasError$ should change value')
      .toBeFalse();

    done();
  });

  it('should remain hasError$ true when form is invalid', done => {
    let result;

    component.hasError$.subscribe(value => {
      result = value;
    });

    component.newPassword.setValue('Abcd12345');
    component.confirmPassword.setValue('Abcd1234');

    expect(result)
      .withContext(
        'change password hasError$ should remain true when form is invalid'
      )
      .toBeTrue();

    done();
  });

  it('should remain hasError$ true when confirmPassword has not digit', done => {
    let result;

    component.hasError$.subscribe(value => {
      result = value;
    });

    component.oldPassword.setValue('Abcd1234');
    component.newPassword.setValue('Abcdedfgh');
    component.confirmPassword.setValue('Abcdedfgh');

    expect(result)
      .withContext(
        'change password hasError$ should remain true when confirmPassword has not digit'
      )
      .toBeTrue();

    done();
  });

  it('should remain hasError$ true when confirmPassword has not uppercase', done => {
    let result;

    component.hasError$.subscribe(value => {
      result = value;
    });

    component.oldPassword.setValue('Abcd1234');
    component.newPassword.setValue('abcd1234');
    component.confirmPassword.setValue('abcd1234');

    expect(result)
      .withContext(
        'change password hasError$ should remain true when confirmPassword has not uppercase'
      )
      .toBeTrue();

    done();
  });

  it('should has submit button disabled by default', () => {
    const form = fixture.debugElement.query(By.css('form'));

    const button = form.query(By.css('button:last-child'));

    expect(button.nativeElement.disabled)
      .withContext(
        'change password submit button should be disabled by default'
      )
      .toBeTrue();
  });

  it('should has submit button enabled when form is valid and has not errors', () => {
    const form = fixture.debugElement.query(By.css('form'));

    const button = form.query(By.css('button:last-child'));

    component.oldPassword.setValue('Abcd1234');
    component.newPassword.setValue('Abcd12345');
    component.confirmPassword.setValue('Abcd12345');

    fixture.detectChanges();

    expect(button.nativeElement.disabled)
      .withContext(
        'change password submit button should be enabled when form is valid and has not errors'
      )
      .toBeFalse();
  });

  it('should call changePassword method when form is submitted', () => {
    const form = fixture.debugElement.query(By.css('form'));

    const button = form.query(By.css('button:last-child'));

    spyOn(component, 'changePassword').and.callThrough();

    component.oldPassword.setValue('Abcd1234');
    component.newPassword.setValue('Abcd12345');
    component.confirmPassword.setValue('Abcd12345');

    fixture.detectChanges();

    expect(button.nativeElement.disabled).toBeFalse();

    button.nativeElement.click();

    expect(component.changePassword)
      .withContext(
        'change password method should be called when form is submitted'
      )
      .toHaveBeenCalledTimes(1);
  });
});
