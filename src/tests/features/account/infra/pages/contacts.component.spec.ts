import { Async<PERSON>ipe } from '@angular/common';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { of } from 'rxjs';
import { ContactUI } from 'src/app/features/account/application/dtos/contact.dto';
import { GetContactInfoUseCase } from 'src/app/features/account/application/usecases/get-contact.usecase';
import { CreateOneButtonComponent } from 'src/app/features/account/infra/components/create-one-button.component';
import { ContactsComponent } from 'src/app/features/account/infra/pages/contacts/contacts.component';
import { CreateContactWithDialogFormService } from 'src/app/features/account/infra/services/create-contact-with-dialog.service';
import { UpdateContactWithDialogFormService } from 'src/app/features/account/infra/services/edit-contact-with-dialog-form.service';
import { MerchantStoreService } from 'src/app/features/account/infra/services/merchant-store.service';
import { UserStoreService } from 'src/app/features/user/src/application/services/user-store.service';
import { DeleteContactWithDialogService } from 'src/app/features/account/infra/services/delete-contact-with-dialog.service';

describe('ContactsComponent', () => {
  let fixture: ComponentFixture<ContactsComponent>;
  let component: ContactsComponent;
  let getContactsUseCase: jasmine.SpyObj<GetContactInfoUseCase>;
  let updateContactService: jasmine.SpyObj<UpdateContactWithDialogFormService>;
  let createContactService: jasmine.SpyObj<CreateContactWithDialogFormService>;
  let merchantStore: jasmine.SpyObj<MerchantStoreService>;
  let deleteContactService: jasmine.SpyObj<DeleteContactWithDialogService>;

  const mockContacts: ContactUI[] = [
    {
      id: 1,
      email: '<EMAIL>',
      name: 'Juan Marketing',
      phone: '**********',
      role: 'Mkt',
      businessArea: 'Marketing',
    },
    {
      id: 2,
      email: '<EMAIL>',
      name: 'Ana Soporte',
      phone: '**********',
      role: 'Support',
      businessArea: 'Support',
    },
  ];

  const mockContactsResponse = {
    data: mockContacts,
  };

  const emptyContactsResponse = {
    data: [],
  };

  beforeEach(() => {
    const getContactsUseCaseSpy = jasmine.createSpyObj(
      'GetContactInfoUseCase',
      ['execute']
    );
    const updateContactServiceSpy = jasmine.createSpyObj(
      'UpdateContactWithDialogFormService',
      ['execute']
    );
    const createContactServiceSpy = jasmine.createSpyObj(
      'CreateContactWithDialogFormService',
      ['execute']
    );
    const deleteContactServiceSpy = jasmine.createSpyObj(
      'DeleteContactWithDialogService',
      ['execute']
    );
    const merchantStoreSpy = jasmine.createSpyObj('MerchantStoreService', [
      'setMerchantContacts',
      'getAvailableRoles',
      'getExistingRoles',
    ]);

    merchantStoreSpy.merchantContacts$ = of(mockContactsResponse);
    merchantStoreSpy.getAvailableRoles.and.returnValue([
      'IT',
      'Commercial',
      'Finance',
    ]);
    merchantStoreSpy.getExistingRoles.and.returnValue(['Mkt', 'Support']);

    updateContactServiceSpy.execute.and.returnValue(Promise.resolve());
    createContactServiceSpy.execute.and.returnValue(Promise.resolve());
    deleteContactServiceSpy.execute.and.returnValue(Promise.resolve());

    TestBed.configureTestingModule({
      imports: [
        ContactsComponent,
        AplazoCardComponent,
        AplazoSimpleTableComponents,
        AplazoCommonMessageComponent,
        AsyncPipe,
        CreateOneButtonComponent,
      ],
      providers: [
        {
          provide: GetContactInfoUseCase,
          useValue: getContactsUseCaseSpy,
        },
        {
          provide: UpdateContactWithDialogFormService,
          useValue: updateContactServiceSpy,
        },
        {
          provide: CreateContactWithDialogFormService,
          useValue: createContactServiceSpy,
        },
        {
          provide: DeleteContactWithDialogService,
          useValue: deleteContactServiceSpy,
        },
        {
          provide: MerchantStoreService,
          useValue: merchantStoreSpy,
        },
        {
          provide: UserStoreService,
          useValue: {
            merchantId$: of(123),
            role$: of(['ROLE_ADMIN_INC']),
          },
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: DeleteContactWithDialogService,
          useValue: deleteContactServiceSpy,
        },
      ],
    });

    getContactsUseCase = TestBed.inject(
      GetContactInfoUseCase
    ) as jasmine.SpyObj<GetContactInfoUseCase>;
    updateContactService = TestBed.inject(
      UpdateContactWithDialogFormService
    ) as jasmine.SpyObj<UpdateContactWithDialogFormService>;
    createContactService = TestBed.inject(
      CreateContactWithDialogFormService
    ) as jasmine.SpyObj<CreateContactWithDialogFormService>;
    merchantStore = TestBed.inject(
      MerchantStoreService
    ) as jasmine.SpyObj<MerchantStoreService>;
    deleteContactService = TestBed.inject(
      DeleteContactWithDialogService
    ) as jasmine.SpyObj<DeleteContactWithDialogService>;

    getContactsUseCase.execute.and.returnValue(of(mockContacts));

    fixture = TestBed.createComponent(ContactsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should load contacts on initialization', () => {
    expect(getContactsUseCase.execute).toHaveBeenCalled();
    expect(merchantStore.setMerchantContacts).toHaveBeenCalledWith(
      mockContacts
    );
  });

  it('should display contacts in a table', fakeAsync(() => {
    const table = fixture.debugElement.query(By.css('table'));
    expect(table).toBeTruthy();

    fixture.detectChanges();
    tick();

    const html = fixture.nativeElement.innerHTML;

    const tableRows = fixture.nativeElement.querySelectorAll(
      'tr:not(:first-child)'
    );
    expect(tableRows.length).toBeGreaterThan(0);

    expect(html).toContain('Mkt');
    expect(html).toContain('Juan Marketing');
    expect(html).toContain('<EMAIL>');
  }));

  it('should call editContact when edit button is clicked', fakeAsync(() => {
    spyOn(component, 'editContact');

    // Mock contacts data
    merchantStore.merchantContacts$ = of(mockContactsResponse);
    fixture.detectChanges();
    tick();

    const editButtons = fixture.debugElement.queryAll(
      By.css('button[aplzColor="light"]')
    );

    expect(editButtons.length).toBeGreaterThan(0);

    if (editButtons.length > 0) {
      editButtons[0].triggerEventHandler('click', null);
      fixture.detectChanges();
      tick();

      expect(component.editContact).toHaveBeenCalledWith(mockContacts[0]);
    }
  }));

  it('should call deleteContact when delete button is clicked', fakeAsync(() => {
    spyOn(component, 'deleteContact');

    // Mock contacts data
    merchantStore.merchantContacts$ = of(mockContactsResponse);
    fixture.detectChanges();
    tick();

    const deleteButtons = fixture.debugElement.queryAll(
      By.css('button[aplzColor="danger"]')
    );

    expect(deleteButtons.length).toBeGreaterThan(0);

    if (deleteButtons.length > 0) {
      deleteButtons[0].triggerEventHandler('click', null);
      fixture.detectChanges();
      tick();

      expect(component.deleteContact).toHaveBeenCalledWith(mockContacts[0]);
    }
  }));

  it('should not reload contacts if deletion is cancelled', fakeAsync(async () => {
    deleteContactService.execute.and.returnValue(Promise.resolve(false));
    spyOn(component, 'loadContacts');

    await component.deleteContact(mockContacts[0]);
    tick();

    expect(deleteContactService.execute).toHaveBeenCalledWith(mockContacts[0]);
    expect(component.loadContacts).not.toHaveBeenCalled();
  }));

  it('should handle empty contacts correctly', fakeAsync(() => {
    merchantStore.merchantContacts$ = of(emptyContactsResponse);
    getContactsUseCase.execute.and.returnValue(of([]));

    fixture = TestBed.createComponent(ContactsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();

    const html = fixture.nativeElement.innerHTML;
    expect(html).toContain('No hay contactos registrados');
  }));

  it('should display the correct roles availability message', fakeAsync(() => {
    fixture.detectChanges();
    tick();

    const message = component.getRolesAvailabilityMessage();
    expect(message).toBe('3 roles disponibles para asignar');

    merchantStore.getAvailableRoles.and.returnValue(['IT']);
    expect(component.getRolesAvailabilityMessage()).toBe(
      '1 rol disponible para asignar'
    );

    merchantStore.getAvailableRoles.and.returnValue([]);
    expect(component.getRolesAvailabilityMessage()).toBe(
      'No hay roles disponibles para asignar'
    );
  }));

  it('should call createNewContact when button is clicked', () => {
    spyOn(component, 'createNewContact');

    component.createNewContact();

    expect(component.createNewContact).toHaveBeenCalled();
  });

  it('should execute createContact service when createNewContact is called', async () => {
    await component.createNewContact();

    expect(createContactService.execute).toHaveBeenCalled();
    expect(getContactsUseCase.execute).toHaveBeenCalledTimes(1);
  });

  it('should execute updateContact service when  should execute updateContact service when editContact is called is called', fakeAsync(async () => {
    await component.editContact(mockContacts[0]);
    tick();

    expect(updateContactService.execute).toHaveBeenCalledWith(mockContacts[0]);
    expect(getContactsUseCase.execute).toHaveBeenCalledTimes(1);
  }));

  it('should display error message when present', () => {
    component.errorMessage.set('Error al cargar contactos');
    fixture.detectChanges();

    const html = fixture.nativeElement.innerHTML;
    expect(html).toContain('Error al cargar contactos');

    spyOn(component, 'loadContacts');
    component.loadContacts();
    expect(component.loadContacts).toHaveBeenCalled();
  });
});
