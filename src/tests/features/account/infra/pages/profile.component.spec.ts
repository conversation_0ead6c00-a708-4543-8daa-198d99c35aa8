import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  AplazoFormInputDirective,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { TranslocoService } from '@jsverse/transloco';
import { of } from 'rxjs';
import { UserStoreService } from 'src/app/features/user/src/application/services/user-store.service';
import { MerchantWithoutBillingInfoUseCase } from '../../../../../app/features/account/application/usecases/merchant-without-billing.usecase';
import { MerchantWithoutBillingRepository } from '../../../../../app/features/account/domain/repositories/merchant-without-billing.repository';
import {
  IProfileFormInjectedTextUI,
  ProfileComponent,
} from '../../../../../app/features/account/infra/pages/profile.component';

describe('ProfileComponent', () => {
  let fixture: ComponentFixture<ProfileComponent>;
  let component: ProfileComponent;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let usecase: MerchantWithoutBillingInfoUseCase;

  const textUI: IProfileFormInjectedTextUI = {
    email: {
      title: 'Email',
      inputLabel: 'Email',
    },
    merchant: {
      title: 'Merchant',
      inputNameLabel: 'Name',
      inputWebsiteLabel: 'Website',
      inputAddressLabel: 'Address',
      idLabel: 'ID',
    },
    representative: {
      title: 'Representative',
      inputNameLabel: 'Name',
      inputRoleLabel: 'Role',
      inputPhoneNumberLabel: 'Phone number',
    },
    message: {
      content: 'Content',
      supportEmail: '<EMAIL>',
      whatsappURL: 'https://wa.me/**********',
    },
    errors: {
      required: 'Required',
      maxNum: 'Max number',
      onlyNumbers: 'Only numbers',
      websitePattern: 'Website pattern',
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        AsyncPipe,
        ReactiveFormsModule,
        AplazoIconComponent,
        AplazoButtonComponent,
        AplazoFormFieldDirectives,
        ProfileComponent,
      ],
      providers: [
        AplazoIconRegistryService,
        MerchantWithoutBillingInfoUseCase,
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UserStoreService,
          useValue: {
            merchantId$: of(123),
          },
        },
        {
          provide: MerchantWithoutBillingRepository,
          useValue: {
            getMerchantWithoutBilling: () => of(null),
          },
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: TranslocoService,
          useValue: { selectTranslateObject: () => of(textUI) },
        },
      ],
    });

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    usecase = TestBed.inject(MerchantWithoutBillingInfoUseCase);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should have a profile form and its disabled by default', () => {
    const form = fixture.debugElement.query(By.css('form'));
    const inputElements = form.queryAll(By.directive(AplazoFormInputDirective));
    const isAllInputsDisabled = inputElements.every(
      element => element.nativeElement.disabled
    );

    expect(form).withContext('Profile form exists').toBeTruthy();
    expect(inputElements.length)
      .withContext('Profile form has 8 input elements')
      .toBe(8);
    expect(isAllInputsDisabled)
      .withContext('All input elements are disabled')
      .toBeTrue();
  });

  it('should each formControl have the correct value', () => {
    expect(component.email.value).toBe('No disponible');
    expect(component.name.value).toBe('No disponible');
    expect(component.website.value).toBe('No disponible');
    expect(component.address.value).toBe('No disponible');
    expect(component.representativeName.value).toBe('No disponible');
    expect(component.representativeRole.value).toBe('No disponible');
    expect(component.representativePhoneNumber.value).toBe('No disponible');
    expect(component.merchantID.value).toBe(123);
  });
});
