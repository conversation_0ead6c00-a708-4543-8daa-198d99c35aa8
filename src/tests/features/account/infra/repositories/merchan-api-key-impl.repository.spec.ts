import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { MerchantApiKeyRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-api-key-impl.repository';
describe('MerchantApiKeyImplRepository', () => {
  let httpTestingController: HttpTestingController;
  let service: MerchantApiKeyRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
        MerchantApiKeyRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(MerchantApiKeyRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(MerchantApiKeyRepositoryImpl);
  });

  it('should execute successfully', () => {
    const token = 'test-token';
    service.getApiKey().subscribe({
      next: response => {
        expect(response).toBe(token);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/api-token'
    );
    expect(req.request.method).toBe('GET');
    req.flush(token);
  });

  it('should throw error when request fails', () => {
    service.getApiKey().subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/api-token'
    );
    expect(req.request.method).toBe('GET');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
