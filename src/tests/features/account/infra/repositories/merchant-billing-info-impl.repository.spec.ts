import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { MerchantBillingInfoRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-billing-info-impl.repository';

describe('MerchantBillingInfoRepositoryImpl', () => {
  let httpTestingController: HttpTestingController;
  let service: MerchantBillingInfoRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        MerchantBillingInfoRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(MerchantBillingInfoRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(MerchantBillingInfoRepositoryImpl);
  });

  it('should execute successfully', () => {
    const expectedResponse = {
      rfc: 'XXX900101XXX',
      bankName: 'BANCO',
      accountNumber: '123456789012345678',
    };
    service.getMerchantBilling().subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://merchantdash.aplazo.net/api/v2/merchant/billing-info'
    );
    expect(req.request.method).toBe('GET');
    req.flush(expectedResponse);
  });

  it('should throw error when request fails', () => {
    service.getMerchantBilling().subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://merchantdash.aplazo.net/api/v2/merchant/billing-info'
    );
    req.flush('Deliberate error', { status: 404, statusText: 'Not Found' });
  });
});
