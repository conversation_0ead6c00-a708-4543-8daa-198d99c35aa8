import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { MerchantChangePasswordRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-change-password-impl.repository';

describe('MerchantChangePasswordRepositoryImpl', () => {
  let httpTestingController: HttpTestingController;
  let service: MerchantChangePasswordRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        MerchantChangePasswordRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(MerchantChangePasswordRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(MerchantChangePasswordRepositoryImpl);
  });

  it('should execute successfully', () => {
    const oldPassword = 'test-old-password';
    const password = 'test-password';

    service
      .changePassword({
        oldPassword,
        newPassword: password,
        confirmNewPassword: password,
      })
      .subscribe({
        next: response => {
          expect(response).toBeFalsy();
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/change-password'
    );

    expect(req.request.method).toBe('POST');
    req.flush(null);
  });

  it('should throw error when request fails', () => {
    const oldPassword = 'test-old-password';
    const password = 'test-password';

    service
      .changePassword({
        oldPassword,
        newPassword: password,
        confirmNewPassword: password,
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(error.error).toBe('Deliberate error');
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/change-password'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
