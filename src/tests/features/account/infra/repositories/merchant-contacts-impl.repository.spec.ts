import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { MerchantContactsRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-contacts-impl.repository';
import {
  NewContactDto,
  RetrievedContactResponse,
  UpdateContactDto,
} from '../../../../../app/features/account/application/dtos/contact.dto';

describe('MerchantContactsRepositoryImpl', () => {
  let httpTestingController: HttpTestingController;
  let service: MerchantContactsRepositoryImpl;
  let spyHttpGet: jasmine.Spy;
  let spyHttpPost: jasmine.Spy;
  let spyHttpPut: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://api.aplazo.net/',
          },
        },
        MerchantContactsRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(MerchantContactsRepositoryImpl);
    const http = TestBed.inject(HttpClient);
    spyHttpGet = spyOn(http, 'get').and.callThrough();
    spyHttpPost = spyOn(http, 'post').and.callThrough();
    spyHttpPut = spyOn(http, 'put').and.callThrough();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(MerchantContactsRepositoryImpl);
  });

  it('should get contacts info successfully', () => {
    const mockResponse: RetrievedContactResponse[] = [
      {
        id: 1,
        role: 'Mkt',
        businessAreaId: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '1234567890',
        businessArea: 'Marketing',
      },
    ];

    service.getInfo().subscribe({
      next: response => {
        expect(response).toEqual(mockResponse);
        expect(spyHttpGet).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/api/v1/merchant-contact-info'
    );

    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should create contact successfully', () => {
    const mockRequest: NewContactDto = {
      role: 'IT',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      businessAreaId: 1,
    };

    const mockResponse: RetrievedContactResponse = {
      id: 1,
      name: mockRequest.name,
      email: mockRequest.email,
      phone: mockRequest.phone,
      businessArea: 'TI (Tecnología de la Información)',
      ...mockRequest,
    };

    service.createOne(mockRequest).subscribe({
      next: response => {
        expect(response).toEqual(mockResponse);
        expect(spyHttpPost).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/api/v1/merchant-contact-info'
    );

    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockRequest);
    req.flush(mockResponse);
  });

  it('should update contact successfully', () => {
    const mockRequest: UpdateContactDto = {
      id: 1,
      role: 'Commercial',
      name: 'John Doe Updated',
      email: '<EMAIL>',
      phone: '0987654321',
      businessAreaId: 4,
    };

    const mockResponse: RetrievedContactResponse = {
      id: mockRequest.id,
      role: mockRequest.role,
      name: mockRequest.name,
      email: mockRequest.email,
      phone: mockRequest.phone,
      businessAreaId: mockRequest.businessAreaId,
      businessArea: 'Comercial',
      ...mockRequest,
    };

    service.updateOne(mockRequest).subscribe({
      next: response => {
        expect(response).toEqual(mockResponse);
        expect(spyHttpPut).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      `https://api.aplazo.net/api/v1/merchant-contact-info/${mockRequest.id}`
    );

    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(mockRequest);
    req.flush(mockResponse);
  });

  it('should get catalogs successfully', () => {
    const mockResponse = [
      { id: 1, name: 'Marketing' },
      { id: 2, name: 'Soporte' },
      { id: 3, name: 'TI (Tecnología de la Información)' },
      { id: 4, name: 'Comercial' },
      { id: 5, name: 'Finanzas' },
    ];

    service.getCatalogs().subscribe({
      next: response => {
        expect(response).toEqual(mockResponse);
        expect(spyHttpGet).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/api/v1/merchant-contact-info/business-area-catalog'
    );

    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should handle error when getting contacts info fails', () => {
    service.getInfo().subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttpGet).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/api/v1/merchant-contact-info'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });

  afterEach(() => {
    httpTestingController.verify();
  });
});
