import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { MerchantWithoutBillingRepositoryImpl } from '../../../../../app/features/account/infra/repositories/merchant-without-billing-repository.impl';

describe('MerchantWithoutBillingRepositoryImpl', () => {
  let httpTestingController: HttpTestingController;
  let service: MerchantWithoutBillingRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
        MerchantWithoutBillingRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(MerchantWithoutBillingRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(MerchantWithoutBillingRepositoryImpl);
  });

  it('should execute successfully', () => {
    const expectedResponse = {
      email: '<EMAIL>',
      name: 'Zapaterias Aplazo Test',
      address: 'Siemprevica 31 Springfield Massachusetts 01103 Estados Unidos',
      website: 'zapateria.aplazo.mx',
      representativeName: 'Angel Pena',
      representativeRole: 'Representante Legal',
      representativePhoneNumber: '5555555555',
    };

    service.getMerchantWithoutBilling().subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/company'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when request fails', () => {
    service.getMerchantWithoutBilling().subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/company'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
