import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { UpdateMerchantWihtoutBillingRepositoryImpl } from '../../../../../app/features/account/infra/repositories/update-merchant-wihtout-billing-impl.repository';

describe('UpdateMerchantWithoutBillingImplRepository', () => {
  let httpTestingController: HttpTestingController;
  let service: UpdateMerchantWihtoutBillingRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        UpdateMerchantWihtoutBillingRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(UpdateMerchantWihtoutBillingRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(UpdateMerchantWihtoutBillingRepositoryImpl);
  });

  it('should execute successfully', () => {
    service
      .updateMerchantWithoutBilling({
        address:
          'Siempreviva 33 Springfield Massachusetts 01103 Estados Unidos',
        email: '<EMAIL>',
        name: 'Zapaterias Aplazo Test',
        representativeName: 'Angel Pena',
        representativePhoneNumber: '5555555555',
        representativeRole: 'Representante Legal',
        website: 'zapateria.aplazo.mx',
      })
      .subscribe({
        next: response => {
          expect(response).toBeFalsy();
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/company-info'
    );

    expect(req.request.method).toBe('POST');

    req.flush(null);
  });

  it('should throw error when request fails', () => {
    service
      .updateMerchantWithoutBilling({
        address:
          'Siempreviva 33 Springfield Massachusetts 01103 Estados Unidos',
        email: '<EMAIL>',
        name: 'Zapaterias Aplazo Test',
        representativeName: 'Angel Pena',
        representativePhoneNumber: '5555555555',
        representativeRole: 'Representante Legal',
        website: 'zapateria.aplazo.mx',
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.error).toBe('Deliberate error');
          expect(error.status).toBe(500);
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/company-info'
    );

    expect(req.request.method).toBe('POST');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
