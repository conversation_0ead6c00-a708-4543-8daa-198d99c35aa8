import { TestBed } from '@angular/core/testing';
import { NotifierService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { CreateOneContactUseCase } from '../../../../../app/features/account/application/usecases/new-contact.usecase';
import {
  ContactUI,
  RetrievedContactResponse,
} from '../../../../../app/features/account/application/dtos/contact.dto';
import { CreateContactWithDialogFormService } from '../../../../../app/features/account/infra/services/create-contact-with-dialog.service';
import { MerchantStoreService } from '../../../../../app/features/account/infra/services/merchant-store.service';

const setup = (args?: {
  store?: {
    existingRoles?: string[];
    contacts?: ContactUI[];
  };
  dialogResult?: Partial<ContactUI> & { hasConfirmation: boolean };
}) => {
  const defaultConfig = {
    store: {
      existingRoles: ['Mkt', 'Support'],
      contacts: [],
    },
    dialogResult: {
      hasConfirmation: false,
    },
  };

  const config = {
    store: {
      existingRoles:
        args?.store?.existingRoles ?? defaultConfig.store.existingRoles,
      contacts: args?.store?.contacts ?? defaultConfig.store.contacts,
    },
    dialogResult: {
      ...defaultConfig.dialogResult,
      ...args?.dialogResult,
    },
  };

  TestBed.configureTestingModule({
    providers: [
      CreateContactWithDialogFormService,
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: CreateOneContactUseCase,
        useValue: jasmine.createSpyObj('CreateOneContactUseCase', ['execute']),
      },
      {
        provide: MerchantStoreService,
        useValue: {
          getExistingRoles: () => config.store.existingRoles,
          contacts: () => config.store.contacts,
          setMerchantContacts: jasmine.createSpy('setMerchantContacts'),
        },
      },
      {
        provide: DialogService,
        useValue: {
          open: () => {
            return {
              afterClosed$: of(config.dialogResult),
            };
          },
        },
      },
    ],
  });

  const usecase = TestBed.inject(CreateContactWithDialogFormService);
  const store = TestBed.inject(MerchantStoreService);
  const notifier = TestBed.inject(NotifierService);
  const dialog = TestBed.inject(DialogService);
  const applicationUsecase = TestBed.inject(
    CreateOneContactUseCase
  ) as jasmine.SpyObj<CreateOneContactUseCase>;
  const errorHandler = TestBed.inject(UseCaseErrorHandler);
  const dialogSpy = spyOn(dialog, 'open').and.callThrough();
  const notifierErrorSpy = spyOn(notifier, 'error').and.callThrough();
  const notifierWarningSpy = spyOn(notifier, 'warning').and.callThrough();
  const notifierInfoSpy = spyOn(notifier, 'info').and.callThrough();
  const errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    store,
    dialogSpy,
    notifierErrorSpy,
    notifierInfoSpy,
    notifierWarningSpy,
    applicationUsecase,
    errorHandlerSpy,
  };
};

describe('CreateContactWithDialogFormService', () => {
  it('should be created', () => {
    const { usecase } = setup();

    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CreateContactWithDialogFormService);
  });

  it('should show warning when all roles are assigned', async () => {
    const { usecase, dialogSpy, notifierWarningSpy } = setup({
      store: {
        existingRoles: ['Mkt', 'Support', 'IT', 'Commercial', 'Finance'],
      },
    });

    await usecase.execute();

    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'No hay roles disponibles',
      message:
        'Todos los roles ya han sido asignados. Para cambiar un contacto, edita uno existente.',
    });
    expect(dialogSpy).not.toHaveBeenCalled();
  });

  it('should show cancel notification', async () => {
    const {
      applicationUsecase,
      errorHandlerSpy,
      usecase,
      dialogSpy,
      notifierInfoSpy,
    } = setup();

    await usecase.execute();

    expect(notifierInfoSpy).toHaveBeenCalledWith({
      title: 'Creación cancelada',
    });
    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).not.toHaveBeenCalled();
    expect(applicationUsecase.execute).not.toHaveBeenCalled();
  });

  it('should show error notification when email is empty', async () => {
    const { applicationUsecase, errorHandlerSpy, usecase, dialogSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        email: '',
        name: 'Test User',
        role: 'IT',
        businessArea: 'IT',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).not.toHaveBeenCalled();
  });

  it('should show error notification when name is empty', async () => {
    const { applicationUsecase, errorHandlerSpy, usecase, dialogSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: '',
        role: 'IT',
        businessArea: 'IT',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).not.toHaveBeenCalled();
  });

  it('should show error notification when role is empty', async () => {
    const { applicationUsecase, errorHandlerSpy, usecase, dialogSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: '',
        businessArea: 'IT',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).not.toHaveBeenCalled();
  });

  it('should show error notification when businessArea is empty', async () => {
    const { applicationUsecase, errorHandlerSpy, usecase, dialogSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'IT',
        businessArea: undefined,
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).not.toHaveBeenCalled();
  });

  it('should show error notification when role is already assigned', async () => {
    const { applicationUsecase, errorHandlerSpy, usecase, dialogSpy } = setup({
      store: {
        existingRoles: ['Mkt', 'Support'],
      },
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'Mkt',
        businessArea: 'Marketing',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).not.toHaveBeenCalled();
  });

  it('should create a contact with phone', async () => {
    const { applicationUsecase, usecase, dialogSpy, store } = setup({
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'IT',
        businessArea: 'IT',
        phone: '1234567890',
      },
    });

    const expectedResponse: RetrievedContactResponse = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'IT',
      businessArea: 'IT',
      phone: '1234567890',
      businessAreaId: 3,
    };

    applicationUsecase.execute.and.returnValue(of(expectedResponse));

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).toHaveBeenCalledWith({
      email: '<EMAIL>',
      name: 'Test User',
      role: 'IT',
      businessArea: 'IT',
      phone: '1234567890',
    });
    expect(store.setMerchantContacts).toHaveBeenCalled();
  });

  it('should create a contact without phone', async () => {
    const { applicationUsecase, usecase, dialogSpy, store } = setup({
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'IT',
        businessArea: 'IT',
      },
    });

    const expectedResponse: RetrievedContactResponse = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'IT',
      businessArea: 'IT',
      phone: '',
      businessAreaId: 3,
    };

    applicationUsecase.execute.and.returnValue(of(expectedResponse));

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(applicationUsecase.execute).toHaveBeenCalledWith({
      email: '<EMAIL>',
      name: 'Test User',
      role: 'IT',
      businessArea: 'IT',
      phone: '',
    });
    expect(store.setMerchantContacts).toHaveBeenCalled();
  });

  it('should update store with empty contacts list', async () => {
    const { applicationUsecase, usecase, store } = setup({
      store: {
        contacts: [],
      },
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'IT',
        businessArea: 'IT',
      },
    });

    const expectedResponse: RetrievedContactResponse = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'IT',
      businessArea: 'IT',
      phone: '',
      businessAreaId: 3,
    };

    applicationUsecase.execute.and.returnValue(of(expectedResponse));

    await usecase.execute();

    expect(applicationUsecase.execute).toHaveBeenCalled();
    expect(store.setMerchantContacts).toHaveBeenCalledWith([
      jasmine.objectContaining({
        id: 1,
        email: '<EMAIL>',
      }),
    ]);
  });

  it('should update store with existing contacts', async () => {
    const existingContacts = [
      {
        id: 2,
        email: '<EMAIL>',
        name: 'Existing User',
        role: 'Mkt',
        businessArea: 'Marketing',
        phone: '9876543210',
      },
    ];

    const { applicationUsecase, usecase, store } = setup({
      store: {
        contacts: existingContacts as ContactUI[],
      },
      dialogResult: {
        hasConfirmation: true,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'IT',
        businessArea: 'IT',
      },
    });

    const expectedResponse: RetrievedContactResponse = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'IT',
      businessArea: 'IT',
      phone: '',
      businessAreaId: 3,
    };

    applicationUsecase.execute.and.returnValue(of(expectedResponse));

    await usecase.execute();

    expect(applicationUsecase.execute).toHaveBeenCalled();
    expect(store.setMerchantContacts).toHaveBeenCalledWith(
      jasmine.arrayContaining([
        jasmine.objectContaining({
          id: 2,
          email: '<EMAIL>',
        }),
        jasmine.objectContaining({
          id: 1,
          email: '<EMAIL>',
        }),
      ])
    );
  });
});
