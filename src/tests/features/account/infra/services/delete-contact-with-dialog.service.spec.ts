import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { NotifierService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { of, throwError } from 'rxjs';

import { DeleteContactWithDialogService } from 'src/app/features/account/infra/services/delete-contact-with-dialog.service';
import { DeleteContactUseCase } from 'src/app/features/account/application/usecases/delete-contact.usecase';
import { MerchantStoreService } from 'src/app/features/account/infra/services/merchant-store.service';
import { ContactUI } from 'src/app/features/account/application/dtos/contact.dto';
import { ContactDeleteConfirmComponent } from 'src/app/features/account/infra/components/contact-delete-confirm/contact-delete-confirm.component';

describe('DeleteContactWithDialogService', () => {
  let service: DeleteContactWithDialogService;
  let dialogServiceMock: jasmine.SpyObj<DialogService>;
  let deleteUseCaseMock: jasmine.SpyObj<DeleteContactUseCase>;
  let merchantStoreMock: jasmine.SpyObj<MerchantStoreService>;
  let notifierMock: jasmine.SpyObj<NotifierService>;
  let errorHandlerMock: jasmine.SpyObj<UseCaseErrorHandler>;
  let dialogRefMock: jasmine.SpyObj<DialogRef<any, boolean>>;

  const mockContact: ContactUI = {
    id: 123,
    email: '<EMAIL>',
    name: 'Test User',
    phone: '**********',
    role: 'Mkt',
    businessArea: 'Marketing',
  };

  const existingContacts: ContactUI[] = [
    mockContact,
    {
      id: 456,
      email: '<EMAIL>',
      name: 'Other User',
      phone: '**********',
      role: 'Support',
      businessArea: 'Support',
    },
  ];

  beforeEach(() => {
    dialogRefMock = jasmine.createSpyObj('DialogRef', [], {
      afterClosed$: of(true),
    });

    dialogServiceMock = jasmine.createSpyObj('DialogService', ['open']);
    dialogServiceMock.open.and.returnValue(dialogRefMock);

    deleteUseCaseMock = jasmine.createSpyObj('DeleteContactUseCase', [
      'execute',
    ]);
    deleteUseCaseMock.execute.and.returnValue(of(void 0));

    merchantStoreMock = jasmine.createSpyObj(
      'MerchantStoreService',
      ['setMerchantContacts'],
      {
        contacts: () => existingContacts,
      }
    );

    notifierMock = jasmine.createSpyObj('NotifierService', [
      'success',
      'info',
      'error',
    ]);

    errorHandlerMock = jasmine.createSpyObj('UseCaseErrorHandler', ['handle']);
    errorHandlerMock.handle.and.returnValue(of(false));

    TestBed.configureTestingModule({
      providers: [
        DeleteContactWithDialogService,
        { provide: DialogService, useValue: dialogServiceMock },
        { provide: DeleteContactUseCase, useValue: deleteUseCaseMock },
        { provide: MerchantStoreService, useValue: merchantStoreMock },
        { provide: NotifierService, useValue: notifierMock },
        { provide: UseCaseErrorHandler, useValue: errorHandlerMock },
      ],
    });

    service = TestBed.inject(DeleteContactWithDialogService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should open dialog with correct configuration', fakeAsync(() => {
    service.execute(mockContact);
    tick();

    expect(dialogServiceMock.open).toHaveBeenCalledWith(
      ContactDeleteConfirmComponent,
      jasmine.objectContaining({
        enableClose: false,
        width: '320px',
        data: {
          contactName: mockContact.name,
          contactId: mockContact.id,
        },
      })
    );
  }));

  it('should return false and not delete when dialog is canceled', fakeAsync(() => {
    Object.defineProperty(dialogRefMock, 'afterClosed$', {
      get: () => of(false),
    });

    let result: boolean | undefined;
    service.execute(mockContact).then(res => (result = res));
    tick();

    expect(result).toBeFalse();
    expect(deleteUseCaseMock.execute).not.toHaveBeenCalled();
    expect(merchantStoreMock.setMerchantContacts).not.toHaveBeenCalled();
  }));

  it('should return true and delete contact when confirmed', fakeAsync(() => {
    let result: boolean | undefined;
    service.execute(mockContact).then(res => (result = res));
    tick();

    expect(result).toBeTrue();
    expect(deleteUseCaseMock.execute).toHaveBeenCalledWith(mockContact.id);
    expect(merchantStoreMock.setMerchantContacts).toHaveBeenCalledWith([
      existingContacts[1],
    ]);

    tick();
  }));

  it('should show error and return false when contact has no ID', fakeAsync(() => {
    const invalidContact: ContactUI = {
      ...mockContact,
      id: undefined,
    };

    let result: boolean | undefined;
    service.execute(invalidContact).then(res => (result = res));
    tick();

    expect(result).toBeFalse();
    expect(dialogServiceMock.open).not.toHaveBeenCalled();
    expect(errorHandlerMock.handle).toHaveBeenCalled();
  }));

  it('should handle errors from delete usecase', fakeAsync(() => {
    deleteUseCaseMock.execute.and.returnValue(
      throwError(() => new Error('Delete failed'))
    );

    let result: boolean | undefined;
    service.execute(mockContact).then(res => (result = res));
    tick();

    expect(result).toBeFalse();
    expect(deleteUseCaseMock.execute).toHaveBeenCalled();
    expect(errorHandlerMock.handle).toHaveBeenCalled();
    expect(merchantStoreMock.setMerchantContacts).not.toHaveBeenCalled();
  }));

  it('should update store correctly after deletion', fakeAsync(() => {
    service.execute(mockContact).then();
    tick();

    const expectedList = existingContacts.filter(c => c.id !== mockContact.id);
    expect(merchantStoreMock.setMerchantContacts).toHaveBeenCalledWith(
      expectedList
    );
  }));

  it('should handle RuntimeMerchantError properly', fakeAsync(() => {
    const invalidContact = {} as ContactUI;

    errorHandlerMock.handle.and.returnValue(
      throwError(() => new Error('Runtime Merchant Error'))
    );

    service.execute(invalidContact).then(
      () => fail('Promise should be rejected'),
      error => {
        expect(error).toBeTruthy();
        expect(errorHandlerMock.handle).toHaveBeenCalled();
        expect(dialogServiceMock.open).not.toHaveBeenCalled();
      }
    );

    tick();
  }));
});
