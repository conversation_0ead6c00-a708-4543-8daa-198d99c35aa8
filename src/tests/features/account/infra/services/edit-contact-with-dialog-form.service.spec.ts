import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { NotifierService, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { of, BehaviorSubject } from 'rxjs';
import {
  BusinessArea,
  ContactUI,
  RetrievedContactResponse,
  serializeRetrievedContactResponse,
} from 'src/app/features/account/application/dtos/contact.dto';
import { UpdateOneContactUseCase } from 'src/app/features/account/application/usecases/update-contact.usecase';
import { ContactFormComponent } from 'src/app/features/account/infra/components/contact-form/contact-form.component';
import { UpdateContactWithDialogFormService } from 'src/app/features/account/infra/services/edit-contact-with-dialog-form.service';
import { MerchantStoreService } from 'src/app/features/account/infra/services/merchant-store.service';

describe('UpdateContactWithDialogFormService', () => {
  let service: UpdateContactWithDialogFormService;
  let dialogServiceMock: jasmine.SpyObj<DialogService>;
  let merchantStoreMock: jasmine.SpyObj<MerchantStoreService>;
  let notifierMock: jasmine.SpyObj<NotifierService>;
  let updateUseCaseMock: jasmine.SpyObj<UpdateOneContactUseCase>;
  let dialogRefMock: jasmine.SpyObj<DialogRef<any, any>>;

  const mockContact: ContactUI = {
    id: 1,
    email: '<EMAIL>',
    name: 'Test User',
    phone: '**********',
    role: 'Mkt',
    businessArea: 'Marketing',
  };

  const updatedContact: ContactUI = {
    id: 1,
    email: '<EMAIL>',
    name: 'Updated User',
    phone: '**********',
    role: 'Mkt',
    businessArea: 'Marketing',
  };

  const mockResponseData: RetrievedContactResponse = {
    id: 1,
    email: '<EMAIL>',
    name: 'Updated User',
    phone: '**********',
    role: 'Mkt',
    businessArea: 'Marketing',
    businessAreaId: 2,
  };

  const existingContacts: ContactUI[] = [
    mockContact,
    {
      id: 2,
      email: '<EMAIL>',
      name: 'Support User',
      phone: '5566778899',
      role: 'Support',
      businessArea: 'Support',
    },
  ];

  const mockMerchantContacts$ = new BehaviorSubject<{ data: ContactUI[] }>({
    data: existingContacts,
  });

  beforeEach(() => {
    dialogRefMock = jasmine.createSpyObj('DialogRef', [], {
      afterClosed$: of({
        hasConfirmation: true,
        ...updatedContact,
      }),
    });

    dialogServiceMock = jasmine.createSpyObj('DialogService', ['open']);
    dialogServiceMock.open.and.returnValue(dialogRefMock);

    merchantStoreMock = jasmine.createSpyObj(
      'MerchantStoreService',
      ['setMerchantContacts', 'getExistingRoles'],
      {
        merchantContacts$: mockMerchantContacts$.asObservable(),
      }
    );

    // Mock getExistingRoles to return roles from existingContacts
    merchantStoreMock.getExistingRoles.and.returnValue(['Mkt', 'Support']);

    notifierMock = jasmine.createSpyObj('NotifierService', [
      'success',
      'info',
      'error',
      'warning',
    ]);

    updateUseCaseMock = jasmine.createSpyObj('UpdateOneContactUseCase', [
      'execute',
    ]);
    updateUseCaseMock.execute.and.returnValue(of(mockResponseData));

    TestBed.configureTestingModule({
      providers: [
        UpdateContactWithDialogFormService,
        { provide: DialogService, useValue: dialogServiceMock },
        { provide: MerchantStoreService, useValue: merchantStoreMock },
        { provide: NotifierService, useValue: notifierMock },
        { provide: UpdateOneContactUseCase, useValue: updateUseCaseMock },
      ],
    });

    service = TestBed.inject(UpdateContactWithDialogFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should open dialog with contact data and existing roles', fakeAsync(() => {
    service.execute(mockContact);
    tick();

    expect(dialogServiceMock.open).toHaveBeenCalledWith(
      ContactFormComponent,
      jasmine.objectContaining({
        enableClose: false,
        width: '320px',
        data: jasmine.objectContaining({
          id: mockContact.id,
          email: mockContact.email,
          role: mockContact.role,
          existingRoles: ['Support'], // Should exclude the current contact's role
        }),
      })
    );
  }));

  it('should show info notification when user cancels', fakeAsync(() => {
    Object.defineProperty(dialogRefMock, 'afterClosed$', {
      get: () => of({ hasConfirmation: false }),
    });

    service.execute(mockContact);
    tick();

    expect(notifierMock.info).toHaveBeenCalledWith({
      title: 'Edición cancelada',
    });
    expect(updateUseCaseMock.execute).not.toHaveBeenCalled();
  }));

  it('should show warning when there are no changes', fakeAsync(() => {
    Object.defineProperty(dialogRefMock, 'afterClosed$', {
      get: () =>
        of({
          hasConfirmation: true,
          ...mockContact,
        }),
    });

    service.execute(mockContact);
    tick();

    expect(notifierMock.warning).toHaveBeenCalledWith({
      title: 'No hay cambios para guardar',
    });
    expect(updateUseCaseMock.execute).not.toHaveBeenCalled();
  }));

  it('should validate required fields', fakeAsync(() => {
    const invalidData = {
      hasConfirmation: true,
      id: 1,
      name: 'Test User',
      role: 'Mkt',
      businessArea: 'Marketing' as BusinessArea,
      phone: '**********',
    };

    Object.defineProperty(dialogRefMock, 'afterClosed$', {
      get: () => of(invalidData),
    });

    service.execute(mockContact);
    tick();

    expect(notifierMock.error).toHaveBeenCalledWith(
      jasmine.objectContaining({
        title: 'Error al actualizar el contacto',
      })
    );
    expect(updateUseCaseMock.execute).not.toHaveBeenCalled();
  }));

  it('should show error when changing to role that already exists', fakeAsync(() => {
    const contactWithChangedRole = {
      ...mockContact,
      role: 'Support', // This role is already taken
      hasConfirmation: true,
    };

    Object.defineProperty(dialogRefMock, 'afterClosed$', {
      get: () => of(contactWithChangedRole),
    });

    service.execute(mockContact);
    tick();

    expect(notifierMock.error).toHaveBeenCalledWith({
      title: 'Error de validación',
      message:
        'Ya existe un contacto con este rol. Para actualizar la información, edite el contacto existente.',
    });
    expect(updateUseCaseMock.execute).not.toHaveBeenCalled();
  }));

  it('should call update usecase with correct data', fakeAsync(() => {
    service.execute(mockContact);
    tick();

    expect(updateUseCaseMock.execute).toHaveBeenCalledWith(
      jasmine.objectContaining({
        id: updatedContact.id,
        email: updatedContact.email,
        name: updatedContact.name,
        role: updatedContact.role,
        businessArea: updatedContact.businessArea,
        phone: updatedContact.phone,
      })
    );
  }));

  it('should update store with response data when editing existing contact', fakeAsync(() => {
    service.execute(mockContact);
    tick();

    const serializedResponse =
      serializeRetrievedContactResponse(mockResponseData);

    expect(merchantStoreMock.setMerchantContacts).toHaveBeenCalledWith(
      jasmine.arrayContaining([
        jasmine.objectContaining({
          id: serializedResponse.id,
          email: serializedResponse.email,
          name: serializedResponse.name,
        }),
      ])
    );
  }));

  it('should handle empty merchant contacts list', fakeAsync(() => {
    mockMerchantContacts$.next({ data: [] });

    service.execute(mockContact);
    tick();

    expect(merchantStoreMock.setMerchantContacts).toHaveBeenCalledWith([
      jasmine.objectContaining({
        id: mockResponseData.id,
        email: mockResponseData.email,
      }),
    ]);
  }));

  it('should handle error when updating contact', fakeAsync(() => {
    updateUseCaseMock.execute.and.throwError(new Error('Test error'));

    service.execute(mockContact);
    tick();

    expect(notifierMock.error).toHaveBeenCalledWith(
      jasmine.objectContaining({
        title: 'Error al actualizar el contacto',
        message: jasmine.any(String),
      })
    );
  }));

  it('should handle RuntimeMerchantError with custom message', fakeAsync(() => {
    const customErrorMessage = 'Error de validación personalizado';
    updateUseCaseMock.execute.and.callFake(() => {
      throw new RuntimeMerchantError(customErrorMessage, 'test::errorCode');
    });

    service.execute(mockContact);
    tick();

    expect(notifierMock.error).toHaveBeenCalledWith({
      title: 'Error al actualizar el contacto',
      message: customErrorMessage,
    });
  }));

  it('should pass empty existing roles array when editing only contact', fakeAsync(() => {
    mockMerchantContacts$.next({ data: [mockContact] });
    merchantStoreMock.getExistingRoles.and.returnValue(['Mkt']);

    service.execute(mockContact);
    tick();

    expect(dialogServiceMock.open).toHaveBeenCalledWith(
      ContactFormComponent,
      jasmine.objectContaining({
        data: jasmine.objectContaining({
          existingRoles: [], // Should be empty as no other contacts exist
        }),
      })
    );
  }));
});
