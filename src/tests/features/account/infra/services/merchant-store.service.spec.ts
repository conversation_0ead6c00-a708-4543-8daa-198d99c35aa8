import { TestBed } from '@angular/core/testing';
import { MerchantStoreService } from '../../../../../app/features/account/infra/services/merchant-store.service';
import { ContactUI } from '../../../../../app/features/account/application/dtos/contact.dto';

describe('MerchantStoreService', () => {
  let service: MerchantStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [MerchantStoreService],
    });

    service = TestBed.inject(MerchantStoreService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(MerchantStoreService);
  });

  it('should initialize with empty contacts', () => {
    expect(service.contacts()).toEqual([]);
  });

  it('should set merchant contacts correctly', () => {
    const mockContacts: ContactUI[] = [
      {
        id: 1,
        role: '<PERSON>t',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'Marketing',
      },
      {
        id: 2,
        role: 'IT',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'IT',
      },
    ];

    service.setMerchantContacts(mockContacts);
    expect(service.contacts()).toEqual(mockContacts);
  });

  it('should not update contacts if they are the same', () => {
    const mockContacts: ContactUI[] = [
      {
        id: 1,
        role: 'Mkt',
        name: 'John Marketing',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'Marketing',
      },
    ];

    service.setMerchantContacts(mockContacts);
    service.setMerchantContacts([...mockContacts]);

    expect(service.contacts()).toEqual(mockContacts);
  });

  it('should handle null or empty contacts', () => {
    service.setMerchantContacts(null);
    expect(service.contacts()).toEqual([]);

    service.setMerchantContacts([]);
    expect(service.contacts()).toEqual([]);
  });

  it('should get existing roles correctly', () => {
    const mockContacts: ContactUI[] = [
      {
        id: 1,
        role: 'Mkt',
        name: 'John Marketing',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'Marketing',
      },
      {
        id: 2,
        role: 'IT',
        name: 'Jane Tech',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'IT',
      },
    ];

    service.setMerchantContacts(mockContacts);
    const existingRoles = service.getExistingRoles();
    expect(existingRoles).toEqual(['Mkt', 'IT']);
  });

  it('should get available roles correctly', () => {
    const mockContacts: ContactUI[] = [
      {
        id: 1,
        role: 'Mkt',
        name: 'John Marketing',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'Marketing',
      },
    ];

    service.setMerchantContacts(mockContacts);
    const availableRoles = service.getAvailableRoles();
    expect(availableRoles).toEqual(['Support', 'IT', 'Commercial', 'Finance']);
  });

  it('should clear all contacts', () => {
    const mockContacts: ContactUI[] = [
      {
        id: 1,
        role: 'Mkt',
        name: 'John Marketing',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'Marketing',
      },
    ];

    service.setMerchantContacts(mockContacts);
    service.clearAll();

    expect(service.contacts()).toEqual([]);
  });
});
