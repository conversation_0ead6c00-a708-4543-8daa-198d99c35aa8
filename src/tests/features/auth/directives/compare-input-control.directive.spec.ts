import { FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { compareTwoFormControlsByName } from '../../../../app/features/auth/directives/compare-input-control.directive';

describe('CompareInputControlDirective', () => {
  const baseName = 'baseInput';
  const compareName = 'compareInput';
  let baseControl: FormControl;
  let compareControl: FormControl;
  let formTest: FormGroup;
  let validatorFn: ValidatorFn;

  beforeEach(() => {
    baseControl = new FormControl('');
    compareControl = new FormControl('');

    formTest = new FormGroup({
      [baseName]: baseControl,
      [compareName]: compareControl,
    });

    validatorFn = compareTwoFormControlsByName({
      baseInputName: baseName,
      toCompareInputName: compareName,
    });
  });

  it('should return null when the values are the same', () => {
    baseControl.setValue('123');
    compareControl.setValue('123');

    expect(validatorFn(formTest)).toBeNull();
    expect(compareControl.hasError('verifyPassword'))
      .withContext('The control should not have the error')
      .toBeFalse();
  });

  it('should return an error when the values are different', () => {
    baseControl.setValue('123');
    compareControl.setValue('1234');

    expect(validatorFn(formTest)).toEqual({ verifyPassword: true });
    expect(compareControl.hasError('verifyPassword'))
      .withContext('The control should have the error')
      .toBeTrue();
  });

  it('should remove the error when the values are the same after being different', () => {
    baseControl.setValue('123');
    compareControl.setValue('1234');

    expect(validatorFn(formTest)).toEqual({ verifyPassword: true });
    expect(compareControl.hasError('verifyPassword'))
      .withContext('The control should have the error')
      .toBeTrue();

    baseControl.setValue('123');
    compareControl.setValue('123');

    expect(validatorFn(formTest)).toBeNull();
    expect(compareControl.hasError('verifyPassword'))
      .withContext('The control should not have the error')
      .toBeFalse();
  });
});
