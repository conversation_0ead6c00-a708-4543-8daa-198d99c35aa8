import { NgIf } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { provideRouter } from '@angular/router';
import {
  RouterTestingHarness,
  RouterTestingModule,
} from '@angular/router/testing';
import { NotifierService, RedirectionService } from '@aplazo/merchant/shared';
import {
  LocalNotifier,
  LocalRedirecter,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { AplazoPasswordControlComponent } from '@aplazo/shared-ui/merchant';
import { of } from 'rxjs';
import { EnterNewPasswordComponent } from 'src/app/features/auth/pages/enter-new-password.component';
import { DASH_ROUTES } from '../../../../app/config/app-route-core';
import { UserForgottenPasswordVerificationTokenUseCase } from '../../../../app/features/user/src/application/usecases/forgotten-password-verification-token.usecase';
import { UserSetNewPasswordUseCase } from '../../../../app/features/user/src/application/usecases/set-new-password.usecase';

const setup = async (route: string) => {
  TestBed.configureTestingModule({
    imports: [
      RouterTestingModule,
      AplazoLogoComponent,
      AplazoButtonComponent,
      AplazoFormFieldDirectives,
      ReactiveFormsModule,
      AplazoPasswordControlComponent,
      NgIf,
    ],
    providers: [
      {
        provide: RedirectionService,
        useClass: LocalRedirecter,
      },
      {
        provide: NotifierService,
        useClass: LocalNotifier,
      },
      {
        provide: UserForgottenPasswordVerificationTokenUseCase,
        useValue: {
          execute: (args: { token: string | undefined | null }) => {
            return of({ status: Boolean(args.token) });
          },
        },
      },
      {
        provide: UserSetNewPasswordUseCase,
        useValue: {
          execute: () => {
            return of(void 0);
          },
        },
      },

      provideRouter([{ path: '**', component: EnterNewPasswordComponent }]),
    ],
  });

  const routerHarness = await RouterTestingHarness.create();
  const component = await routerHarness.navigateByUrl(
    route,
    EnterNewPasswordComponent
  );
  const internalNavigationSpy = spyOn(
    TestBed.inject(RedirectionService),
    'internalNavigation'
  ).and.callThrough();
  const warningSpy = spyOn(
    TestBed.inject(NotifierService),
    'warning'
  ).and.callThrough();
  const setNewPasswordSpy = spyOn(
    TestBed.inject(UserSetNewPasswordUseCase),
    'execute'
  ).and.callThrough();

  return {
    component,
    routerHarness,
    internalNavigationSpy,
    setNewPasswordSpy,
    warningSpy,
  };
};

describe('EnterNewPasswordComponent', () => {
  it('should be created', async () => {
    const { component } = await setup('/new-password?token=123456');
    expect(component).toBeTruthy();
  });

  it('should have 2 Aplazo Password Form Control ', async () => {
    const { routerHarness } = await setup('/new-password?token=123456');

    const controls = routerHarness.routeDebugElement.queryAll(
      By.directive(AplazoPasswordControlComponent)
    );

    expect(controls.length)
      .withContext('Expected to find 2 AplazoPasswordControlComponent')
      .toBe(2);
  });

  it('should not shown form when token is invalid', async () => {
    const { routerHarness } = await setup('/new-password?token=');

    const form = routerHarness.routeDebugElement.query(By.css('form'));

    expect(form).withContext('Expected form to be null').toBeNull();
  });

  it('should shown form when token is valid', async () => {
    const { routerHarness } = await setup('/new-password?token=123456');

    const form = routerHarness.routeDebugElement.query(By.css('form'));

    expect(form).withContext('Expected form to be truthy').toBeTruthy();
  });

  it('should update hasComparedError', async () => {
    const { component, routerHarness } = await setup(
      '/new-password?token=123456'
    );

    expect(component.hasComparedError)
      .withContext('Expected hasComparedError to be false')
      .toBeFalse();

    component.basePassword.setValue('12345678');
    component.confirmPassword.setValue('123456789');
    routerHarness.routeDebugElement
      .query(By.css('button[type="submit"]'))
      .nativeElement.click();

    expect(component.hasComparedError)
      .withContext('Expected hasComparedError to be true')
      .toBeTrue();
  });

  it('should have invalid token when no source is provided', async () => {
    const { component } = await setup('/new-password?token=');

    expect(component.isValidToken)
      .withContext(
        'Expected isValidToken to be false when no source is provided'
      )
      .toBeFalse();
  });

  it('should have invalid token when ACS source is provided', async () => {
    const { component } = await setup('/new-password?source=ACS');

    expect(component.isValidToken)
      .withContext(
        'Expected isValidToken to be false when ACS source is provided'
      )
      .toBeFalse();
  });

  it('should have invalid token when ACS source is provided but token is empty', async () => {
    const { component } = await setup('/new-password?source=ACS&token=');

    expect(component.isValidToken)
      .withContext(
        'Expected isValidToken to be false when ACS source is provided but token is empty'
      )
      .toBeFalse();
  });

  it('should have valid token when ACS source is provided', async () => {
    const { component } = await setup('/new-password?source=ACS&token=123456');

    expect(component.isValidToken)
      .withContext(
        'Expected isValidToken to be true when ACS source is provided'
      )
      .toBeTrue();
  });

  it('should have valid token when no source is provided', async () => {
    const { component } = await setup('/new-password?token=123456');

    expect(component.isValidToken)
      .withContext('Expected isValidToken to be true when source is provided')
      .toBeTrue();
  });

  it('should navigate to login route', async () => {
    const { routerHarness, internalNavigationSpy } = await setup(
      '/new-password?token=123456'
    );

    const button = routerHarness.routeDebugElement.query(
      By.css('button[type="button"]:not([aria-label])')
    );

    button.nativeElement.click();

    expect(internalNavigationSpy).toHaveBeenCalledOnceWith([
      DASH_ROUTES.authentication,
      DASH_ROUTES.login,
    ]);
    expect(internalNavigationSpy).toHaveBeenCalledTimes(1);
  });

  it('should have one only redirection button when token is invalid', async () => {
    const { routerHarness } = await setup('/new-password?token=');

    const buttons = routerHarness.routeDebugElement.queryAll(
      By.css('button[type="button"]')
    );

    expect(buttons.length).withContext('Expected to find 1 button').toBe(1);
  });

  it('should navigate to forgot password route', async () => {
    const { routerHarness, internalNavigationSpy } = await setup(
      '/new-password?token='
    );

    const button = routerHarness.routeDebugElement.query(
      By.css('button[type="button"]')
    );

    button.nativeElement.click();

    expect(internalNavigationSpy).toHaveBeenCalledOnceWith([
      DASH_ROUTES.authentication,
      DASH_ROUTES.forgotPass,
    ]);
    expect(internalNavigationSpy).toHaveBeenCalledTimes(1);
  });

  it('should call setNewPassword and goToLoginRoute when form is valid', async () => {
    const { component, routerHarness, setNewPasswordSpy } = await setup(
      '/new-password?token=123456'
    );
    const goToLoginRouteSpy = spyOn(component, 'goToLoginRoute');

    component.basePassword.setValue('12345678');
    component.confirmPassword.setValue('12345678');
    routerHarness.routeDebugElement
      .query(By.css('button[type="submit"]'))
      .nativeElement.click();

    expect(setNewPasswordSpy)
      .withContext('Expected setNewPassword to have been called once')
      .toHaveBeenCalledTimes(1);
    expect(goToLoginRouteSpy)
      .withContext('Expected goToLoginRoute to have been called once')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call neither one setNewPassword and goToLoginRoute when form is invalid', async () => {
    const { component, routerHarness, setNewPasswordSpy } = await setup(
      '/new-password?token=123456'
    );
    const goToLoginRouteSpy = spyOn(component, 'goToLoginRoute');

    component.basePassword.setValue('12345678');
    component.confirmPassword.setValue('123456789');
    routerHarness.routeDebugElement
      .query(By.css('button[type="submit"]'))
      .nativeElement.click();

    expect(setNewPasswordSpy)
      .withContext('Expected setNewPassword to have not been called')
      .toHaveBeenCalledTimes(0);
    expect(goToLoginRouteSpy)
      .withContext('Expected goToLoginRoute to have not been called')
      .toHaveBeenCalledTimes(0);
  });

  it('should show warning when called setNewPassword with invalid token', async () => {
    const { component, setNewPasswordSpy, warningSpy } = await setup(
      '/new-password?token='
    );

    component.basePassword.setValue('12345678');
    component.confirmPassword.setValue('12345678');

    component.setNewPassword();

    expect(warningSpy)
      .withContext('Expected warning to have been called once')
      .toHaveBeenCalledTimes(1);

    expect(setNewPasswordSpy)
      .withContext('Expected setNewPassword to have not been called')
      .toHaveBeenCalledTimes(0);
  });
});
