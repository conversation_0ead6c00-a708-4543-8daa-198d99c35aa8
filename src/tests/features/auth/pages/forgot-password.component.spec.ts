import { NgIf } from '@angular/common';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import {
  LoaderService,
  NotifierService,
  RedirectionService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalRedirecter,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  AplazoFormInputDirective,
} from '@aplazo/shared-ui/forms';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { Observable } from 'rxjs';
import { ForgotPasswordComponent } from '../../../../app/features/auth/pages/forgot-password.component';
import { UserForgotPasswordNewRolesDto } from '../../../../app/features/user/src/application/dtos/forgot-password-v2.dto';
import { ForgotPasswordUseCase } from '../../../../app/features/user/src/application/usecases/forgot-password.usecase';
import { UserForgotPasswordResetRepository } from '../../../../app/features/user/src/domain/repositories/user-forgot-password-v2.repository';
import { UserForgotPasswordRepository } from '../../../../app/features/user/src/domain/repositories/user-forgot-password.repository';
import { LocalForgotOldRepository } from '../../user/infra/repositories/local-forgot-old.repository';
import { LocalForgotV2Repository } from '../../user/infra/repositories/local-forgot-v2.repository';

describe('ForgotPasswordComponent', () => {
  let component: ForgotPasswordComponent;
  let fixture: ComponentFixture<ForgotPasswordComponent>;
  let redirectionService: RedirectionService;
  let forgotPasswordUseCase: ForgotPasswordUseCase;
  let repoV1: UserForgotPasswordRepository<FormData, Observable<void>>;
  let repoV2: UserForgotPasswordResetRepository<
    string,
    Observable<UserForgotPasswordNewRolesDto>
  >;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        ForgotPasswordComponent,
        AplazoLogoComponent,
        AplazoButtonComponent,
        AplazoFormFieldDirectives,
        ReactiveFormsModule,
        NgIf,
      ],
      providers: [
        ForgotPasswordUseCase,
        {
          provide: UserForgotPasswordRepository,
          useClass: LocalForgotOldRepository,
        },
        {
          provide: UserForgotPasswordResetRepository,
          useClass: LocalForgotV2Repository,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
      ],
    });

    fixture = TestBed.createComponent(ForgotPasswordComponent);
    component = fixture.componentInstance;
    redirectionService = TestBed.inject(RedirectionService);
    forgotPasswordUseCase = TestBed.inject(ForgotPasswordUseCase);
    repoV1 = TestBed.inject(UserForgotPasswordRepository);
    repoV2 = TestBed.inject(UserForgotPasswordResetRepository);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should have logo component', () => {
    const logo = fixture.debugElement.query(By.directive(AplazoLogoComponent));
    expect(logo).toBeTruthy();
  });

  it('should have the title', () => {
    const title = fixture.debugElement.query(By.css('h4'));
    expect(title.nativeElement.textContent).toContain(
      '¿Olvidaste tu contraseña?'
    );
  });

  it('should have one only input for email', () => {
    const inputs = fixture.debugElement.queryAll(
      By.directive(AplazoFormInputDirective)
    );

    expect(inputs.length).toBe(1);
  });

  it('should have a button to send email', () => {
    const buttons = fixture.debugElement.queryAll(
      By.css('button[type="submit"]')
    );
    expect(buttons.length).toBeTruthy();
  });

  it('should not call forgotPasswordUseCase when form is invalid', fakeAsync(() => {
    const spy = spyOn(forgotPasswordUseCase, 'execute').and.callThrough();

    component.sendEmail();

    tick();

    expect(spy).toHaveBeenCalledTimes(0);
  }));

  it('should call forgotPasswordUseCase when form is valid', fakeAsync(() => {
    const spy = spyOn(forgotPasswordUseCase, 'execute').and.callThrough();
    const repoV1Spy = spyOn(repoV1, 'requestReset').and.callThrough();
    const repoV2Spy = spyOn(repoV2, 'reset').and.callThrough();
    const internalNavigationSpy = spyOn(
      redirectionService,
      'internalNavigation'
    ).and.callThrough();

    component.email.setValue('<EMAIL>');

    component.sendEmail();

    tick();

    expect(spy).toHaveBeenCalledTimes(1);
    expect(repoV1Spy).toHaveBeenCalledTimes(1);
    expect(repoV2Spy).toHaveBeenCalledTimes(1);
    expect(internalNavigationSpy).toHaveBeenCalledTimes(1);
  }));

  it('should call goToLoginRoute when button is clicked', () => {
    const spy = spyOn(component, 'goToLoginRoute').and.callThrough();
    const internalNavigationSpy = spyOn(
      redirectionService,
      'internalNavigation'
    ).and.callThrough();
    const button = fixture.debugElement.query(By.css('button[type="button"]'));

    button.nativeElement.click();

    expect(spy).toHaveBeenCalledTimes(1);
    expect(internalNavigationSpy).toHaveBeenCalledTimes(1);
  });
});
