import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { RedirectionService } from '@aplazo/merchant/shared';
import {
  provideJwtDecoderTesting,
  provideLoaderTesting,
  provideNotifierTesting,
  provideRedirecterTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { LoginFormComponent } from '@aplazo/shared-ui/merchant';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { StatsigService } from '@statsig/angular-bindings';
import { of } from 'rxjs';
import { LoginCentralizedUsecase } from 'src/app/features/user/src/application/usecases/login-centralized.usecase';
import { CredentialsLoginCentralized } from 'src/app/features/user/src/domain/entities/credentials-login-centralized';
import { LoginCentralizedRepository } from 'src/app/features/user/src/domain/repositories/login-centralized.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../app/config/merchant-core.environment';
import { LoginComponent } from '../../../../app/features/auth/pages/login.component';
import { DashUserPersistenceService } from '../../../../app/features/user/src/application/services/local-persistence.service';
import { UserStoreService } from '../../../../app/features/user/src/application/services/user-store.service';
import { Credentials } from '../../../../app/features/user/src/domain/entities/credentials';
import { UserRepository } from '../../../../app/features/user/src/domain/repositories/user.repository';
import { DashUserSimpleStore } from '../../../../app/features/user/src/infra/services/dash-user-simple-store.service';
import { LocalDashLoginRepository } from '../../user/infra/repositories/local-dash-login.repository';
import { LocalLoginCentralizedRepository } from '../../user/infra/repositories/local-login-centralized.repository';
import { LocalDashUserPersistenceService } from '../../user/infra/services/local-dash-user-persistence.service';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let centralizeUsecase: LoginCentralizedUsecase;
  let redirecter: RedirectionService;
  let flags: StatsigService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoLogoComponent,
        LoginFormComponent,
        AplazoButtonComponent,
        NgIf,
        AsyncPipe,
        LoginComponent,
      ],
      providers: [
        provideTranslocoScope('login'),
        LoginCentralizedUsecase,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            redirectionsMerchantRegisterPage:
              'https://merchant-register.aplazo.net',
            redirectionsCustomerLoginPage: 'https://customer.aplazo.net',
          },
        },
        {
          provide: UserRepository,
          useClass: LocalDashLoginRepository,
        },
        {
          provide: LoginCentralizedRepository,
          useClass: LocalLoginCentralizedRepository,
        },
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideJwtDecoderTesting(),
        provideRedirecterTesting(),
        {
          provide: DashUserPersistenceService,
          useClass: LocalDashUserPersistenceService,
        },
        {
          provide: UserStoreService,
          useClass: DashUserSimpleStore,
        },
        {
          provide: TranslocoService,
          useValue: {
            selectTranslateObject: () => {
              return of({
                header: { title: 'Login' },
                form: {
                  email: {
                    label: 'Email',
                    placeholder: 'Enter your email',
                    requiredError: 'Email is required',
                    emailPatternError: 'Invalid email',
                  },
                  password: {
                    label: 'Password',
                    placeholder: 'Enter your password',
                  },
                  submitButton: 'Login',
                },
                actions: {
                  title: 'New to Aplazo?',
                  registerButton: 'Register',
                  customerLoginButton: 'Login as a customer',
                  forgotPasswordButton: 'Forgot password?',
                },
              });
            },
          },
        },
        {
          provide: StatsigService,
          useValue: {
            updateUserAsync: async () => {
              return void 0;
            },
            logEvent: () => {
              void 0;
            },
            getClient: () => {
              return {
                getContext: () => {
                  return { user: {} };
                },
              };
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    redirecter = TestBed.inject(RedirectionService);
    flags = TestBed.inject(StatsigService);
    centralizeUsecase = TestBed.inject(LoginCentralizedUsecase);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should have the logo', () => {
    const logo = fixture.debugElement.query(By.directive(AplazoLogoComponent));

    expect(logo).toBeTruthy();
  });

  it('should have the title', () => {
    const title = fixture.debugElement.query(By.css('h1'));

    expect(title.nativeElement.textContent.trim()).toBe('Login');
  });

  it('should have the login form', () => {
    const form = fixture.debugElement.query(By.directive(LoginFormComponent));

    expect(form).toBeTruthy();
  });

  it('should have 3 redirection buttons', () => {
    const buttons = fixture.debugElement.queryAll(
      By.css('button:not(:is(form  button))')
    );

    expect(buttons.length).toBe(3);
  });

  it('should not have login method called when form is invalid', () => {
    const loginSpy = spyOn(component, 'login');

    const submitButton = fixture.debugElement.query(
      By.css('button:is(form button:last-child)')
    );

    submitButton.nativeElement.click();

    expect(loginSpy).toHaveBeenCalledTimes(0);
  });

  it('should have the login method called when form is valid', () => {
    const loginSpy = spyOn(component, 'login');

    const syntethicEvent = {
      username: 'test',
      password: 'test',
    };

    const formEle = fixture.debugElement.query(
      By.directive(LoginFormComponent)
    );

    formEle.triggerEventHandler('login', syntethicEvent);

    expect(loginSpy).toHaveBeenCalledTimes(1);
    expect(loginSpy).toHaveBeenCalledWith(syntethicEvent);
  });

  it('should do the happy path when login is called', fakeAsync(() => {
    const usecaseSpy = spyOn(centralizeUsecase, 'execute').and.callThrough();
    const internalNavigateSpy = spyOn(
      redirecter,
      'internalNavigation'
    ).and.callThrough();

    const credentials: Credentials = {
      username: '<EMAIL>',
      password: '2323',
    };

    const credentialsCentralized: CredentialsLoginCentralized = {
      merchantUsername: credentials.username,
      merchantPassword: credentials.password,
      authType: 'LOGIN_RETRY',
    };

    component.login(credentials);

    fixture.detectChanges();
    tick();

    expect(usecaseSpy).toHaveBeenCalledTimes(1);
    expect(usecaseSpy).toHaveBeenCalledWith(credentialsCentralized);
    expect(internalNavigateSpy).toHaveBeenCalledTimes(1);
  }));

  it('should cancel the login process when the user is not logged in', fakeAsync(() => {
    const usecaseSpy = spyOn(centralizeUsecase, 'execute').and.callThrough();
    const updateUserAsyncSpy = spyOn(
      flags,
      'updateUserAsync'
    ).and.callThrough();
    const internalNavigateSpy = spyOn(
      redirecter,
      'internalNavigation'
    ).and.callThrough();

    const credentials: Credentials = {
      username: '<EMAIL>',
      password: '23',
    };

    const credentialsCentralized: CredentialsLoginCentralized = {
      merchantUsername: credentials.username,
      merchantPassword: credentials.password,
      authType: 'LOGIN_RETRY',
    };

    component.login(credentials);

    fixture.detectChanges();

    tick();

    expect(usecaseSpy).toHaveBeenCalledTimes(1);
    expect(usecaseSpy).toHaveBeenCalledWith(credentialsCentralized);
    expect(updateUserAsyncSpy).toHaveBeenCalledTimes(0);
    expect(internalNavigateSpy).toHaveBeenCalledTimes(0);
  }));

  it('should navigate to register merchant route', () => {
    const externalNavigateSpy = spyOn(redirecter, 'externalNavigation');

    component.goToRegisterMerchantRoute();

    expect(externalNavigateSpy).toHaveBeenCalledTimes(1);
    expect(externalNavigateSpy).toHaveBeenCalledWith(
      'https://merchant-register.aplazo.net',
      '_blank'
    );
  });

  it('should navigate to customer login route', () => {
    const externalNavigateSpy = spyOn(redirecter, 'externalNavigation');

    component.goToCustomerLoginRoute();

    expect(externalNavigateSpy).toHaveBeenCalledTimes(1);
    expect(externalNavigateSpy).toHaveBeenCalledWith(
      'https://customer.aplazo.net',
      '_blank'
    );
  });

  it('should navigate to forgot password route', () => {
    const internalNavigateSpy = spyOn(redirecter, 'internalNavigation');

    component.goToForgotPasswordRoute();

    expect(internalNavigateSpy).toHaveBeenCalledTimes(1);
    expect(internalNavigateSpy).toHaveBeenCalledWith([
      '/',
      'authorization',
      'forgot-password',
    ]);
  });
});
