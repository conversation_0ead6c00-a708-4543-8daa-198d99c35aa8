import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, take } from 'rxjs';
import { IPaymentBalanceAdjustmentsParamsDto } from 'src/app/features/balance/application/dtos/payment-balance-adjustments-params.dto';
import { IPaymentBalanceAdjustmentsResponseDto } from 'src/app/features/balance/application/dtos/payment-balance-adjustments-response.dto';
import {
  defaultPaymentBalanceAdjustmentsResponse,
  PaymentBalanceAdjustmentUsecase,
} from '../../../../app/features/balance/application/usecases/payment-balance-adjustments.usecase';
import { PaymentBalanceAdjustmentsRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-adjustments.repository';

describe('PaymentBalanceAdjustmentsUseCase', () => {
  let useCase: PaymentBalanceAdjustmentUsecase;
  let repository: jasmine.SpyObj<
    PaymentBalanceAdjustmentsRepository<
      IPaymentBalanceAdjustmentsParamsDto,
      Observable<IPaymentBalanceAdjustmentsResponseDto>
    >
  >;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceAdjustmentUsecase,
        {
          provide: PaymentBalanceAdjustmentsRepository,
          useValue: jasmine.createSpyObj(
            'PaymentBalanceAdjustmentsRepository',
            ['getList']
          ),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
      ],
    });

    useCase = TestBed.inject(PaymentBalanceAdjustmentUsecase);
    repository = TestBed.inject(
      PaymentBalanceAdjustmentsRepository
    ) as jasmine.SpyObj<
      PaymentBalanceAdjustmentsRepository<
        IPaymentBalanceAdjustmentsParamsDto,
        Observable<IPaymentBalanceAdjustmentsResponseDto>
      >
    >;
    const loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should handle error when request has an invalid page number', async () => {
    const args: unknown = {
      pageNum: undefined,
      pageSize: 10,
      adjustmentBalanceId: '27726',
    };

    const result = await lastValueFrom(
      useCase.execute(args as IPaymentBalanceAdjustmentsParamsDto).pipe(take(1))
    );

    expect(result)
      .withContext('should return default response when page number is invalid')
      .toEqual(defaultPaymentBalanceAdjustmentsResponse);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error when request has an negative page number', async () => {
    const args: unknown = {
      pageNum: -1,
      pageSize: 10,
      adjustmentBalanceId: '27726',
    };

    const result = await lastValueFrom(
      useCase.execute(args as IPaymentBalanceAdjustmentsParamsDto).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return default response when page number is less than 0'
      )
      .toEqual(defaultPaymentBalanceAdjustmentsResponse);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);

    expect(repository.getList)
      .withContext(
        'should not call repository.getList when page number is less than 0'
      )
      .toHaveBeenCalledTimes(0);
  });

  it('should handle error when request has an invalid pageSize number', async () => {
    const args: unknown = {
      pageNum: 0,
      pageSize: undefined,
      adjustmentBalanceId: '27726',
    };

    const result = await lastValueFrom(
      useCase.execute(args as IPaymentBalanceAdjustmentsParamsDto).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return default response when pageSize number is invalid'
      )
      .toEqual(defaultPaymentBalanceAdjustmentsResponse);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error when request has an negative pageSize', async () => {
    const args: unknown = {
      pageNum: 0,
      pageSize: -1,
      adjustmentBalanceId: '27726',
    };

    const result = await lastValueFrom(
      useCase.execute(args as IPaymentBalanceAdjustmentsParamsDto).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return default response when page size is less than 0'
      )
      .toEqual(defaultPaymentBalanceAdjustmentsResponse);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);

    expect(repository.getList)
      .withContext(
        'should not call repository.getList when page size is less than 0'
      )
      .toHaveBeenCalledTimes(0);
  });

  it('should handle error when adjustmentBalanceId is undefined', async () => {
    const args: unknown = {
      pageNum: 0,
      pageSize: 10,
      adjustmentBalanceId: undefined,
    };

    const result = await lastValueFrom(
      useCase.execute(args as IPaymentBalanceAdjustmentsParamsDto).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return default response when adjustmentBalanceId is undefined'
      )
      .toEqual(defaultPaymentBalanceAdjustmentsResponse);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);

    expect(repository.getList)
      .withContext(
        'should not call repository.getList when adjustmentBalanceId is undefined'
      )
      .toHaveBeenCalledTimes(0);
  });

  it('should handle error when adjustmentBalanceId is not a valid number', async () => {
    const args: unknown = {
      pageNum: 0,
      pageSize: 10,
      adjustmentBalanceId: 'a12',
    };

    const result = await lastValueFrom(
      useCase.execute(args as IPaymentBalanceAdjustmentsParamsDto).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return default response when adjustmentBalanceId is not a valid number'
      )
      .toEqual(defaultPaymentBalanceAdjustmentsResponse);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);

    expect(repository.getList)
      .withContext(
        'should not call repository.getList when adjustmentBalanceId is not a valid number'
      )
      .toHaveBeenCalledTimes(0);
  });

  it('should complete successfully', async () => {
    const expected = {
      content: [
        {
          id: 1763,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 123308,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-19T11:02:46.421564',
          refundId: 14686,
          amount: 74.26,
        },
        {
          id: 1769,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 124669,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-23T11:11:24.272672',
          refundId: 15626,
          amount: 56.58,
        },
        {
          id: 1764,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 124519,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-22T17:18:45.86707',
          refundId: 15526,
          amount: 45.09,
        },
        {
          id: 1774,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 124807,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-24T11:02:58.978039',
          refundId: 15671,
          amount: 65.42,
        },
        {
          id: 1761,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 123040,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-18T11:10:21.268422',
          refundId: 14635,
          amount: 45.09,
        },
      ],
      number: 0,
      size: 10,
      totalElements: 5,
      totalPages: 1,
      hasContent: true,
      numberOfElements: 5,
      last: true,
      first: true,
    };

    repository.getList.and.returnValue(of(expected));

    const args: IPaymentBalanceAdjustmentsParamsDto = {
      adjustmentBalanceId: '27726',
      pageNum: 0,
      pageSize: 10,
    };

    const result = await lastValueFrom(useCase.execute(args).pipe(take(1)));

    expect(result)
      .withContext(
        'should return a list of adjustments when request is successful'
      )
      .toEqual(expected);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList)
      .withContext('should call once the repository.getList')
      .toHaveBeenCalledTimes(1);
  });
});
