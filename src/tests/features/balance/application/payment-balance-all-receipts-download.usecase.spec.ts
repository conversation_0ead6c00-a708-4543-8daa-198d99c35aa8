import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  NotifierService,
  provideTemporal,
  RawDateDayFirst,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  FileSaverService,
  provideXlsxFileGenerator,
} from '@aplazo/merchant/shared-dash';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { catchError, isEmpty, lastValueFrom, of, take, throwError } from 'rxjs';
import { PaymentBalanceAllReceiptsDownloadUseCase } from 'src/app/features/balance/application/usecases/payment-balance-all-receipts-download.usecase';
import { PaymentBalanceReceiptsRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-receipts.repository';

describe('PaymentBalanceAllReceiptsDownloadUseCase', () => {
  let usecase: PaymentBalanceAllReceiptsDownloadUseCase;
  let repository: jasmine.SpyObj<PaymentBalanceReceiptsRepository>;
  let errorHandler: jasmine.Spy;
  let fileSaver: jasmine.SpyObj<FileSaverService>;

  let notificationWarningSpy: jasmine.Spy;
  let notificationSuccessSpy: jasmine.Spy;

  const request: {
    date: RawDateDayFirst;
    merchantId: number;
  } = {
    date: '01/07/2024',
    merchantId: 199,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceAllReceiptsDownloadUseCase,
        provideUseCaseErrorHandlerTesting(),
        provideNotifierTesting(),
        provideTemporal(),
        provideXlsxFileGenerator(),
        {
          provide: FileSaverService,
          useValue: jasmine.createSpyObj('FileSaverService', ['saveFile']),
        },
        {
          provide: PaymentBalanceReceiptsRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceReceiptsRepository', [
            'byDate',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceAllReceiptsDownloadUseCase);
    repository = TestBed.inject(
      PaymentBalanceReceiptsRepository
    ) as jasmine.SpyObj<PaymentBalanceReceiptsRepository>;
    const errorUsecase = TestBed.inject(UseCaseErrorHandler);
    errorHandler = spyOn(errorUsecase, 'handle').and.callThrough();
    fileSaver = TestBed.inject(
      FileSaverService
    ) as jasmine.SpyObj<FileSaverService>;

    const notifier = TestBed.inject(NotifierService);

    notificationWarningSpy = spyOn(notifier, 'warning').and.callThrough();
    notificationSuccessSpy = spyOn(notifier, 'success').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should throws a RuntimeMerchantError when date is null', async () => {
    const req = {
      ...request,
      date: null,
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext('should throws a RuntimeMerchantError when date is null')
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
    expect(usecase.activeProcessesCount()).toBe(0);
  });

  it('should throws a RuntimeMerchantError when date has an invalid format', async () => {
    const req = {
      ...request,
      date: '01-07-2024',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when date has an invalid format'
      )
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
    expect(usecase.activeProcessesCount()).toBe(0);
  });

  it('should throws a RuntimeMechantError when date is not a valid date', async () => {
    const req = {
      ...request,
      date: '01/22/2024',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when date is not a valid date'
      )
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
    expect(usecase.activeProcessesCount()).toBe(0);
  });

  it('should complete without stream when an active process is already running', async () => {
    usecase.addActiveProcess(request.date);

    const isCompleteWithoutStrem = await lastValueFrom(
      usecase.execute(request).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStrem)
      .withContext(
        'should complete without stream when an active process is already running'
      )
      .toBeTrue();
    expect(usecase.activeProcessesCount()).toBe(1);
    expect(errorHandler)
      .withContext(
        'should not call the error handler because the stream is empty'
      )
      .toHaveBeenCalledTimes(0);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should show a warning notification and throws a RuntimeMerchantError when the repository returns a 404 error', async () => {
    repository.byDate.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: new Blob([JSON.stringify({ error: 'No files found' })], {
              type: 'application/json',
            }),
            status: 404,
            statusText: 'Not Found',
          })
      )
    );

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(request).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should show a warning notification and throws a RuntimeMerchantError when the repository returns a 404 error'
      )
      .toBe(expectedTestMessage);
    expect(repository.byDate).toHaveBeenCalledTimes(1);
    expect(errorHandler)
      .withContext(
        'should not call the error handler because error is throwing instead'
      )
      .toHaveBeenCalledTimes(0);
    expect(notificationWarningSpy).toHaveBeenCalledTimes(1);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
    expect(usecase.activeProcessesCount()).toBe(0);
  });

  it('should show success notification and save the file when the repository returns a file', async () => {
    const blob = new Blob([''], { type: 'application/pdf' });

    repository.byDate.and.returnValue(of(blob));
    fileSaver.saveFile.and.returnValue(of(void 0) as any);

    const result = await lastValueFrom(usecase.execute(request).pipe(take(1)));

    expect(result)
      .withContext(
        'should show success notification and save the file when the repository returns a file'
      )
      .toBeTruthy();
    expect(repository.byDate).toHaveBeenCalledTimes(1);
    expect(repository.byDate)
      .withContext(
        'should call the repository with a date in the format YYYY-MM'
      )
      .toHaveBeenCalledWith({
        date: '2024-07',
      });
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(1);
    expect(fileSaver.saveFile).toHaveBeenCalledTimes(1);
    expect(fileSaver.saveFile).toHaveBeenCalledWith(
      blob,
      `[RECEIPTS]_M${request.merchantId}_${request.date}.zip`
    );
    expect(usecase.activeProcessesCount()).toBe(0);
    expect(errorHandler).toHaveBeenCalledTimes(0);
  });
});
