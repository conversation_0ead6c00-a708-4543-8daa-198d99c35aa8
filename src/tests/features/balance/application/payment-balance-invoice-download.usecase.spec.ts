import { TestBed } from '@angular/core/testing';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { FileSaverService } from '@aplazo/merchant/shared-dash';
import {
  provideDateCalculatorTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { catchError, isEmpty, lastValueFrom, of, take } from 'rxjs';
import { DownloadOneUIRequest } from '../../../../app/features/balance/application/usecases/download-one';
import { PaymentBalanceInvoiceDownloadByIdUseCase } from '../../../../app/features/balance/application/usecases/payment-balance-invoice-download.usecase';
import { PaymentBalanceInvoiceRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-invoice.repository';

describe('PaymentBalanceInvoiceDownloaByIdUseCase', () => {
  let usecase: PaymentBalanceInvoiceDownloadByIdUseCase;
  let repository: jasmine.SpyObj<PaymentBalanceInvoiceRepository>;
  let notifySuccessSpy: jasmine.Spy;
  let fileSaver: jasmine.SpyObj<FileSaverService>;
  let errorHandlerSpy: jasmine.Spy;

  const request: DownloadOneUIRequest = {
    merchantId: 199,
    date: '2024-07-01',
    balanceId: 123,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceInvoiceDownloadByIdUseCase,
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        provideDateCalculatorTesting(),
        {
          provide: PaymentBalanceInvoiceRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceInvoiceRepository', [
            'byId',
          ]),
        },
        {
          provide: FileSaverService,
          useValue: jasmine.createSpyObj('FileSaverService', ['saveFile']),
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceInvoiceDownloadByIdUseCase);
    repository = TestBed.inject(
      PaymentBalanceInvoiceRepository
    ) as jasmine.SpyObj<PaymentBalanceInvoiceRepository>;
    const notifier = TestBed.inject(NotifierService);
    notifySuccessSpy = spyOn(notifier, 'success');
    fileSaver = TestBed.inject(
      FileSaverService
    ) as jasmine.SpyObj<FileSaverService>;
    errorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(PaymentBalanceInvoiceDownloadByIdUseCase);
  });

  it('should throws an error when merchantId is not provided', async () => {
    const req = { ...request, merchantId: undefined };
    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when merchantId is undefined'
      )
      .toBe(expectedTestMessage);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throws an error when balanceId is not a number', async () => {
    const req = {
      ...request,
      balanceId: '123',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when receiptId is not a number'
      )
      .toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throws an error when date is not in a valid format', async () => {
    const req = {
      ...request,
      date: '01/22/2024',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when date is not in a valid format'
      )
      .toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throws an error when date is valid format but is not valid at all', async () => {
    const req = {
      ...request,
      date: '2024-22-01',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when date is in a format but is not valid at all'
      )
      .toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when an active process is already running', async () => {
    usecase.addActiveProcess(request.balanceId);

    const isCompleteWithoutStream = await lastValueFrom(
      usecase.execute(request).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext(
        'should complete without stream when an active process is already running'
      )
      .toBeTrue();
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should show success notification and save the file when the repository returns a file', async () => {
    const blob = new Blob([''], { type: 'application/zip' });

    repository.byId.and.returnValue(of(blob));
    fileSaver.saveFile.and.returnValue(of(void 0) as any);

    const result = await lastValueFrom(usecase.execute(request).pipe(take(1)));

    expect(result).toBeTruthy();
    expect(repository.byId).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledWith({
      balanceId: request.balanceId,
    });
    expect(notifySuccessSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  });
});
