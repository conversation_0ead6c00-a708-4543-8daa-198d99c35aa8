import { TestBed } from '@angular/core/testing';
import {
  DateCalculator,
  LoaderService,
  provideTemporal,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalDateCalculator,
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, take } from 'rxjs';
import { IPaymentBalanceListRepositoryParamsDto } from 'src/app/features/balance/application/dtos/payment-balance-list-repository-params.dto';
import { PaymentBalanceListResponseUIDto } from 'src/app/features/balance/application/dtos/payment-balance-list-response-ui.dto';
import { PaymentBalanceListRepository } from 'src/app/features/balance/domain/repositories/payment-balance-list.repository';
import { IPaymentBalanceListRequestUIDto } from '../../../../app/features/balance/application/dtos/payment-balance-list-request-ui.dto';
import {
  PaymentBalanceListUsecase,
  responseWithEmptyContent,
} from '../../../../app/features/balance/application/usecases/payment-balance-list.usecase';
const successfulResponse = {
  content: [
    {
      id: 26941,
      paymentDate: '2024-07-11',
      saleAmount: 1600,
      adjustment: 0,
      feeAmount: 66.42,
      payAmount: 1533.58,
      finalAmount: 1533.58,
      status: 'PROCESS',
      loans: 6,
      adjustments: 0,
      current: 'MXN',
    },
    {
      id: 27732,
      paymentDate: '2024-07-25',
      saleAmount: 5534,
      adjustment: 0,
      feeAmount: 229.66,
      payAmount: 5304.34,
      finalAmount: 5304.34,
      status: 'PROCESS',
      loans: 22,
      adjustments: 0,
      current: 'MXN',
    },
    {
      id: 26549,
      paymentDate: '2024-07-04',
      saleAmount: 4632,
      adjustment: -47.93,
      feeAmount: 192.24,
      payAmount: 4439.76,
      finalAmount: 4391.83,
      status: 'PROCESS',
      loans: 22,
      adjustments: 1,
      current: 'MXN',
    },
    {
      id: 27335,
      paymentDate: '2024-07-18',
      saleAmount: 100,
      adjustment: 200,
      feeAmount: 100,
      payAmount: 100,
      finalAmount: 100,
      status: 'PROCESS',
      loans: 100,
      adjustments: 100,
      current: 'MXN',
    },
  ],
  number: 0,
  size: 10,
  totalElements: 4,
  totalPages: 1,
  hasContent: true,
  numberOfElements: 4,
  last: true,
  first: true,
};

describe('PaymentBalanceListUsecase', () => {
  let usecase: PaymentBalanceListUsecase;
  let repository: jasmine.SpyObj<
    PaymentBalanceListRepository<
      IPaymentBalanceListRepositoryParamsDto,
      Observable<PaymentBalanceListResponseUIDto>
    >
  >;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let temporal: TemporalService;

  const request: IPaymentBalanceListRequestUIDto = {
    status: 'process',
    date: '08/08/2024',
    pageNum: 0,
    pageSize: 10,
    dynamicInitialDateToBalances: null,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceListUsecase,
        provideTemporal(),
        {
          provide: DateCalculator,
          useClass: LocalDateCalculator,
        },
        {
          provide: PaymentBalanceListRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceListRepository', [
            'getList',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceListUsecase);
    repository = TestBed.inject(PaymentBalanceListRepository) as jasmine.SpyObj<
      PaymentBalanceListRepository<
        IPaymentBalanceListRepositoryParamsDto,
        Observable<PaymentBalanceListResponseUIDto>
      >
    >;
    temporal = TestBed.inject(TemporalService);
    const loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should handle error when request has a date that is undefined', async () => {
    const requestWithUndefinedDate = {
      ...request,
      date: undefined,
    };

    const result = await lastValueFrom(
      usecase.execute(requestWithUndefinedDate).pipe(take(1))
    );

    expect(result)
      .withContext('should return a default response when date is undefined')
      .toEqual(responseWithEmptyContent);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when request has a date with incorrect format [RawDateDayFirst]', async () => {
    const requestWithIncorrectDate = {
      ...request,
      date: '2024-05-20',
    };

    const result = await lastValueFrom(
      usecase.execute(requestWithIncorrectDate as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when date has incorrect format'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when request has an initial date that is invalid', async () => {
    const requestWithIncorrectDate = {
      ...request,
      initialDateToBalances: '2024-05-20',
      date: temporal.todayRawDayFirst,
    };

    const result = await lastValueFrom(
      usecase.execute(requestWithIncorrectDate as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when initialDateToBalances has incorrect format'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when today is before the initial date', async () => {
    const dayAfterTomorrow = new Date();
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    const testRequest = {
      ...request,
      initialDateToBalances: dayAfterTomorrow,
      date: temporal.todayRawDayFirst,
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when today is before the initial date'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when dynamic date is null', async () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const testRequest = {
      ...request,
      initialDateToBalances: yesterday,
      dynamicInitialDateToBalances: null,
      date: temporal.todayRawDayFirst,
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext('should return a default response when dynamic date is null')
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when dynamic date has an invalid format', async () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const testRequest = {
      ...request,
      initialDateToBalances: yesterday,
      dynamicInitialDateToBalances: '01/02/2024',
      date: temporal.todayRawDayFirst,
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when dynamic date has invalid format'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when today is before dynamicDate', async () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dayAfterTomorrow = new Date();
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
    const rawDateAfterTomorrow =
      temporal.formatRawDateYearFirstWithHyphen(dayAfterTomorrow);

    const testRequest = {
      ...request,
      date: '01/02/2024',
      initialDateToBalances: yesterday,
      dynamicInitialDateToBalances: rawDateAfterTomorrow,
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when today is before dynamicDate'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when requested date is before the month of initialDate', async () => {
    const previousMonth = new Date();
    previousMonth.setMonth(previousMonth.getMonth() - 3);

    const testRequest = {
      ...request,
      date: temporal.formatRawDateDayFirst(previousMonth),
      initialDateToBalances: temporal.yesterday,
      dynamicInitialDateToBalances: temporal.formatRawDateYearFirstWithHyphen(
        temporal.yesterday
      ),
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when requested date is before the month of initialDate'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when requested date is before the month of dynamicDate', async () => {
    const previousMonth = new Date();
    previousMonth.setMonth(previousMonth.getMonth() - 3);

    const testRequest = {
      ...request,
      date: temporal.formatRawDateDayFirst(previousMonth),
      initialDateToBalances: previousMonth,
      dynamicInitialDateToBalances: temporal.formatRawDateYearFirstWithHyphen(
        temporal.yesterday
      ),
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext(
        'should return a default response when requested date is before the month of dynamicDate'
      )
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when status is invalid', async () => {
    const testRequest = {
      ...request,
      date: temporal.todayRawDayFirst,
      initialDateToBalances: temporal.yesterday,
      dynamicInitialDateToBalances: temporal.formatRawDateYearFirstWithHyphen(
        temporal.yesterday
      ),
      status: 'invalid',
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext('should return a default response when status is invalid')
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should handle error when pageNum is invalid', async () => {
    const testRequest = {
      ...request,
      date: temporal.todayRawDayFirst,
      initialDateToBalances: temporal.yesterday,
      dynamicInitialDateToBalances: temporal.formatRawDateYearFirstWithHyphen(
        temporal.yesterday
      ),
      pageNum: '1',
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest as any).pipe(take(1))
    );

    expect(result)
      .withContext('should return a default response when pageNum is invalid')
      .toEqual(responseWithEmptyContent);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(0);
  });

  it('should return a successful response', async () => {
    repository.getList.and.returnValue(of(successfulResponse as any));

    const testRequest = {
      ...request,
      date: temporal.todayRawDayFirst,
      initialDateToBalances: temporal.yesterday,
      dynamicInitialDateToBalances: temporal.formatRawDateYearFirstWithHyphen(
        temporal.yesterday
      ),
    };

    const result = await lastValueFrom(
      usecase.execute(testRequest).pipe(take(1))
    );

    expect(result)
      .withContext('should return a successful response')
      .toEqual(successfulResponse as any);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledTimes(1);
  });
});
