import { TestBed } from '@angular/core/testing';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { FileSaverService } from '@aplazo/merchant/shared-dash';
import {
  provideDateCalculatorTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { catchError, isEmpty, lastValueFrom, of, take } from 'rxjs';
import { DownloadOneUIRequest } from '../../../../app/features/balance/application/usecases/download-one';
import { PaymentBalanceReceiptDownloadUseCase } from '../../../../app/features/balance/application/usecases/payment-balance-receipt-download.usecase';
import { PaymentBalanceReceiptsRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-receipts.repository';

describe('PaymentBalanceReceiptDownloadUseCase', () => {
  let usecase: PaymentBalanceReceiptDownloadUseCase;
  let repository: jasmine.SpyObj<PaymentBalanceReceiptsRepository>;
  let fileSaver: jasmine.SpyObj<FileSaverService>;
  let errorHandler: jasmine.Spy;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let notificationWarningSpy: jasmine.Spy;
  let notificationSuccessSpy: jasmine.Spy;

  const request: DownloadOneUIRequest = {
    merchantId: 199,
    date: '2024-07-01',
    balanceId: 123,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceReceiptDownloadUseCase,
        provideUseCaseErrorHandlerTesting(),
        provideNotifierTesting(),
        provideDateCalculatorTesting(),
        {
          provide: FileSaverService,
          useValue: jasmine.createSpyObj('FileSaverService', ['saveFile']),
        },
        {
          provide: PaymentBalanceReceiptsRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceReceiptsRepository', [
            'byId',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceReceiptDownloadUseCase);
    repository = TestBed.inject(
      PaymentBalanceReceiptsRepository
    ) as jasmine.SpyObj<PaymentBalanceReceiptsRepository>;
    const errorUsecase = TestBed.inject(UseCaseErrorHandler);
    errorHandler = spyOn(errorUsecase, 'handle').and.callThrough();
    fileSaver = TestBed.inject(
      FileSaverService
    ) as jasmine.SpyObj<FileSaverService>;

    const notifier = TestBed.inject(NotifierService);

    notificationWarningSpy = spyOn(notifier, 'warning').and.callThrough();
    notificationSuccessSpy = spyOn(notifier, 'success').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should throws an error when merchantId is undefined', async () => {
    const req = {
      ...request,
      merchantId: undefined,
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when merchantId is undefined'
      )
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throws an error when balanceId is not a number', async () => {
    const req = {
      ...request,
      balanceId: '123',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when balanceId is not a number'
      )
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throws an error when date is not in a valid format', async () => {
    const req = {
      ...request,
      date: '01/22/2024',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when date is not in a valid format'
      )
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throws an error when date is valid format but is not valid at all', async () => {
    const req = {
      ...request,
      date: '2024-22-01',
    } as any;

    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result)
      .withContext(
        'should throws a RuntimeMerchantError when date is in a format but is not valid at all'
      )
      .toBe(expectedTestMessage);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when an active process is already running', async () => {
    usecase.addActiveProcess(request.balanceId);

    const isCompleteWithoutStream = await lastValueFrom(
      usecase.execute(request).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext(
        'should complete without stream when an active process is already running'
      )
      .toBeTrue();
    expect(errorHandler).toHaveBeenCalledTimes(0);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should show success notification and save the file when the repository returns a file', async () => {
    const blob = new Blob([''], { type: 'application/pdf' });

    repository.byId.and.returnValue(of(blob));
    fileSaver.saveFile.and.returnValue(of(void 0) as any);

    const result = await lastValueFrom(usecase.execute(request).pipe(take(1)));

    expect(result).toBeTruthy();
    expect(repository.byId).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledWith({
      balanceId: 123,
    });
    expect(notificationSuccessSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
  });
});
