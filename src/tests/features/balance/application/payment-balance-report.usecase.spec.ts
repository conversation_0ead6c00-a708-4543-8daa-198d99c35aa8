import { TestBed } from '@angular/core/testing';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
  provideCsvMapper,
} from '@aplazo/merchant/shared-dash';
import {
  provideDateCalculatorTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { catchError, lastValueFrom, of, take } from 'rxjs';
import { DownloadOneUIRequest } from '../../../../app/features/balance/application/usecases/download-one';
import { PaymentBalanceReportUseCase } from '../../../../app/features/balance/application/usecases/payment-balance-report.usecase';
import { PaymentBalanceTransctionsRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-transactions.repository';

describe('PaymentBalanceReportUseCase', () => {
  let usecase: PaymentBalanceReportUseCase;
  let repository: jasmine.SpyObj<PaymentBalanceTransctionsRepository>;
  let downloader: jasmine.SpyObj<FileGeneratorService<Promise<string[][]>>>;
  let csvMapperSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;
  let notifyInfoSpy: jasmine.Spy;
  let notifySuccessSpy: jasmine.Spy;

  const request: DownloadOneUIRequest = {
    merchantId: 1,
    balanceId: 1,
    date: '2024-07-01',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceReportUseCase,
        provideNotifierTesting(),
        provideCsvMapper(),
        provideDateCalculatorTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: PaymentBalanceTransctionsRepository,
          useValue: jasmine.createSpyObj(
            'PaymentBalanceTransctionsRepository',
            ['byId']
          ),
        },
        {
          provide: FileGeneratorService,
          useValue: jasmine.createSpyObj('FileGeneratorService', [
            'generateFileAndDownload',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceReportUseCase);
    repository = TestBed.inject(
      PaymentBalanceTransctionsRepository
    ) as jasmine.SpyObj<PaymentBalanceTransctionsRepository>;
    downloader = TestBed.inject(FileGeneratorService) as jasmine.SpyObj<
      FileGeneratorService<Promise<string[][]>>
    >;
    csvMapperSpy = spyOn(
      TestBed.inject(CsvMapperService),
      'transform'
    ).and.callThrough();
    errorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    notifyInfoSpy = spyOn(notifier, 'info').and.callThrough();
    notifySuccessSpy = spyOn(notifier, 'success').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(PaymentBalanceReportUseCase);
  });

  it('should throw an error when balanceId is null', async () => {
    const req = { ...request, balanceId: null } as any;
    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result).toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
  });

  it('should throw an error when balanceId is not a number', async () => {
    const req = { ...request, balanceId: '1' } as any;
    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result).toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
  });

  it('should throw an error when initialDateToBalances is not a valid date', async () => {
    const req = {
      ...request,
      initialDateToBalances: 'invalid date',
    } as any;
    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result).toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
  });

  it('should throw an error when initialDateToBalances is at least one day after from today', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const req = {
      ...request,
      initialDateToBalances: tomorrow,
    };
    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(req).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result).toBe(expectedTestMessage);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.byId).toHaveBeenCalledTimes(0);
    expect(notifyInfoSpy).toHaveBeenCalledTimes(0);
  });

  it('should trigger csv transformation and download file on success response', async () => {
    const content = 'col1,col2\nval1,val2\nval3,val4';
    const expected = [
      ['col1', 'col2'],
      ['val1', 'val2'],
      ['val3', 'val4'],
    ];

    repository.byId.and.returnValue(of(content));
    downloader.generateFileAndDownload.and.returnValue(
      Promise.resolve(expected)
    );

    const response = await lastValueFrom(
      usecase.execute(request).pipe(take(1))
    );

    expect(response).toEqual(expected);
    expect(csvMapperSpy).toHaveBeenCalledTimes(1);
    expect(csvMapperSpy).toHaveBeenCalledWith(content);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(1);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledWith(
      expected,
      `[TRANSACTION]_M${request.merchantId}_${request.date}`
    );
    expect(notifyInfoSpy).toHaveBeenCalledTimes(1);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  });

  it('should complete with an empty array when response is empty', async () => {
    repository.byId.and.returnValue(of(''));
    const expectedTestMessage = 'Controlled RuntimeMerchantError';

    const result = await lastValueFrom(
      usecase.execute(request).pipe(
        take(1),
        catchError(err => {
          if (err instanceof RuntimeMerchantError) {
            return of(expectedTestMessage);
          }

          return of('Uncontrolled Error');
        })
      )
    );

    expect(result).toBe(expectedTestMessage);
    expect(csvMapperSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(notifyInfoSpy).toHaveBeenCalledTimes(1);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });
});
