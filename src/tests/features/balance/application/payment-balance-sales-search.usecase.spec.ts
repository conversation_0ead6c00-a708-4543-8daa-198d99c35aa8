import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  Transaction,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, Observable, take, throwError } from 'rxjs';
import { IPaymentBalanceSalesSearchRepositoryParams } from 'src/app/features/balance/application/dtos/payment-balance-sales-search-repository-params.dto';
import { PaymentBalanceSalesSearchRepository } from 'src/app/features/balance/domain/repositories/payment-balance-sales-search.repository';
import { IPaymentBalanceSalesSearchRequestUiDto } from '../../../../app/features/balance/application/dtos/payment-balance-sales-search-request-ui.dto';
import { PaymentBalanceSalesSearchUsecase } from '../../../../app/features/balance/application/usecases/payment-balance-sales-search.usecase';

describe('PaymentBalanceSalesSearchUseCase', () => {
  let usecase: PaymentBalanceSalesSearchUsecase;
  let repository: jasmine.SpyObj<
    PaymentBalanceSalesSearchRepository<
      IPaymentBalanceSalesSearchRepositoryParams,
      Observable<Transaction[]>
    >
  >;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorSpy: jasmine.Spy;

  const request: IPaymentBalanceSalesSearchRequestUiDto = {
    dateRange: {
      startDate: '03/08/2024',
      endDate: '10/08/2024',
    },
    search: '1234',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceSalesSearchUsecase,
        {
          provide: PaymentBalanceSalesSearchRepository,
          useValue: jasmine.createSpyObj(
            'PaymentBalanceSalesSearchRepository',
            ['search']
          ),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    usecase = TestBed.inject(PaymentBalanceSalesSearchUsecase);
    repository = TestBed.inject(
      PaymentBalanceSalesSearchRepository
    ) as jasmine.SpyObj<
      PaymentBalanceSalesSearchRepository<
        IPaymentBalanceSalesSearchRepositoryParams,
        Observable<Transaction[]>
      >
    >;
    const loader = TestBed.inject(LoaderService);

    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(PaymentBalanceSalesSearchUsecase);
  });

  it('should complete without stream when no request is received', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      usecase.execute(null).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when dateRange is missing', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      usecase.execute({ ...request, dateRange: null }).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when startDate is missing', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      usecase
        .execute({
          ...request,
          dateRange: { ...request.dateRange, startDate: null },
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when endDate is missing', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      usecase
        .execute({
          ...request,
          dateRange: { ...request.dateRange, endDate: null },
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when startDate is an invalid date', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      usecase
        .execute({
          ...request,
          dateRange: {
            ...request.dateRange,
            // @ts-expect-error: not a valid date
            startDate: '01-01-2024',
          },
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when endDate is an invalid date', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      usecase
        .execute({
          ...request,
          dateRange: {
            ...request.dateRange,
            // @ts-expect-error: not a valid date
            endDate: '01-01-2024',
          },
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(0);
  });

  it('should return an empty array of transaction when repository fails', async () => {
    repository.search.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: 'Internal Server Error',
            statusText: 'Internal Server Error',
          })
      )
    );

    const transactions = await lastValueFrom(
      usecase.execute(request).pipe(take(1))
    );

    expect(transactions).toEqual([]);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.search).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });
});
