import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, Observable, take, throwError } from 'rxjs';
import { PaymentBalanceSalesUIDto } from '../../../../app/features/balance/application/dtos/payment-balance-sales-params.dto';
import { IPaymentBalanceSalesResponseDto } from '../../../../app/features/balance/application/dtos/payment-balance-sales-response.dto';
import {
  defaultBalanceSalesResponse,
  PaymentBalanceSalesUsecase,
} from '../../../../app/features/balance/application/usecases/payment-balance-sales.usecase';
import { PaymentBalanceSalesRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-sales.repository';

describe('PaymentBalanceSalesUseCase', () => {
  let usecase: PaymentBalanceSalesUsecase;
  let repository: jasmine.SpyObj<
    PaymentBalanceSalesRepository<
      PaymentBalanceSalesUIDto,
      Observable<IPaymentBalanceSalesResponseDto>
    >
  >;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorSpy: jasmine.Spy;

  const request = {
    pageNum: 0,
    pageSize: 10,
    salesBalanceId: 123,
  } satisfies PaymentBalanceSalesUIDto;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceSalesUsecase,
        {
          provide: PaymentBalanceSalesRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceSalesRepository', [
            'getList',
          ]),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    usecase = TestBed.inject(PaymentBalanceSalesUsecase);
    repository = TestBed.inject(
      PaymentBalanceSalesRepository
    ) as jasmine.SpyObj<
      PaymentBalanceSalesRepository<
        PaymentBalanceSalesUIDto,
        Observable<IPaymentBalanceSalesResponseDto>
      >
    >;
    const loader = TestBed.inject(LoaderService);
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();

    usecaseErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(PaymentBalanceSalesUsecase);
  });

  it('should complete without stream when salesBalanceId is null', async () => {
    const req = {
      ...request,
      salesBalanceId: null,
    } as any;

    const isCompletedWithoutEmission = await lastValueFrom(
      usecase.execute(req).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(repository.getList).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should complete without stream when pageNum is not a valid number', async () => {
    const req = {
      ...request,
      pageNum: '1',
    } as any;

    const isCompletedWithoutEmission = await lastValueFrom(
      usecase.execute(req).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(repository.getList).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should complete without stream when pageSize is not a valid number', async () => {
    const req = {
      ...request,
      pageSize: '10',
    } as any;

    const isCompletedWithoutEmission = await lastValueFrom(
      usecase.execute(req).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(repository.getList).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });
  it('should return an empty content for payment balance sales response when repository fails', async () => {
    repository.getList.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
            error: { message: 'Internal Server Error' },
          })
      )
    );

    const response = await lastValueFrom(
      usecase.execute(request).pipe(take(1))
    );

    expect(response).toEqual(defaultBalanceSalesResponse);
  });
});
