import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, Observable, of, take, throwError } from 'rxjs';
import {
  defaultEmptySearchResponse,
  PaymentBalanceSearchUsecase,
} from '../../../../app/features/balance/application/usecases/payment-balance-search.usecase';
import { PaymentBalance } from '../../../../app/features/balance/domain/entities/payment-balance';
import { PaymentBalanceSearchRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-search.repository';

describe('PaymentBalanceSearchUseCase', () => {
  let useCase: PaymentBalanceSearchUsecase;
  let repository: jasmine.SpyObj<
    PaymentBalanceSearchRepository<Observable<PaymentBalance>>
  >;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: PaymentBalanceSearchRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceSearchRepository', [
            'getPaymentById',
          ]),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    useCase = TestBed.inject(PaymentBalanceSearchUsecase);
    repository = TestBed.inject(
      PaymentBalanceSearchRepository
    ) as jasmine.SpyObj<
      PaymentBalanceSearchRepository<Observable<PaymentBalance>>
    >;
    const loader = TestBed.inject(LoaderService);
    const usecaseError = TestBed.inject(UseCaseErrorHandler);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(usecaseError, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
    expect(useCase).toBeInstanceOf(PaymentBalanceSearchUsecase);
  });

  it('should complete stream without emission when id is null', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      useCase.execute(null).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should complete stream without emission when id is negative', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      useCase.execute(-1).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.getPaymentById).toHaveBeenCalledTimes(0);
  });

  it('should complete stream without emission when id is not a number', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      useCase
        .execute(
          // @ts-expect-error: 'a' is not a number
          'a'
        )
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.getPaymentById).toHaveBeenCalledTimes(0);
  });

  it('should complete stream without emission when id is a decimal number', async () => {
    const isCompletedWithoutEmission = await lastValueFrom(
      useCase.execute(1.5).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutEmission).toBeTrue();
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.getPaymentById).toHaveBeenCalledTimes(0);
  });

  it('should return a default empty search response when repository returns null', async () => {
    repository.getPaymentById.and.returnValue(of(null));

    const response = await lastValueFrom(useCase.execute(1).pipe(take(1)));

    expect(response).toEqual(defaultEmptySearchResponse);
    expect(repository.getPaymentById).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should return a default empty search response when repository throws an error', async () => {
    repository.getPaymentById.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
            error: { message: 'Internal Server Error' },
          })
      )
    );

    const response = await lastValueFrom(useCase.execute(1).pipe(take(1)));

    expect(response).toEqual(defaultEmptySearchResponse);
    expect(repository.getPaymentById).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should return PaymentBalance when repository returns a valid response', async () => {
    const response: PaymentBalance = {
      id: 28533,
      paymentDate: '2024-08-08',
      saleAmount: 9973,
      adjustment: 0,
      feeAmount: 413.94,
      payAmount: 9559.06,
      finalAmount: 9559.06,
      status: 'PROCESS',
      loans: 29,
      adjustments: 0,
      current: 'MXN',
      statusInvoice: 'PROCESS',
      statusReceipt: 'AVAILABLE',
    };

    repository.getPaymentById.and.returnValue(of(response));

    const result = await lastValueFrom(useCase.execute(28533).pipe(take(1)));

    expect(result).toEqual([response]);
    expect(repository.getPaymentById).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should call repository with the correct id', async () => {
    const id = 28533;

    repository.getPaymentById.and.returnValue(of(null));

    await lastValueFrom(useCase.execute(id).pipe(take(1)));

    expect(repository.getPaymentById).toHaveBeenCalledTimes(1);
    expect(repository.getPaymentById).toHaveBeenCalledWith(id);
  });
});
