import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  DateCalculator,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalDateCalculator,
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, take, throwError } from 'rxjs';
import { IPaymentBalanceStatsRepositoryParamsDto } from '../../../../app/features/balance/application/dtos/payment-balance-stats-repository-params.dto';
import { IPaymentBalanceStatsRequestUIDto } from '../../../../app/features/balance/application/dtos/payment-balance-stats-request.dto';
import {
  emptyBalanceStats,
  PaymentBalanceStatsUseCase,
} from '../../../../app/features/balance/application/usecases/payment-balance-stats.usecase';
import { PaymentBalanceStats } from '../../../../app/features/balance/domain/entities/payment-balance-stats';
import { PaymentBalanceStatsRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-stats.repository';

describe('PaymentBalanceStatsUsecase', () => {
  let usecase: PaymentBalanceStatsUseCase;
  let repository: jasmine.SpyObj<
    PaymentBalanceStatsRepository<
      IPaymentBalanceStatsRepositoryParamsDto,
      Observable<PaymentBalanceStats>
    >
  >;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let errorHandler: jasmine.Spy;

  const request: IPaymentBalanceStatsRequestUIDto = {
    status: 'process',
    date: '05/08/2024',
    dynamicInitialDateToBalances: '2024-08-01',
    initialDateToBalances: new Date('2024-08-01'),
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceStatsUseCase,
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: DateCalculator,
          useClass: LocalDateCalculator,
        },
        {
          provide: PaymentBalanceStatsRepository,
          useValue: jasmine.createSpyObj('PaymentBalanceStatsRepository', [
            'getStats',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceStatsUseCase);
    repository = TestBed.inject(
      PaymentBalanceStatsRepository
    ) as jasmine.SpyObj<
      PaymentBalanceStatsRepository<
        IPaymentBalanceStatsRepositoryParamsDto,
        Observable<PaymentBalanceStats>
      >
    >;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show');
    hideLoaderSpy = spyOn(loader, 'hide');
    errorHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(PaymentBalanceStatsUseCase);
  });

  it('should return a defaultResponse when initialDateToBalances is an invalid date', async () => {
    const requestWithInvalidDate = {
      ...request,
      initialDateToBalances: new Date('invalid-date'),
    };
    const response = await lastValueFrom(
      usecase.execute(requestWithInvalidDate).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when today is before initialDateToBalances', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const reqWithInitialAsTomorrow = {
      ...request,
      initialDateToBalances: tomorrow,
    };

    const response = await lastValueFrom(
      usecase.execute(reqWithInitialAsTomorrow).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when dynamicInitialDateToBalances is null', async () => {
    const reqWithNullDynamic = {
      ...request,
      dynamicInitialDateToBalances: null,
    };

    const response = await lastValueFrom(
      usecase.execute(reqWithNullDynamic).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when dynamicInitialDateToBalances is an invalid date', async () => {
    const reqWithInvalidDynamic = {
      ...request,
      dynamicInitialDateToBalances: 'invalid-date',
    } as any;

    const response = await lastValueFrom(
      usecase.execute(reqWithInvalidDynamic).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when today is before dynamicInitialDateToBalances', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const reqWithDynamicAsTomorrow = {
      ...request,
      dynamicInitialDateToBalances: tomorrow,
    } as any;

    const response = await lastValueFrom(
      usecase.execute(reqWithDynamicAsTomorrow).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when date is invalid', async () => {
    const reqWithInvalidDate = {
      ...request,
      date: 'invalid-date',
    } as any;

    const response = await lastValueFrom(
      usecase.execute(reqWithInvalidDate).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when date is before initialDateToBalance', async () => {
    const date = '08/07/2024';
    const dateAfter = new Date('2024/08/01');

    const reqWithDateAsTomorrow = {
      ...request,
      date,
      initialDateToBalances: dateAfter,
    } as any;

    const response = await lastValueFrom(
      usecase.execute(reqWithDateAsTomorrow).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when date is before dynamicInitialDateToBalances', async () => {
    const date = '08/07/2024';

    const reqWithDateAsTomorrow = {
      ...request,
      date,
      dynamicInitialDateToBalances: '2024-08-01',
      initialDateToBalances: new Date('2024/07/01'),
    } as any;

    const response = await lastValueFrom(
      usecase.execute(reqWithDateAsTomorrow).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when status is invalid', async () => {
    const reqWithInvalidStatus = {
      ...request,
      status: 'invalid-status',
    } as any;

    const response = await lastValueFrom(
      usecase.execute(reqWithInvalidStatus).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(0);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when repository throws an error', async () => {
    repository.getStats.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
            error: 'Internal Server Error',
          })
      )
    );

    const response = await lastValueFrom(
      usecase.execute(request).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a defaultResponse when repository returns null', async () => {
    repository.getStats.and.returnValue(null);

    const response = await lastValueFrom(
      usecase.execute(request).pipe(take(1))
    );

    expect(response).toEqual(emptyBalanceStats);
    expect(repository.getStats).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(1);
  });

  it('should return a success response when all parameters are valid', async () => {
    const response = {
      totalSales: 9973,
      totalAdjustment: 0,
      totalPay: 9559.06,
      totalFee: 413.94,
    };

    repository.getStats.and.returnValue(of(response));

    const result = await lastValueFrom(usecase.execute(request).pipe(take(1)));

    expect(result).toEqual(response);
    expect(repository.getStats).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
  });
});
