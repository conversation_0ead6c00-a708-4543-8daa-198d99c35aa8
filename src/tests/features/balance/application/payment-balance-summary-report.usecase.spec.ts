import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  NotifierService,
  provideTemporal,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  FileGeneratorService,
  provideCsvMapper,
} from '@aplazo/merchant/shared-dash';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { catchError, isEmpty, lastValueFrom, of, take, throwError } from 'rxjs';
import { DownloadAllUIRequest } from '../../../../app/features/balance/application/usecases/download-all';
import { PaymentBalanceSummaryReportUsecase } from '../../../../app/features/balance/application/usecases/payment-balance-summary-report.usecase';
import { PaymentBalanceTransctionsRepository } from '../../../../app/features/balance/domain/repositories/payment-balance-transactions.repository';

describe('PaymentBalanceSummaryReportUsecase', () => {
  let usecase: PaymentBalanceSummaryReportUsecase;
  let repository: jasmine.SpyObj<PaymentBalanceTransctionsRepository>;
  let downloader: jasmine.SpyObj<FileGeneratorService<Promise<string[][]>>>;
  let csvMapperSpy: jasmine.Spy;
  let notifyInfoSpy: jasmine.Spy;
  let notifySuccessSpy: jasmine.Spy;
  let usecaseErrorSpy: jasmine.Spy;

  const request: DownloadAllUIRequest = {
    merchantId: 123,
    status: 'process',
    date: '08/08/2024',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PaymentBalanceSummaryReportUsecase,
        provideNotifierTesting(),
        provideCsvMapper(),
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        provideTemporal(),
        {
          provide: PaymentBalanceTransctionsRepository,
          useValue: jasmine.createSpyObj(
            'PaymentBalanceSummaryReportRepository',
            ['byDate']
          ),
        },
        {
          provide: FileGeneratorService,
          useValue: jasmine.createSpyObj('FileGeneratorService', [
            'generateFileAndDownload',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(PaymentBalanceSummaryReportUsecase);
    repository = TestBed.inject(
      PaymentBalanceTransctionsRepository
    ) as jasmine.SpyObj<PaymentBalanceTransctionsRepository>;
    downloader = TestBed.inject(FileGeneratorService) as jasmine.SpyObj<
      FileGeneratorService<Promise<string[][]>>
    >;
    csvMapperSpy = spyOn(
      TestBed.inject(CsvMapperService),
      'transform'
    ).and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    notifyInfoSpy = spyOn(notifier, 'info').and.callThrough();
    notifySuccessSpy = spyOn(notifier, 'success').and.callThrough();
    usecaseErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(PaymentBalanceSummaryReportUsecase);
  });

  it('should throw an error when date is invalid', async () => {
    const reqWithInvalidDate = { ...request, date: '08-08-2024' } as any;
    const controlledError = 'Controlled error';

    const response = await lastValueFrom(
      usecase.execute(reqWithInvalidDate).pipe(
        take(1),
        catchError(e => {
          if (e instanceof RuntimeMerchantError) {
            return of(controlledError);
          }

          return of('uncontrolled error');
        })
      )
    );

    expect(response).toEqual(controlledError);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(0);
    expect(csvMapperSpy).toHaveBeenCalledTimes(0);
    expect(notifyInfoSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should throw an error when status is invalid', async () => {
    const reqWithInvalidStatus = { ...request, status: 'invalid' } as any;
    const controlledError = 'Controlled error';

    const response = await lastValueFrom(
      usecase.execute(reqWithInvalidStatus).pipe(
        take(1),
        catchError(e => {
          if (e instanceof RuntimeMerchantError) {
            return of(controlledError);
          }

          return of('uncontrolled error');
        })
      )
    );

    expect(response).toEqual(controlledError);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(0);
    expect(csvMapperSpy).toHaveBeenCalledTimes(0);
    expect(notifyInfoSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should complete without stream when an active process is already running', async () => {
    usecase.addActiveProcess(request.date);

    const isCompleteWithoutStrem = await lastValueFrom(
      usecase.execute(request).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStrem).toBeTrue();
    expect(usecase.activeProcessesCount()).toBe(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(0);
    expect(repository.byDate).toHaveBeenCalledTimes(0);
    expect(notifySuccessSpy).toHaveBeenCalledTimes(0);
  });

  it('should throw an error when repository throws an error', async () => {
    repository.byDate.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    const response = await lastValueFrom(
      usecase.execute(request).pipe(
        take(1),
        catchError(() => {
          return of('expected error');
        })
      )
    );

    expect(response).toEqual('expected error');
    expect(repository.byDate).toHaveBeenCalledTimes(1);
    expect(csvMapperSpy).toHaveBeenCalledTimes(0);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(0);
    expect(notifyInfoSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should throw an error when content is empty', async () => {
    repository.byDate.and.returnValue(of(null));
    const controlledError = 'Controlled error';

    const response = await lastValueFrom(
      usecase.execute(request).pipe(
        take(1),
        catchError(e => {
          if (e instanceof RuntimeMerchantError) {
            return of(controlledError);
          }

          return of('uncontrolled error');
        })
      )
    );

    expect(response).toEqual(controlledError);
    expect(repository.byDate).toHaveBeenCalledTimes(1);
    expect(csvMapperSpy).toHaveBeenCalledTimes(1);
    expect(csvMapperSpy).toHaveBeenCalledWith('');
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(0);
    expect(notifyInfoSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should trigger csv transformation and download file on success response', async () => {
    const repositoryResponse = 'col1,col2\nval1,val2\nval3,val4';
    const expectedCsvResult = [
      ['col1', 'col2'],
      ['val1', 'val2'],
      ['val3', 'val4'],
    ];

    repository.byDate.and.returnValue(of(repositoryResponse));
    downloader.generateFileAndDownload.and.returnValue(
      Promise.resolve(expectedCsvResult)
    );

    const response = await lastValueFrom(
      usecase.execute(request).pipe(take(1))
    );

    expect(response).toEqual(expectedCsvResult);
    expect(repository.byDate).toHaveBeenCalledTimes(1);
    expect(repository.byDate).toHaveBeenCalledOnceWith({
      status: 'PROCESS',
      date: '08/2024',
    });
    expect(csvMapperSpy).toHaveBeenCalledTimes(1);
    expect(csvMapperSpy).toHaveBeenCalledWith(repositoryResponse);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledTimes(1);
    expect(downloader.generateFileAndDownload).toHaveBeenCalledWith(
      expectedCsvResult,
      `[TRANSACTIONS]_M${request.merchantId}_${request.date}`
    );
    expect(notifySuccessSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(0);
  });
});
