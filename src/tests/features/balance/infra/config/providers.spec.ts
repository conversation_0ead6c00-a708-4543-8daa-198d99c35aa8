import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideBalanceTools } from 'src/app/features/balance/infra/config/providers';
import {
  MERCHANT_CORE_ENVIRONMENT,
  merchantCoreEnvironment,
} from '../../../../../app/config/merchant-core.environment';
import { PaymentBalanceReceiptsRepository } from '../../../../../app/features/balance/domain/repositories/payment-balance-receipts.repository';
import { PaymentBalanceReceiptsWithHttp } from '../../../../../app/features/balance/infra/repositories/payment-balance-receipts-with-http.repository';

describe('Payment providers', () => {
  let balanceReceiptsRepo: PaymentBalanceReceiptsRepository;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideBalanceTools(),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: merchantCoreEnvironment,
        },
      ],
    });

    balanceReceiptsRepo = TestBed.inject(PaymentBalanceReceiptsRepository);
  });

  it('should be created the balance receipts repository', () => {
    expect(balanceReceiptsRepo).toBeTruthy();
    expect(balanceReceiptsRepo).toBeInstanceOf(PaymentBalanceReceiptsWithHttp);
  });
});
