import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter, UrlTree } from '@angular/router';
import { lastValueFrom, Observable, of } from 'rxjs';
import { hasSelectedBalance } from 'src/app/features/balance/infra/guards/has-selected-balance.guard';
import { DASH_ROUTES } from '../../../../../app/config/app-route-core';
import { CriteriaBalanceDetailsService } from '../../../../../app/features/balance/infra/services/criteria-balance-details.service';

@Component({
  standalone: true,
  selector: 'app-test',
  template: '',
})
class TestComponent {}

const setup = (criteria: unknown) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      {
        provide: CriteriaBalanceDetailsService,
        useValue: criteria,
      },
      provideRouter([
        {
          path: '',
          component: TestComponent,
          canActivate: [hasSelectedBalance],
        },
        { path: '**', component: TestComponent },
      ]),
    ],
  });

  return TestBed.runInInjectionContext(() =>
    hasSelectedBalance({} as any, {} as any)
  ) as Observable<boolean | UrlTree>;
};

describe('hasSelectedBalance', async () => {
  it('should return true if hasSelectedBalance$ returns true', async () => {
    const result = await lastValueFrom(
      setup({ hasSelectedBalance$: () => of(true) })
    );

    expect(result).toBeTrue();
  });

  it('should return UrlTree if hasSelectedBalance$ returns false', async () => {
    const result = await lastValueFrom(
      setup({ hasSelectedBalance$: () => of(false) })
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe(
      '/' + [DASH_ROUTES.rootApp, DASH_ROUTES.balance].join('/')
    );
  });
});
