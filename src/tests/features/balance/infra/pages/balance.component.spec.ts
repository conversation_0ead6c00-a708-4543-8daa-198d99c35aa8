import {
  <PERSON>ync<PERSON><PERSON><PERSON>,
  I18nPluralPipe,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
} from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import {
  LoaderService,
  NotifierService,
  provideTemporal,
  DateCalculator,
  RedirectionService,
} from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
  AplazoFormDatepickerComponent,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { PaymentBalanceAllReceiptsDownloadUseCase } from 'src/app/features/balance/application/usecases/payment-balance-all-receipts-download.usecase';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { of } from 'rxjs';
import { PaymentBalanceListUsecase } from 'src/app/features/balance/application/usecases/payment-balance-list.usecase';
import { PaymentBalanceStatsUseCase } from 'src/app/features/balance/application/usecases/payment-balance-stats.usecase';
import { BalanceComponent } from 'src/app/features/balance/infra/pages/balance/balance.component';
import { EventManagerService } from 'src/app/services/event-manger.service';
import { getTranslocoModule } from 'src/tests/features/shared/transloco-testing';
import { CriteriaBalanceDetailsService } from 'src/app/features/balance/infra/services/criteria-balance-details.service';
import { CriteriaBalanceService } from 'src/app/features/balance/infra/services/criteria-balance.service';
import { UserStoreService } from 'src/app/features/user/src/application/services/user-store.service';
import { ActivatedRoute } from '@angular/router';

let fixture: ComponentFixture<BalanceComponent>;
let component: BalanceComponent;

const mockStatsResp = {
  totalSales: 1000,
  totalAdjustment: 0,
  totalPay: 800,
  totalFee: 200,
};

const mockListResp = {
  content: [],
  totalElements: 0,
  numberOfElements: 0,
  hasContent: false,
  number: 0,
  totalPages: 0,
};

const setup = () => {
  fixture = TestBed.createComponent(BalanceComponent);
  component = fixture.componentInstance;
  fixture.detectChanges();
};

describe('BalanceComponent', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        NgFor,
        NgClass,
        AsyncPipe,
        ReactiveFormsModule,
        I18nPluralPipe,
        AplazoDynamicPipe,
        AplazoIconComponent,
        AplazoButtonComponent,
        AplazoMetricCardComponents,
        AplazoPaginationComponent,
        AplazoSimpleTableComponents,
        AplazoCommonMessageComponents,
        AplazoFormFieldDirectives,
        AplazoSelectComponents,
        AplazoFormDatepickerComponent,
        getTranslocoModule(),
        BalanceComponent,
      ],
      providers: [
        provideTranslocoScope('balance'),
        TranslocoService,
        AplazoIconRegistryService,
        { provide: LoaderService, useClass: LocalLoader },
        { provide: NotifierService, useClass: LocalNotifier },
        provideTemporal(),
        {
          provide: PaymentBalanceStatsUseCase,
          useValue: { execute: () => of(mockStatsResp) },
        },
        {
          provide: PaymentBalanceListUsecase,
          useValue: { execute: () => of(mockListResp) },
        },
        {
          provide: EventManagerService,
          useValue: { sendTrackEvent: () => void 0 },
        },
        {
          provide: UserStoreService,
          useValue: {
            merchantId$: of(1),
            username$: of('tester'),
          },
        },
        {
          provide: DateCalculator,
          useValue: {
            isSameOrAfterMonth: () => true,
          },
        },
        {
          provide: RedirectionService,
          useValue: { internalNavigation: () => {} },
        },
        {
          provide: CriteriaBalanceService,
          useValue: {
            getBalanceCriteria$: () =>
              of({
                search: null,
                date: '01/07/2023',
                status: 'empty',
                page: 1,
              }),
            getDate$: () => of('01/07/2023'),
            getStatus$: () => of('empty'),
            getPageNum$: () => of(1),
            setPageNum: () => {},
            setSearch: () => {},
            setDate: () => {},
            setBalanceStatus: () => {},
          },
        },
        {
          provide: CriteriaBalanceDetailsService,
          useValue: { setSelectedBalance: () => {} },
        },
        {
          provide: AplazoIconRegistryService,
          useValue: { registerIcons: () => {} },
        },
        {
          provide: PaymentBalanceAllReceiptsDownloadUseCase,
          useValue: { execute: () => of(null) },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            data: of({
              dynamicBalanceConfig: { paymentBalanceDateStart: null },
            }),
            snapshot: { firstChild: null },
          },
        },
      ],
    });
  });

  it('should create', () => {
    setup();
    expect(component).toBeTruthy();
  });

  it('should render four metrics cards', () => {
    setup();
    const cards = fixture.debugElement.queryAll(By.css('aplz-ui-metric-card'));
    expect(cards.length).toBe(4);
  });
});
