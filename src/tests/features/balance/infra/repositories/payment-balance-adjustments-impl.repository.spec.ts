import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { IPaymentBalanceAdjustmentsResponseDto } from '../../../../../app/features/balance/application/dtos/payment-balance-adjustments-response.dto';
import { PaymentBalanceAdjustmentsRepositoryImpl } from '../../../../../app/features/balance/infra/repositories/payment-balance-adjustments-impl.repository';

describe('PaymentBalanceAdjustmentsImplRepository', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceAdjustmentsRepositoryImpl;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceAdjustmentsRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceAdjustmentsRepositoryImpl);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceAdjustmentsRepositoryImpl);
  });

  it('should execute successfully', () => {
    const expectedResponse: IPaymentBalanceAdjustmentsResponseDto = {
      content: [
        {
          id: 1763,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 123308,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-19T11:02:46.421564',
          refundId: 14686,
          amount: 74.26,
        },
        {
          id: 1769,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 124669,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-23T11:11:24.272672',
          refundId: 15626,
          amount: 56.58,
        },
        {
          id: 1764,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 124519,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-22T17:18:45.86707',
          refundId: 15526,
          amount: 45.09,
        },
        {
          id: 1774,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 124807,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-24T11:02:58.978039',
          refundId: 15671,
          amount: 65.42,
        },
        {
          id: 1761,
          merchantId: 2239,
          merchantPaymentId: 27726,
          loanId: 123040,
          status: 'PROCESS',
          type: 'RETURN',
          comment: 'Refund',
          created: '2024-07-18T11:10:21.268422',
          refundId: 14635,
          amount: 45.09,
        },
      ],
      number: 0,
      size: 10,
      totalElements: 5,
      last: true,
      totalPages: 1,
      hasContent: true,
      numberOfElements: 5,
      first: true,
    };
    service
      .getList({
        adjustmentBalanceId: '123',
        pageNum: 0,
        pageSize: 10,
      })
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/adjustment/merchantPayment/123/page?pageNum=0&pageSize=10'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when getList throw error', () => {
    service
      .getList({
        adjustmentBalanceId: '123',
        pageNum: 0,
        pageSize: 10,
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.error).toBe('Deliberate error');
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/adjustment/merchantPayment/123/page?pageNum=0&pageSize=10'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
