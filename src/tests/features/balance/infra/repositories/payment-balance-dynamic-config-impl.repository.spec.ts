import { HttpClient, provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PaymentBalanceDynamicConfigRepositoryImp } from 'src/app/features/balance/infra/repositories/payment-balance-dynamic-config-impl.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { PaymentBalanceDynamicConfigResponseDto } from '../../../../../app/features/balance/application/dtos/payment-balance-dynamic-config-response.dto';
import { defaultDynamicDate } from '../../../../../app/features/balance/infra/repositories/payment-balance-dynamic-config-impl.repository';

describe('PaymentBalanceDynamicConfigImplRepository', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceDynamicConfigRepositoryImp;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceDynamicConfigRepositoryImp,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceDynamicConfigRepositoryImp);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceDynamicConfigRepositoryImp);
  });

  it('should execute successfully', () => {
    const expectedResponse = {
      sensitiveData: false,
      paymentBalanceDateStart: '2024-01-01',
    } satisfies PaymentBalanceDynamicConfigResponseDto;

    service.execute().subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(httpSpy).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/status'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should handle error when request fails and return default value', () => {
    service.execute().subscribe({
      next: response => {
        expect(response).toEqual(defaultDynamicDate);
        expect(httpSpy).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/status'
    );

    expect(req.request.method).toBe('GET');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
