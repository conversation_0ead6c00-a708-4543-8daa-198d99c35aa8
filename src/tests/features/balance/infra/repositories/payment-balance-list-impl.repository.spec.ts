import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PaymentBalanceListRepositoryImpl } from 'src/app/features/balance/infra/repositories/payment-balance-list-impl.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { PaymentBalanceListResponseUIDto } from '../../../../../app/features/balance/application/dtos/payment-balance-list-response-ui.dto';

describe('PaymentBalanceListImplRepository', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceListRepositoryImpl;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceListRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceListRepositoryImpl);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceListRepositoryImpl);
  });

  it('should execute successfully', () => {
    const expectedResponse: PaymentBalanceListResponseUIDto = {
      content: [
        {
          id: 27726,
          paymentDate: '2024-07-25',
          saleAmount: 7820,
          adjustment: -286.44,
          feeAmount: 1178.56,
          payAmount: 6641.44,
          finalAmount: 6355,
          status: 'PROCESS',
          loans: 78,
          adjustments: 5,
          current: 'MXN',
          statusInvoice: 'INVOICED',
          statusReceipt: 'AVAILABLE',
        },
        {
          id: 26935,
          paymentDate: '2024-07-11',
          saleAmount: 10400,
          adjustment: -516.29,
          feeAmount: 1561.36,
          payAmount: 8838.64,
          finalAmount: 8322.35,
          status: 'PROCESS',
          loans: 102,
          adjustments: 8,
          current: 'MXN',
          statusInvoice: 'INVOICED',
          statusReceipt: 'AVAILABLE',
        },
        {
          id: 27329,
          paymentDate: '2024-07-18',
          saleAmount: 23800,
          adjustment: -843.41,
          feeAmount: 3589.04,
          payAmount: 20210.96,
          finalAmount: 19367.55,
          status: 'PROCESS',
          loans: 238,
          adjustments: 13,
          current: 'MXN',
          statusInvoice: 'INVOICED',
          statusReceipt: 'AVAILABLE',
        },
        {
          id: 26543,
          paymentDate: '2024-07-04',
          saleAmount: 24100,
          adjustment: -915,
          feeAmount: 3616.88,
          payAmount: 20483.12,
          finalAmount: 19568.12,
          status: 'PROCESS',
          loans: 236,
          adjustments: 14,
          current: 'MXN',
          statusInvoice: 'INVOICED',
          statusReceipt: 'AVAILABLE',
        },
      ],
      number: 0,
      size: 10,
      totalElements: 4,
      last: true,
      totalPages: 1,
      hasContent: true,
      numberOfElements: 4,
      first: true,
    };

    service
      .getList({
        monthYear: '07/2024',
        pageNum: 0,
        pageSize: 10,
        status: 'PROCESS',
      })
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchantPayment/page?status=PROCESS&monthYear=07/2024&pageNum=0&pageSize=10'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when getList throw error', () => {
    service
      .getList({
        monthYear: '07/2024',
        pageNum: 0,
        pageSize: 10,
        status: 'PROCESS',
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.error).toBe('Deliberate error');
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchantPayment/page?status=PROCESS&monthYear=07/2024&pageNum=0&pageSize=10'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
