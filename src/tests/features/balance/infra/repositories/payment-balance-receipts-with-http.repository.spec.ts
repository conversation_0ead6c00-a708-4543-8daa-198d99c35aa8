import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PaymentBalanceReceiptsWithHttp } from 'src/app/features/balance/infra/repositories/payment-balance-receipts-with-http.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';

describe('PaymentBalanceReceiptsWithHttp', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceReceiptsWithHttp;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceReceiptsWithHttp,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceReceiptsWithHttp);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceReceiptsWithHttp);
  });

  it('should execute successfully', () => {
    const expectedResponse = new Blob();

    service.byId({ balanceId: 123 }).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse as any);
        expect(httpSpy).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchantPayment/123/export/receipt'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when search throw error', () => {
    service.byId({ balanceId: 123 }).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toEqual(new Blob());
        expect(httpSpy).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchantPayment/123/export/receipt'
    );

    req.flush(new Blob(), {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
