import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PaymentBalanceSalesRepositoryImpl } from 'src/app/features/balance/infra/repositories/payment-balance-sales-impl.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { IPaymentBalanceSalesResponseDto } from '../../../../../app/features/balance/application/dtos/payment-balance-sales-response.dto';

describe('PaymentBalanceSalesImplRepository', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceSalesRepositoryImpl;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceSalesRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceSalesRepositoryImpl);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceSalesRepositoryImpl);
  });

  it('should execute successfully', () => {
    const expectedResponse: IPaymentBalanceSalesResponseDto = {
      content: [
        {
          id: 122712,
          creationDate: '2024-07-17T11:01:50.497391',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'hpaiEpSjuKIFfXjMjNm6G',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122714,
          creationDate: '2024-07-17T11:02:05.153045',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'JN-bYE213cJVmdXbAmEpc',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122717,
          creationDate: '2024-07-17T11:02:10.192801',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: '26zX9lc3w_jLbC8NhTL08',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122718,
          creationDate: '2024-07-17T11:02:10.650458',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: '3_ilQBx5f5hvrOKDN0Zr1',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122719,
          creationDate: '2024-07-17T11:02:11.039088',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'K-851hGTUuM8rTRltfmHv',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122731,
          creationDate: '2024-07-17T11:02:27.073703',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'Qc_2Lud7GLmqEqOBc1g7C',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122738,
          creationDate: '2024-07-17T11:02:43.352501',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'tldN_FZH41utck-e4Ct5D',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122741,
          creationDate: '2024-07-17T11:02:53.351901',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'GYfLHg72hBViOThdidLED',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122743,
          creationDate: '2024-07-17T11:02:56.011634',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: '0B8ElU_6ANgC3K9tdXMvS',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
        {
          id: 122772,
          creationDate: '2024-07-17T11:13:04.962616',
          salesAmount: 100,
          feePct: 0.1,
          feeMerchantPct: 10,
          feeOps: 3,
          feeMerchant: 13,
          feeTaxMerchantPct: 0.16,
          feeTaxMerchant: 2.08,
          feeRevenue: 15.08,
          payMerchantAmount: 84.92,
          status: 'APROBADO',
          statusPayment: 'PROCESS',
          cartId: 'qRfOHVDhSlWgfJg4o5oJr',
          branch: 'Store QA_wolf No 1',
          totalProducts: 0,
        },
      ],
      number: 0,
      size: 10,
      totalElements: 78,
      last: false,
      totalPages: 8,
      hasContent: true,
      numberOfElements: 10,
      first: true,
    };

    service
      .getList({
        pageNum: 0,
        pageSize: 10,
        salesBalanceId: 123,
      })
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/report/payment/merchantPayment/123?pageNum=0&pageSize=10'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when getList throw error', () => {
    service
      .getList({
        pageNum: 0,
        pageSize: 10,
        salesBalanceId: 123,
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.error).toBe('Deliberate error');
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/report/payment/merchantPayment/123?pageNum=0&pageSize=10'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
