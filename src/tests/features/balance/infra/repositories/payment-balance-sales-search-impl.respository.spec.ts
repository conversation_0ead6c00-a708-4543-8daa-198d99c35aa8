import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PaymentBalanceSalesSearchRepositoryImpl } from 'src/app/features/balance/infra/repositories/payment-balance-sales-search-impl.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';

describe('PaymentBalanceSalesSearchImplRepository', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceSalesSearchRepositoryImpl;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceSalesSearchRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceSalesSearchRepositoryImpl);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceSalesSearchRepositoryImpl);
  });

  it('should execute successfully', () => {
    const expectedResponse = [
      {
        content: 'test',
      },
    ];

    service
      .search({
        dateRange: {
          endDate: '01/05/2024',
          startDate: '01/05/2024',
        },
        search: '123',
        status: 'PROCESS',
      })
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse as any);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/report/payment/filter?startDate=01/05/2024&endDate=01/05/2024&status=PROCESS&element=123'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when search throw error', () => {
    service
      .search({
        dateRange: {
          endDate: '01/05/2024',
          startDate: '01/05/2024',
        },
        search: '123',
        status: 'PROCESS',
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.error).toBe('Deliberate error');
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/report/payment/filter?startDate=01/05/2024&endDate=01/05/2024&status=PROCESS&element=123'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
