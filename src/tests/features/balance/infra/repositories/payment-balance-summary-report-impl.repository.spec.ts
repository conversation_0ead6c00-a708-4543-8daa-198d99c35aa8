import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PaymentBalanceTransactionsWithHttp } from 'src/app/features/balance/infra/repositories/payment-balance-transactions-with-http.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';

describe('PaymentBalanceTransactionsWithHttp', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: PaymentBalanceTransactionsWithHttp;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        PaymentBalanceTransactionsWithHttp,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentBalanceTransactionsWithHttp);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentBalanceTransactionsWithHttp);
  });

  it('should execute successfully', () => {
    const expectedResponse = 'test content';

    service
      .byDate({
        date: '05/2024',
        status: 'PROCESS',
      })
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse as any);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchantPayment/edo-summary?status=PROCESS&monthYear=05/2024'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw error when service throw error', () => {
    service
      .byDate({
        date: '05/2024',
        status: 'PROCESS',
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.error).toBe('Deliberate error');
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchantPayment/edo-summary?status=PROCESS&monthYear=05/2024'
    );

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
