import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { PaymentBalanceDynamicConfigResponseDto } from '../../../../../app/features/balance/application/dtos/payment-balance-dynamic-config-response.dto';
import { PaymentBalanceDynamicConfigRepository } from '../../../../../app/features/balance/domain/repositories/payment-balance-dynamic-config.repository';
import {
  dynamicConfigDefault,
  getDynamicConfig,
} from '../../../../../app/features/balance/infra/resolvers/dynamic-config.resolver';

@Component({
  standalone: true,
  selector: 'app-test',
  template: '',
})
export class TestComponent {}

const setup = (
  repositoryResponse: Observable<PaymentBalanceDynamicConfigResponseDto>
) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      {
        provide: PaymentBalanceDynamicConfigRepository,
        useValue: {
          execute: () => repositoryResponse,
        },
      },
      provideRouter([
        {
          path: '',
          component: TestComponent,
          resolve: { config: getDynamicConfig },
        },
      ]),
    ],
  });

  return TestBed.runInInjectionContext(
    () =>
      getDynamicConfig(
        {} as any,
        {} as any
      ) as Observable<PaymentBalanceDynamicConfigResponseDto>
  );
};

describe('getDynamicConfig', () => {
  it('should return the dynamic config', async () => {
    const resolver = await lastValueFrom(
      setup(
        of({
          paymentBalanceDateStart: '2021-01-01',
          sensitiveData: true,
        })
      )
    );

    expect(resolver).toEqual({
      paymentBalanceDateStart: '2021-01-01',
      sensitiveData: true,
    });
  });

  it('should return the dynamic config with different values', async () => {
    const resolver = await lastValueFrom(
      setup(
        of({
          paymentBalanceDateStart: '2021-02-01',
          sensitiveData: false,
        })
      )
    );

    expect(resolver).toEqual({
      paymentBalanceDateStart: '2021-02-01',
      sensitiveData: false,
    });
  });

  it('should return a default dynamic config on repository error', async () => {
    const resolver = await lastValueFrom(
      setup(throwError(() => 'Deliberate error'))
    );

    expect(resolver).toEqual(dynamicConfigDefault);
  });
});
