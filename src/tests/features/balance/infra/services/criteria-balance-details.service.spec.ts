import { TestBed } from '@angular/core/testing';
import { provideTemporal } from '@aplazo/merchant/shared';
import { PaymentBalance } from '../../../../../app/features/balance/domain/entities/payment-balance';
import { CriteriaBalanceDetailsService } from '../../../../../app/features/balance/infra/services/criteria-balance-details.service';

describe('CriteriaBalanceDetailsService', () => {
  let store: CriteriaBalanceDetailsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [CriteriaBalanceDetailsService, provideTemporal()],
    });

    store = TestBed.inject(CriteriaBalanceDetailsService);
  });

  it('should be created', () => {
    expect(store).toBeTruthy();
  });

  it('should update the selected balance', done => {
    let result: PaymentBalance | null = null;

    store.selectedBalance$.subscribe(balance => {
      result = balance;
    });

    expect(result).toBeNull();

    const mockBalance: PaymentBalance = {
      id: 1,
      adjustment: 0,
      feeAmount: 0,
      payAmount: 0,
      paymentDate: '2024-02-01',
      saleAmount: 0,
      finalAmount: 0,
      status: 'paid',
      adjustments: 0,
      loans: 0,
      current: 'MXN',
      statusInvoice: 'INVOICED',
      statusReceipt: 'AVAILABLE',
    };

    store.setSelectedBalance(mockBalance);

    expect(result).toEqual(mockBalance);

    done();
  });

  it('should reflect changes for hasSelectedBalance$', done => {
    let result = false;

    store.hasSelectedBalance$().subscribe(hasSelected => {
      result = hasSelected;
    });

    expect(result).toBeFalse();

    const mockBalance: PaymentBalance = {
      id: 1,
      adjustment: 0,
      feeAmount: 0,
      payAmount: 0,
      paymentDate: '2024-02-01',
      saleAmount: 0,
      finalAmount: 0,
      status: 'paid',
      adjustments: 0,
      loans: 0,
      current: 'MXN',
      statusInvoice: 'INVOICED',
      statusReceipt: 'AVAILABLE',
    };

    store.setSelectedBalance(mockBalance);

    expect(result).toBeTrue();

    done();
  });

  it('should update salesPageNum and reflect changes for the sales criteria', done => {
    let result = null;

    store.salesCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageNumSalesCriteria(1);

    expect(result).toEqual({
      search: null,
      pageNum: 1,
      pageSize: 10,
    });

    done();
  });

  it('should update adjustmentsPageNum and reflect changes for the adjustments criteria', done => {
    let result = null;

    store.adjustmentsCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageNumAdjustmentsCriteria(1);

    expect(result).toEqual({
      search: null,
      pageNum: 1,
      pageSize: 10,
    });

    done();
  });

  it('should update salesSearch and reflect changes for the sales criteria', done => {
    let result = null;

    store.salesCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setSearchSalesCriteria('search');

    expect(result).toEqual({
      search: 'search',
      pageNum: 0,
      pageSize: 10,
    });

    done();
  });

  it('should update adjustmentsSearch and reflect changes for the adjustments criteria', done => {
    let result = null;

    store.adjustmentsCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setSearchAdjustmentsCriteria('search');

    expect(result).toEqual({
      search: 'search',
      pageNum: 0,
      pageSize: 10,
    });

    done();
  });

  it('should update salesPageSize and reflect changes for the sales criteria', done => {
    let result = null;

    store.salesCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageSizeSalesCriteria(20);

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 20,
    });

    done();
  });

  it('should update adjustmentsPageSize and reflect changes for the adjustments criteria', done => {
    let result = null;

    store.adjustmentsCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageSizeAdjustmentsCriteria(20);

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 20,
    });

    done();
  });

  it('should return the correct date range for sales', done => {
    let result = null;

    store.getSalesDateRange$().subscribe(dateRange => {
      result = dateRange;
    });

    expect(result).withContext('The result should be null').toBeNull();

    const mockBalance: PaymentBalance = {
      id: 1,
      adjustment: 0,
      feeAmount: 0,
      payAmount: 0,
      paymentDate: '2024-02-01',
      saleAmount: 0,
      finalAmount: 0,
      status: 'paid',
      adjustments: 0,
      loans: 0,
      current: 'MXN',
      statusInvoice: 'INVOICED',
      statusReceipt: 'AVAILABLE',
    };

    store.setSelectedBalance(mockBalance);

    expect(result)
      .withContext('The result should be a valid B2BDateRange')
      .toEqual({
        startDate: '01/02/2024',
        endDate: '29/02/2024',
      });

    done();
  });

  it('should reflect changes for salesPageNum$', done => {
    let result = null;

    store.getSalesPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    store.setPageNumSalesCriteria(1);

    expect(result).toBe(1);

    done();
  });

  it('should reflect changes for adjustmentsPageNum$', done => {
    let result = null;

    store.getAdjustmentsPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    store.setPageNumAdjustmentsCriteria(1);

    expect(result).toBe(1);

    done();
  });

  it('should reflect changes for salesPageSize$', done => {
    let result = null;

    store.getSalesPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    store.setPageSizeSalesCriteria(20);

    expect(result).toBe(20);

    done();
  });

  it('should reflect changes for adjustmentsPageSize$', done => {
    let result = null;

    store.getAdjustmentsPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    store.setPageSizeAdjustmentsCriteria(20);

    expect(result).toBe(20);

    done();
  });

  it('should reflect changes for salesSearch$', done => {
    let result = null;

    store.getSalesSearch$().subscribe(search => {
      result = search;
    });

    expect(result).toBeNull();

    store.setSearchSalesCriteria('search');

    expect(result).toBe('search');

    done();
  });

  it('should reflect changes for hasSalesActiveSearch$', done => {
    let result = null;

    store.hasSalesActiveSearch$().subscribe(hasSearch => {
      result = hasSearch;
    });

    expect(result).toBeFalse();

    store.setSearchSalesCriteria('search');

    expect(result).toBeTrue();

    done();
  });

  it('should not update salesPageNum', done => {
    let result = null;

    store.salesCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageNumSalesCriteria(1);

    expect(result).toEqual({
      search: null,
      pageNum: 1,
      pageSize: 10,
    });

    store.setPageNumSalesCriteria(1);

    expect(result).toEqual({
      search: null,
      pageNum: 1,
      pageSize: 10,
    });

    done();
  });

  it('should not update adjustmentsPageNum', done => {
    let result = null;

    store.adjustmentsCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageNumAdjustmentsCriteria(1);

    expect(result).toEqual({
      search: null,
      pageNum: 1,
      pageSize: 10,
    });

    store.setPageNumAdjustmentsCriteria(1);

    expect(result).toEqual({
      search: null,
      pageNum: 1,
      pageSize: 10,
    });

    done();
  });

  it('should not update salesSearch', done => {
    let result = null;

    store.salesCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setSearchSalesCriteria('search');

    expect(result).toEqual({
      search: 'search',
      pageNum: 0,
      pageSize: 10,
    });

    store.setSearchSalesCriteria('search');

    expect(result).toEqual({
      search: 'search',
      pageNum: 0,
      pageSize: 10,
    });

    done();
  });

  it('should not update adjustmentsSearch', done => {
    let result = null;

    store.adjustmentsCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setSearchAdjustmentsCriteria('search');

    expect(result).toEqual({
      search: 'search',
      pageNum: 0,
      pageSize: 10,
    });

    store.setSearchAdjustmentsCriteria('search');

    expect(result).toEqual({
      search: 'search',
      pageNum: 0,
      pageSize: 10,
    });

    done();
  });

  it('should not update salesPageSize', done => {
    let result = null;

    store.salesCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageSizeSalesCriteria(20);

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 20,
    });

    store.setPageSizeSalesCriteria(20);

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 20,
    });

    done();
  });

  it('should not update adjustmentsPageSize', done => {
    let result = null;

    store.adjustmentsCriteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 10,
    });

    store.setPageSizeAdjustmentsCriteria(20);

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 20,
    });

    store.setPageSizeAdjustmentsCriteria(20);

    expect(result).toEqual({
      search: null,
      pageNum: 0,
      pageSize: 20,
    });

    done();
  });
});
