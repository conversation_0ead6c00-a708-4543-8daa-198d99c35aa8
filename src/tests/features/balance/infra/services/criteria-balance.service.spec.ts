import { TestBed } from '@angular/core/testing';
import { TemporalService } from '@aplazo/merchant/shared';
import {
  CriteriaBalanceService,
  IBalanceCriteriaDto,
} from '../../../../../app/features/balance/infra/services/criteria-balance.service';

describe('criteria-balance service', () => {
  let store: CriteriaBalanceService;
  let temporal: TemporalService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [CriteriaBalanceService, TemporalService],
    });

    store = TestBed.inject(CriteriaBalanceService);
    temporal = TestBed.inject(TemporalService);
  });

  it('should be an instance of CriteriaBalanceService', () => {
    expect(store).toBeDefined();
    expect(store).toBeInstanceOf(CriteriaBalanceService);
  });

  it('should update the criteria balance', done => {
    let result: IBalanceCriteriaDto | undefined;

    store.getBalanceCriteria$().subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual({
      status: 'process',
      date: temporal.todayRawDayFirst,
      pageNum: 0,
      pageSize: 10,
      search: null,
    });

    done();
  });

  it('should update the page number', done => {
    let result: number | undefined;

    store.getPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    store.setPageNum(1);

    expect(result).toBe(1);

    done();
  });

  it('should not update the page number', done => {
    let result: number | undefined;

    store.getPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    store.setPageNum(0);

    expect(result).toBe(0);

    done();
  });

  it('should update the page size', done => {
    let result: number | undefined;

    store.getPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    store.setPageSize(20);

    expect(result).toBe(20);

    done();
  });

  it('should not update the page size', done => {
    let result: number | undefined;

    store.getPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    store.setPageSize(10);

    expect(result).toBe(10);

    done();
  });

  it('should update the balance status', done => {
    let result: string | undefined;

    store.getStatus$().subscribe(status => {
      result = status;
    });

    expect(result).toBe('process');

    store.setBalanceStatus('paid');

    expect(result).toBe('paid');

    done();
  });

  it('should not update the balance status', done => {
    let result: string | undefined;

    store.getStatus$().subscribe(status => {
      result = status;
    });

    expect(result).toBe('process');

    store.setBalanceStatus('process');

    expect(result).toBe('process');

    done();
  });

  it('should update the search', done => {
    let result: string | undefined;

    store.getSearch$().subscribe(search => {
      result = search;
    });

    expect(result).toBe(null);

    store.setSearch('search');

    expect(result).toBe('search');

    done();
  });

  it('should not update the search', done => {
    let result: string | undefined;

    store.getSearch$().subscribe(search => {
      result = search;
    });

    expect(result).toBe(null);

    store.setSearch(null);

    expect(result).toBe(null);

    done();
  });

  it('should update the date', done => {
    let result: string | undefined;

    store.getDate$().subscribe(date => {
      result = date;
    });

    expect(result).toBe(temporal.todayRawDayFirst);

    store.setDate('22/03/2024');

    expect(result).toBe('22/03/2024');

    done();
  });

  it('should not update the date', done => {
    let result: string | undefined;

    store.getDate$().subscribe(date => {
      result = date;
    });

    expect(result).toBe(temporal.todayRawDayFirst);

    store.setDate(temporal.todayRawDayFirst);

    expect(result).toBe(temporal.todayRawDayFirst);

    done();
  });
});
