import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, throwError } from 'rxjs';
import { GetAllBranchOfficesUsecase } from '../../../../../app/features/branch-offices/src/application/usecases/get-all-branch-offices.usecase';
import { IBranchOfficeRepositoryResponseDto } from '../../../../../app/features/branch-offices/src/domain/entities/branch-office';
import { BranchOfficesRepository } from '../../../../../app/features/branch-offices/src/domain/repositories/branch-offices.repository';
import { LocalBranchOfficesRepository } from '../../infra/repositories/local-branch-offices.repository';

describe('GetAllBranchOfficesUsecase', () => {
  let usecase: GetAllBranchOfficesUsecase;
  let repository: BranchOfficesRepository<
    Observable<IBranchOfficeRepositoryResponseDto>
  >;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;

  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let getItemsSpy: jasmine.Spy;
  let errorHandleSpy: jasmine.Spy;

  beforeEach(() => {
    repository = new LocalBranchOfficesRepository();
    loader = new LocalLoader();
    errorHandler = new LocalUsecaseErrorHandler();
    usecase = new GetAllBranchOfficesUsecase(repository, loader, errorHandler);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    getItemsSpy = spyOn(repository, 'getItems').and.callThrough();
    errorHandleSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetAllBranchOfficesUsecase);
  });

  it('should retrieve all branch offices', async () => {
    const response = await lastValueFrom(usecase.execute());

    expect(Array.isArray(response))
      .withContext('response should be an array')
      .toBeTrue();

    expect(
      response
        .map(b => Object.hasOwn(b, 'id') && Object.hasOwn(b, 'name'))
        .every(Boolean)
    )
      .withContext('response should have id and name properties')
      .toBeTrue();

    expect(response.length).toBe(35);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(getItemsSpy).toHaveBeenCalledTimes(1);
    expect(errorHandleSpy).toHaveBeenCalledTimes(0);
  });

  it('should return an empty array when an error occurs', async () => {
    getItemsSpy.and.returnValue(throwError(() => 'Deliberate error'));

    const response = await lastValueFrom(usecase.execute());

    expect(Array.isArray(response))
      .withContext('response should be an array')
      .toBeTrue();

    expect(response.length).toBe(0);
    expect(getItemsSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandleSpy).toHaveBeenCalledTimes(1);
  });
});
