import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Observable } from 'rxjs';
import { IBranchOfficeRepositoryResponseDto } from 'src/app/features/branch-offices/src/domain/entities/branch-office';
import { BranchOfficesRepository } from 'src/app/features/branch-offices/src/domain/repositories/branch-offices.repository';
import { provideBranchOfficesRepository } from 'src/app/features/branch-offices/src/infra/config/providers';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { GetBranchOfficesWithHttpRepository } from '../../../../../app/features/branch-offices/src/infra/repositories/branch-offices-with-http.repository';

describe('BranchOfficesProviders', () => {
  let repository: BranchOfficesRepository<
    Observable<IBranchOfficeRepositoryResponseDto>
  >;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiBaseUrl: '',
            merchantPath: '',
            posUiBaseUrl: 'https://posui.aplazo.net/api/merchant_configs',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
        provideBranchOfficesRepository(),
      ],
    });

    repository = TestBed.inject(BranchOfficesRepository);
  });

  it('should get an instance of BranchOfficesRepository', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(GetBranchOfficesWithHttpRepository);
  });
});
