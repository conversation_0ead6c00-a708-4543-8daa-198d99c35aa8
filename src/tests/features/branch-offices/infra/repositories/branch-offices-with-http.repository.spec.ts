import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { GetBranchOfficesWithHttpRepository } from 'src/app/features/branch-offices/src/infra/repositories/branch-offices-with-http.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import data from '../local-branche.db.json';

describe('BranchOfficesWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let httpSpy: jasmine.Spy;
  let service: GetBranchOfficesWithHttpRepository;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        GetBranchOfficesWithHttpRepository,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiPosUiBaseUrl: 'https://posui.aplazo.net',
          },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(GetBranchOfficesWithHttpRepository);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(GetBranchOfficesWithHttpRepository);
  });

  it('should execute successfully', () => {
    service.getItems().subscribe({
      next: response => {
        expect(
          Object.keys(response)
            .map(key => key === 'code' || key === 'content' || key === 'error')
            .every(Boolean)
        )
          .withContext('Response should have all expected keys')
          .toBeTrue();
        expect(httpSpy).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://posui.aplazo.net/api/merchant_configs'
    );

    expect(req.request.method).toBe('GET');

    req.flush(data);
  });

  it('should throw error when getItems throw error', () => {
    service.getItems().subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(404);
        expect(httpSpy).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      'https://posui.aplazo.net/api/merchant_configs'
    );

    req.flush('Deliberate error', { status: 404, statusText: 'Not Found' });
  });
});
