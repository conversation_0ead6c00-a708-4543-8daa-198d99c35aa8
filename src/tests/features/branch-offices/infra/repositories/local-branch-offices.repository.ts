import { Observable, of } from 'rxjs';
import { IBranchOfficeRepositoryResponseDto } from '../../../../../app/features/branch-offices/src/domain/entities/branch-office';
import { BranchOfficesRepository } from '../../../../../app/features/branch-offices/src/domain/repositories/branch-offices.repository';
import response from '../local-branche.db.json';

export class LocalBranchOfficesRepository
  implements
    BranchOfficesRepository<Observable<IBranchOfficeRepositoryResponseDto>>
{
  readonly #data = response as IBranchOfficeRepositoryResponseDto;

  getItems(): Observable<IBranchOfficeRepositoryResponseDto> {
    return of(this.#data);
  }
}
