import { <PERSON><PERSON><PERSON><PERSON><PERSON>, I18nPlural<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import {
  B2BDateRange,
  LoaderService,
  NotifierService,
  provideTemporal,
} from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDatepickerComponent } from '@aplazo/shared-ui/datepicker';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponent,
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
  MetricCardComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import {
  AplazoSimpleTableComponent,
  AplazoSimpleTableComponents,
  AplazoSimpleTableHeaderCellDirective,
} from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { NgxMaskDirective } from 'ngx-mask';
import { of } from 'rxjs';
import { ClarificationsComponent } from 'src/app/features/clarifications/infra/clarifications.component';
import { CriteriaClarificationsService } from 'src/app/features/clarifications/infra/services/criteria-clarifications.service';
import { EventManagerService } from 'src/app/services/event-manger.service';
import { IGetListResponseDto } from '../../../../app/features/clarifications/application/dtos/get-list-response.dto';
import { ClarificationStat } from '../../../../app/features/clarifications/domain/entities/clarification-stat';
import { ClarificationListRepository } from '../../../../app/features/clarifications/domain/repositories/clarification-list.repository';
import { ClarificationStatsRepository } from '../../../../app/features/clarifications/domain/repositories/clarification-stats.repository';
import { getTranslocoModule } from '../../shared/transloco-testing';
import { mockListRepoResp, mockSummaryResp } from '../mock-clarifications';

let fixture: ComponentFixture<ClarificationsComponent>;
let component: ClarificationsComponent;

const setup = (list?: IGetListResponseDto, stats?: ClarificationStat[]) => {
  if (list) {
    TestBed.overrideProvider(ClarificationListRepository, {
      useValue: {
        getList: () => of(list),
      },
    });
  }

  if (stats) {
    TestBed.overrideProvider(ClarificationStatsRepository, {
      useValue: {
        getStats: () => of(stats),
      },
    });
  }

  fixture = TestBed.createComponent(ClarificationsComponent);
  component = fixture.componentInstance;

  fixture.detectChanges();
};

describe('ClarificationsComponent', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        NgFor,
        AsyncPipe,
        I18nPluralPipe,
        AplazoIconComponent,
        ReactiveFormsModule,
        NgxMaskDirective,
        I18nPluralPipe,
        AplazoDynamicPipe,
        AplazoIconComponent,
        AplazoCardComponent,
        AplazoDatepickerComponent,
        AplazoButtonComponent,
        AplazoMetricCardComponents,
        AplazoPaginationComponent,
        AplazoSimpleTableComponents,
        AplazoCommonMessageComponents,
        AplazoFormFieldDirectives,
        AplazoTooltipDirective,
        AplazoSelectComponents,
        getTranslocoModule(),
      ],
      providers: [
        provideTranslocoScope('clarifications'),
        CriteriaClarificationsService,
        TranslocoService,
        AplazoIconRegistryService,
        provideTemporal(),
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: EventManagerService,
          useValue: {
            sendTrackEvent: () => {
              void 0;
            },
          },
        },
        {
          provide: ClarificationListRepository,
          useValue: {
            getList: () => of(mockListRepoResp),
          },
        },
        {
          provide: ClarificationStatsRepository,
          useValue: {
            getStats: () => of(mockSummaryResp),
          },
        },
      ],
    });
  });

  it('should be created', () => {
    setup();
    expect(component).toBeTruthy();
  });

  it('should have a title', done => {
    setup();
    const title = fixture.debugElement.query(By.css('h3'));
    let expecteTextUI = '';

    component.titleTextUI$.subscribe(text => {
      expecteTextUI = text.title;
    });

    expect(title.nativeElement.textContent.trim()).toEqual(expecteTextUI);

    done();
  });

  it('should have a four card statistics', () => {
    setup();
    const statistics = fixture.debugElement.queryAll(
      By.css('aplz-ui-metric-card, aplz-ui-stat-card')
    );

    expect(statistics.length).toEqual(4);
  });

  it('should have a counter label and includes the total of refunds', done => {
    setup();
    let total = 0;
    const counterLabel = fixture.debugElement.query(By.css('h5 > span'));

    component.clarificationsCounting$.subscribe(count => {
      total = count;
    });

    expect(counterLabel.nativeElement.textContent.includes(total))
      .withContext('The counter label should include the total of refunds')
      .toBeTrue();

    done();
  });

  it('should have a counter label and not includes the total of refunds', done => {
    setup({
      ...mockListRepoResp,
      totalElements: 0,
      content: [],
      numberOfElements: 0,
      hasContent: false,
      number: 0,
      totalPages: 0,
    });

    let total = 0;
    const counterLabel = fixture.debugElement.query(By.css('h5 > span'));

    component.clarificationsCounting$.subscribe(count => {
      total = count;
    });

    expect(counterLabel.nativeElement.textContent.includes(total))
      .withContext('The counter label should NOT include the total of refunds')
      .toBeFalse();

    done();
  });

  it('should have a four card statistics', () => {
    setup();

    const statistics = fixture.debugElement.queryAll(
      By.directive(MetricCardComponent)
    );

    expect(statistics.length).toEqual(4);
  });

  it('should have a table with 8 columns', () => {
    setup();

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    expect(table).toBeDefined();

    const columHeaders = table.queryAll(
      By.directive(AplazoSimpleTableHeaderCellDirective)
    );

    expect(columHeaders.length).toEqual(8);
  });

  it('should show pagination', done => {
    setup();

    let totalPages = 0;

    component.pagesByClarificationsCounting$.subscribe(pages => {
      totalPages = pages;
    });

    const pagination = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );

    expect(totalPages)
      .withContext('The total pages should be greater than 0')
      .toBeGreaterThan(0);
    expect(pagination)
      .withContext('The page should show the pagination component')
      .toBeDefined();

    done();
  });

  it('should not show pagination', done => {
    setup({
      ...mockListRepoResp,
      totalElements: 0,
      content: [],
      numberOfElements: 0,
      hasContent: false,
      number: 0,
      totalPages: 0,
    });

    let totalPages = 0;

    component.pagesByClarificationsCounting$.subscribe(pages => {
      totalPages = pages;
    });

    const pagination = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );

    expect(totalPages).withContext('The total pages should be 0').toEqual(0);
    expect(pagination)
      .withContext('The page should NOT show the pagination component')
      .toBeNull();

    done();
  });

  it('should show a message when there are no clarifications', () => {
    setup({
      ...mockListRepoResp,
      totalElements: 0,
      content: [],
      numberOfElements: 0,
      hasContent: false,
      number: 0,
      totalPages: 0,
    });

    const message = fixture.debugElement.query(
      By.directive(AplazoCommonMessageComponent)
    );

    expect(message).toBeDefined();
  });

  it('should update page on changePage', done => {
    setup();

    const eventTrackSpy = spyOn(
      TestBed.inject(EventManagerService),
      'sendTrackEvent'
    ).and.callThrough();

    let result = 0;
    const newPage = 2;

    component.currentPage$.subscribe(page => {
      result = page;
    });

    component.changePage(newPage);

    expect(eventTrackSpy)
      .withContext('The event track should be called when the page changes')
      .toHaveBeenCalledTimes(1);
    expect(result)
      .withContext('The current page should be the same that the new page')
      .toBe(newPage);

    done();
  });

  it('should update date range on changeDateRange', fakeAsync(() => {
    setup();

    let result: B2BDateRange | null = null;

    component.dateRange$.subscribe(range => {
      result = range;
    });

    tick();

    expect(result).toEqual({
      endDate: component.todayRawDateDayFirst,
      startDate: component.sevenDaysAgoRawDateDayFirst,
    });

    component.dateControl.setValue([
      new Date('2024-03-03T00:00:00'),
      new Date('2024-03-13T00:00:00'),
    ]);

    tick();

    expect(result).toEqual({
      endDate: '13/03/2024',
      startDate: '03/03/2024',
    });
  }));
});
