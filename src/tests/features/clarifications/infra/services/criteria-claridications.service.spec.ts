import { TestBed } from '@angular/core/testing';
import { B2BDateRange, TemporalService } from '@aplazo/merchant/shared';
import {
  CriteriaClarificationsService,
  ICriteriaClarificationsDto,
} from '../../../../../app/features/clarifications/infra/services/criteria-clarifications.service';

describe('CriteriaClarificationsService', () => {
  let service: CriteriaClarificationsService;
  let temporal: TemporalService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [CriteriaClarificationsService, TemporalService],
    });

    service = TestBed.inject(CriteriaClarificationsService);
    temporal = TestBed.inject(TemporalService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(CriteriaClarificationsService);
  });

  it('should update the criteria clarifications', done => {
    let result: ICriteriaClarificationsDto | undefined;

    service.getClarificationsCriteria$().subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(service.initialClarificationsCriteria);

    done();
  });

  it('should update the page number', done => {
    let result: number | undefined;

    service.getPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    service.setPageNum(1);

    expect(result).toBe(1);

    done();
  });

  it('should update the page size', done => {
    let result: number | undefined;

    service.getPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    service.setPageSize(20);

    expect(result).toBe(20);

    done();
  });

  it('should update the date range', done => {
    let result: B2BDateRange | undefined;

    service.getDateRange$().subscribe(dateRange => {
      result = dateRange;
    });

    expect(result).toEqual({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    service.setDateRange({
      startDate: temporal.todayRawDayFirst,
      endDate: temporal.todayRawDayFirst,
    });

    expect(result).toEqual({
      startDate: temporal.todayRawDayFirst,
      endDate: temporal.todayRawDayFirst,
    });

    done();
  });

  it('should update the status', done => {
    let result: string | undefined;

    service.getStatus$().subscribe(status => {
      result = status;
    });

    expect(result).toBe('merchantEvidence');

    service.setStatus('approved');

    expect(result).toBe('approved');

    done();
  });

  it('should clear the criteria clarifications', done => {
    let result: ICriteriaClarificationsDto | undefined;

    service.getClarificationsCriteria$().subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(service.initialClarificationsCriteria);

    service.setPageNum(1);

    expect(result).toEqual({
      ...service.initialClarificationsCriteria,
      pageNum: 1,
    });

    service.clearCriteria();

    expect(result).toEqual(service.initialClarificationsCriteria);

    done();
  });

  it('should not update pageNum if the value is the same', done => {
    let result: number | undefined;

    service.getPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    service.setPageNum(0);

    expect(result).toBe(0);

    done();
  });

  it('should not update pageSize if the value is the same', done => {
    let result: number | undefined;

    service.getPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    service.setPageSize(10);

    expect(result).toBe(10);

    done();
  });

  it('should not update dateRange if the value is the same', done => {
    let result: B2BDateRange | undefined;

    service.getDateRange$().subscribe(dateRange => {
      result = dateRange;
    });

    expect(result).toEqual({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    service.setDateRange({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    expect(result).toEqual({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    done();
  });

  it('should not update status if the value is the same', done => {
    let result: string | undefined;

    service.getStatus$().subscribe(status => {
      result = status;
    });

    expect(result).toBe('merchantEvidence');

    service.setStatus('merchantEvidence');

    expect(result).toBe('merchantEvidence');

    done();
  });
});
