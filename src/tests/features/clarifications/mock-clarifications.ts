export const mockSummaryResp = [
  {
    salesAmount: 2600.0,
    refundAmount: 797.0,
    totalRefunds: 13,
    customers: 1,
    status: 'APPROVED',
  },
  {
    salesAmount: 200.0,
    refundAmount: 200.0,
    totalRefunds: 1,
    customers: 1,
    status: 'REQUESTED',
  },
];

export const mockListRepoResp = {
  content: [
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 200,
      merchantCancelId: 7368,
      loanId: 98310,
      cartId: '199906744gddr12_esv14',
      reason: 'WM - POSUI - refund',
      created: '2024-04-12T09:09:27.115096',
      requestType: 'A',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7369,
      loanId: 98311,
      cartId: '199906714gddr10_esv21',
      reason: 'WM - POSUI - refund',
      created: '2024-04-12T09:09:36.990817',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7370,
      loanId: 98311,
      cartId: '199906714gddr10_esv21',
      reason: 'WM - POSUI - refund',
      created: '2024-04-12T09:09:37.493035',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7371,
      loanId: 98311,
      cartId: '199906714gddr10_esv21',
      reason: 'WM - POSUI - refund',
      created: '2024-04-12T09:09:38.01658',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7372,
      loanId: 98311,
      cartId: '199906714gddr10_esv21',
      reason: 'WM - POSUI - refund',
      created: '2024-04-12T09:09:38.580035',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 200,
      merchantCancelId: 7386,
      loanId: 98632,
      cartId: '199906724gddr11_esv20',
      reason: 'WM - POSUI - refund',
      created: '2024-04-13T09:08:52.223258',
      requestType: 'A',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7387,
      loanId: 98633,
      cartId: '199906874gddr12_esv28',
      reason: 'WM - POSUI - refund',
      created: '2024-04-13T09:09:01.565647',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7388,
      loanId: 98633,
      cartId: '199906874gddr12_esv28',
      reason: 'WM - POSUI - refund',
      created: '2024-04-13T09:09:02.113132',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7389,
      loanId: 98633,
      cartId: '199906874gddr12_esv28',
      reason: 'WM - POSUI - refund',
      created: '2024-04-13T09:09:02.740315',
      requestType: 'AP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'NO PAGADO',
      statusRefund: 'APROBADO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 7390,
      loanId: 98633,
      cartId: '199906874gddr12_esv28',
      reason: 'WM - POSUI - refund',
      created: '2024-04-13T09:09:03.11835',
      requestType: 'AP',
      merchantId: null,
    },
  ],
  number: 0,
  size: 10,
  totalElements: 35,
  totalPages: 4,
  hasContent: true,
  numberOfElements: 10,
  first: true,
  last: false,
};
