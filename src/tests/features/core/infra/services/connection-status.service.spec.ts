import { TestBed } from '@angular/core/testing';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  ConnectionStatusService,
  CONNECTION_STATUS_CONFIG,
  ConnectionStatusConfig,
} from 'src/app/features/core/infra/services/connection-status.service';

describe('ConnectionStatusService', () => {
  const DEFAULT_CONFIG: ConnectionStatusConfig = {
    notifierText: {
      title: 'Sin conexión a internet',
      message:
        'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
    },
    position: 'toast-bottom-left',
  };
  let service: ConnectionStatusService;
  let notifierServiceSpy: jasmine.SpyObj<NotifierService>;

  beforeEach(() => {
    notifierServiceSpy = jasmine.createSpyObj('NotifierService', ['warning']);

    TestBed.configureTestingModule({
      providers: [
        ConnectionStatusService,
        { provide: NotifierService, useValue: notifierServiceSpy },
        { provide: CONNECTION_STATUS_CONFIG, useValue: DEFAULT_CONFIG },
      ],
    });

    service = TestBed.inject(ConnectionStatusService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should expose online$ observable', () => {
    expect(service.online$).toBeDefined();
  });

  describe('when offline', () => {
    beforeEach(() => {
      spyOnProperty(navigator, 'onLine').and.returnValue(false);
      TestBed.resetTestingModule();

      TestBed.configureTestingModule({
        providers: [
          ConnectionStatusService,
          { provide: NotifierService, useValue: notifierServiceSpy },
          { provide: CONNECTION_STATUS_CONFIG, useValue: DEFAULT_CONFIG },
        ],
      });

      service = TestBed.inject(ConnectionStatusService);
    });

    it('should show a warning notification with default text', () => {
      expect(notifierServiceSpy.warning).toHaveBeenCalledWith({
        title: 'Sin conexión a internet',
        message:
          'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
      });
    });
  });

  describe('with custom configuration', () => {
    const customConfig = {
      notifierText: {
        title: 'Custom Offline Title',
        message: 'Custom Offline Message',
      },
      position: 'toast-top-right',
    };

    beforeEach(() => {
      TestBed.resetTestingModule();
      TestBed.configureTestingModule({
        providers: [
          { provide: NotifierService, useValue: notifierServiceSpy },
          { provide: CONNECTION_STATUS_CONFIG, useValue: customConfig },
        ],
      });

      spyOnProperty(navigator, 'onLine').and.returnValue(false);
      service = TestBed.inject(ConnectionStatusService);
    });

    it('should use custom notification text when offline', () => {
      expect(notifierServiceSpy.warning).toHaveBeenCalledWith({
        title: 'Custom Offline Title',
        message: 'Custom Offline Message',
      });
    });
  });

  describe('online/offline events', () => {
    beforeEach(() => {
      spyOn(window, 'addEventListener');
      TestBed.resetTestingModule();

      TestBed.configureTestingModule({
        providers: [
          { provide: NotifierService, useValue: notifierServiceSpy },
          { provide: CONNECTION_STATUS_CONFIG, useValue: DEFAULT_CONFIG },
        ],
      });

      service = TestBed.inject(ConnectionStatusService);
    });

    it('should register online and offline event listeners', () => {
      const onlineCall = (window.addEventListener as jasmine.Spy).calls
        .allArgs()
        .find(args => args[0] === 'online');
      expect(onlineCall).toBeTruthy();
      expect(onlineCall[0]).toBe('online');
      expect(typeof onlineCall[1]).toBe('function');

      const offlineCall = (window.addEventListener as jasmine.Spy).calls
        .allArgs()
        .find(args => args[0] === 'offline');
      expect(offlineCall).toBeTruthy();
      expect(offlineCall[0]).toBe('offline');
      expect(typeof offlineCall[1]).toBe('function');
    });

    it('should initialize online$ with the current navigator.onLine value', () => {
      spyOnProperty(navigator, 'onLine').and.returnValue(true);

      TestBed.resetTestingModule();
      TestBed.configureTestingModule({
        providers: [
          ConnectionStatusService,
          { provide: NotifierService, useValue: notifierServiceSpy },
          { provide: CONNECTION_STATUS_CONFIG, useValue: DEFAULT_CONFIG },
        ],
      });

      service = TestBed.inject(ConnectionStatusService);

      service.online$.subscribe(isOnline => {
        expect(isOnline).toBe(true);
      });
    });
  });
});
