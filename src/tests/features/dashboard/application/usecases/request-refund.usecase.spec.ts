import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, of, throwError } from 'rxjs';
import {
  cancelledRefundRequestMessage,
  RequestRefundUseCase,
  successRefundRequestMessage,
} from 'src/app/features/dashboard/application/usecases/request-refund.usecase';
import {
  RequestRefundRequest,
  RequestRefundResponse,
} from 'src/app/features/dashboard/domain/entities/refund';
import { RefundsRepository } from 'src/app/features/dashboard/domain/repositories/request-refund.repository';

describe('RequestRefundUsecase', () => {
  let usecase: RequestRefundUseCase;
  let loader: LoaderService;
  let notifier: NotifierService;
  let errorHandler: UseCaseErrorHandler;

  let repositorSpy: jasmine.SpyObj<RefundsRepository>;

  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;
  let infoNotifierSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        RequestRefundUseCase,
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: RefundsRepository,
          useValue: jasmine.createSpyObj('RequestRefundRepository', [
            'createOne',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(RequestRefundUseCase);
    loader = TestBed.inject(LoaderService);
    notifier = TestBed.inject(NotifierService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);
    repositorSpy = TestBed.inject(
      RefundsRepository
    ) as jasmine.SpyObj<RefundsRepository>;

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
    infoNotifierSpy = spyOn(notifier, 'info').and.callThrough();
    errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(RequestRefundUseCase);
  });

  it('should handle error when invalid args', async () => {
    // ts-expect-error: Testing purposes
    const request: RequestRefundRequest = {
      totalAmount: 10,
      cartId: 'cartId',
      reason: undefined,
    };

    const isFinishedEmpty = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isFinishedEmpty)
      .withContext(
        'should complete without emitting any value when error happens'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(0);
  });

  it('should handle error when invalid total amount', async () => {
    const request = {
      totalAmount: '10',
      cartId: 'cartId',
      reason: 'reason',
    } as any;

    const isFinishedEmpty = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isFinishedEmpty)
      .withContext(
        'should complete without emitting any value when totalAmount is not a number'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(0);
  });

  it('shoudl handle error when totalAmount is minor than 0', async () => {
    const request = {
      totalAmount: -10,
      cartId: 'cartId',
      reason: 'reason',
    };

    const isFinishedEmpty = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isFinishedEmpty)
      .withContext(
        'should complete without emitting any value when totalAmount is minor than 0'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(0);
  });

  it('should handle error when the total amount is exceeded', async () => {
    repositorSpy.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: {
              code: 'INVALID_REFUND',
              data: {
                error: 'Amount requested or sum of the refunded amount exceeds',
              },
            },
            status: 400,
          })
      )
    );

    const request = {
      totalAmount: 10,
      cartId: 'cartId',
      reason: 'reason',
    };

    const isFinishedEmpty = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isFinishedEmpty)
      .withContext(
        'should complete without emitting any value when totalAmount is exceeded'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when the cartId is not found', async () => {
    repositorSpy.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: {
              code: 'LOAN_DONT_EXISTS',
            },
            status: 400,
          })
      )
    );

    const request = {
      totalAmount: 10,
      cartId: 'cartId',
      reason: 'reason',
    };

    const isFinishedEmpty = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isFinishedEmpty)
      .withContext(
        'should complete without emitting any value when cartId is not found'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when unknown error happens', async () => {
    repositorSpy.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: {
              code: 'UNKNOWN_ERROR',
            },
            status: 500,
          })
      )
    );

    const request = {
      totalAmount: 10,
      cartId: 'cartId',
      reason: 'reason',
    };

    const isFinishedEmpty = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isFinishedEmpty)
      .withContext(
        'should complete without emitting any value when unknown error happens'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should complete successfully when refundStatus is REQUESTED', async () => {
    const expected: RequestRefundResponse = {
      refundId: 124,
      merchantId: 199,
      cartId: 'cartId',
      refundStatus: 'REQUESTED',
      refundDate: new Date('2024-05-01T00:00:00Z').toISOString(),
    };

    repositorSpy.createOne.and.returnValue(of(expected));

    const request = {
      totalAmount: 10,
      cartId: 'cartId',
      reason: 'reason',
    };

    const result = await lastValueFrom(usecase.execute(request));

    expect(result)
      .withContext(
        'should retrieve success response with refundStatus REQUESTED'
      )
      .toEqual(expected);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy)
      .withContext('success notification should be called with success message')
      .toHaveBeenCalledWith({
        title: successRefundRequestMessage,
      });
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  });

  it('should complete when refundStatus is CANCELLED and shows cancelledRefundRequestMessage', async () => {
    const expected: RequestRefundResponse = {
      refundId: 124,
      merchantId: 199,
      cartId: 'cartId',
      refundStatus: 'CANCELLED',
      refundDate: new Date('2024-05-01T00:00:00Z').toISOString(),
    };

    repositorSpy.createOne.and.returnValue(of(expected));

    const request = {
      totalAmount: 10,
      cartId: 'cartId',
      reason: 'reason',
    };

    const result = await lastValueFrom(usecase.execute(request));

    expect(result)
      .withContext(
        'should retrieve success response with refundStatus CANCELLED'
      )
      .toEqual(expected);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorSpy.createOne).toHaveBeenCalledTimes(1);
    expect(infoNotifierSpy).toHaveBeenCalledTimes(1);
    expect(infoNotifierSpy)
      .withContext(
        'success notification should be called with cancelledRefundRequestMessage'
      )
      .toHaveBeenCalledWith({
        title: cancelledRefundRequestMessage,
      });
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  });
});
