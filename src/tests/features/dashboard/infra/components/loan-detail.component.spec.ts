import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import {
  AplazoSimpleTableComponent,
  AplazoSimpleTableComponents,
} from '@aplazo/shared-ui/simple-table';
import { of } from 'rxjs';
import { LoanDetailsComponent } from 'src/app/features/dashboard/infra/components/loan-details.component';
import { SelectedLoanStore } from 'src/app/services/selected-loan.store';

const setup = (store: unknown) => {
  TestBed.configureTestingModule({
    imports: [AplazoSimpleTableComponents],
    providers: [
      {
        provide: SelectedLoanStore,
        useValue: {
          loan$: store,
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(LoanDetailsComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  return { fixture, component };
};

describe('LoanDetailsComponent', () => {
  it('should be created', () => {
    const { component } = setup(of(null));

    expect(component).toBeTruthy();
  });

  it('should does not render loan info when loan store has not selected loan', () => {
    const { fixture } = setup(of(null));

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    expect(table).withContext('Loan info should not be rendered').toBeFalsy();
  });

  it('should render loan info when loan store has selected loan', () => {
    const { fixture } = setup(of({ loan: { id: 123 } }));

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    expect(table).withContext('Loan info should be rendered').toBeTruthy();
  });
});
