import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { of } from 'rxjs';
import { LoanDetailsComponent } from 'src/app/features/dashboard/infra/components/loan-details.component';
import { SelectedLoanComponent } from 'src/app/features/dashboard/infra/components/selected-loan/selected-loan.component';
import { Refund } from 'src/app/features/refund/domain/entities/refund';
import { IfValidRoleDirective } from 'src/app/features/shared/directives/if-valid-role.directive';
import { UserStoreService } from 'src/app/features/user/src/application/services/user-store.service';
import { ValidMerchantRoles } from 'src/app/features/user/src/domain/entities/valid-roles';
import { SelectedLoanStore } from 'src/app/services/selected-loan.store';

const setup = (
  store: unknown,
  role: ValidMerchantRoles = 'ROLE_PANEL_ADMIN',
  refunds?: Refund[]
) => {
  const selectedLoanStoreMock = {
    loan$: store,
    refunds$: of(refunds ?? []),
  };
  TestBed.configureTestingModule({
    imports: [
      LoanDetailsComponent,
      IfValidRoleDirective,
      AplazoSimpleTableComponents,
      AplazoButtonComponent,
      AplazoDynamicPipe,
      NgIf,
      NgFor,
      AsyncPipe,
    ],
    providers: [
      {
        provide: SelectedLoanStore,
        useValue: selectedLoanStoreMock,
      },
      {
        provide: UserStoreService,
        useValue: {
          getRole: () => role,
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(SelectedLoanComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  return { fixture, component };
};

describe('SelectedLoanComponent', () => {
  it('should be created', () => {
    const { component } = setup(
      of({
        loan: {
          id: 123,
          status: 'Aprobado',
        },
      })
    );

    expect(component).toBeTruthy();
  });

  it('should call emit event when requestRefund is called', () => {
    const { component } = setup(
      of({
        loan: {
          id: 123,
          status: 'Aprobado',
        },
      })
    );

    const spy = spyOn(component.refundRequested, 'emit').and.callThrough();

    component.requestRefund();

    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('should show refund button when role is valid and loan status is "Aprobado"', () => {
    const { fixture } = setup(
      of({
        loan: {
          id: 123,
          status: 'Aprobado',
        },
      })
    );

    const button = fixture.debugElement.query(
      By.directive(AplazoButtonComponent)
    );

    expect(button).withContext('Refund button should be shown').toBeTruthy();
  });

  it('should not show refund button when role is invalid', () => {
    const { fixture } = setup(
      of({
        loan: {
          id: 123,
          status: 'Aprobado',
        },
      }),
      'ROLE_MERCHANT'
    );

    const button = fixture.debugElement.query(
      By.directive(AplazoButtonComponent)
    );

    expect(button)
      .withContext('Refund button should not be shown when role is invalid')
      .toBeFalsy();
  });

  it('should not show refund button when loan status is not "Aprobado"', () => {
    const { fixture } = setup(
      of({
        loan: {
          id: 123,
          status: 'Rechazado',
        },
      })
    );

    const button = fixture.debugElement.query(
      By.directive(AplazoButtonComponent)
    );

    expect(button)
      .withContext(
        'Refund button should not be shown when loan status is not "Aprobado"'
      )
      .toBeFalsy();
  });
});
