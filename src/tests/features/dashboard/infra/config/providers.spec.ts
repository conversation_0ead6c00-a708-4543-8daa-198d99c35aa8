import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { RefundsRepository } from 'src/app/features/dashboard/domain/repositories/request-refund.repository';
import { provideDashboardRefundsDeps } from 'src/app/features/dashboard/infra/config/providers';
import { RequestRefundWithHttpRepository } from 'src/app/features/dashboard/infra/repositories/refunds-with-http.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';

describe('provide dashboard refunds deps', () => {
  let service: RefundsRepository;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideDashboardRefundsDeps(),
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'http://localhost:3000/',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    service = TestBed.inject(RefundsRepository);
  });

  it('should create an instance of RequestRefundWithHttpRepository', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(RequestRefundWithHttpRepository);
  });
});
