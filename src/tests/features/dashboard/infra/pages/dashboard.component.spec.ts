import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { provideTemporal, RedirectionService } from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideRedirecterTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponent,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponent,
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
  AplazoStatCardComponent,
  AplazoStatsComponent,
  MetricCardComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { DialogService } from '@ngneat/dialog';
import { of, throwError } from 'rxjs';
import { SelectedLoanComponent } from 'src/app/features/dashboard/infra/components/selected-loan/selected-loan.component';
import { GetActiveBannerUsecase } from 'src/app/features/dynamic-banners/application/get-active-banner.usecase';
import { MerchantLoan } from 'src/app/features/loan/domain/entities/merchant-loan';
import { provideI18NTesting } from 'src/tests/i18n.local';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { PaymentBalanceAllInvoicesDownloadUsecase } from '../../../../../app/features/balance/application/usecases/payment-balance-all-invoices-download.usecase';
import { PaymentBalanceAllReceiptsDownloadUseCase } from '../../../../../app/features/balance/application/usecases/payment-balance-all-receipts-download.usecase';
import { PaymentBalanceSummaryReportUsecase } from '../../../../../app/features/balance/application/usecases/payment-balance-summary-report.usecase';
import { CriteriaBalanceService } from '../../../../../app/features/balance/infra/services/criteria-balance.service';
import { RequestRefundUseCase } from '../../../../../app/features/dashboard/application/usecases/request-refund.usecase';
import { DashboardComponent } from '../../../../../app/features/dashboard/infra/pages/dashboard/dashboard.component';
import { LoanDetailsUseCase } from '../../../../../app/features/loan/application/usecases/loan-details.usecase';
import { MerchantLoansBySearchUseCase } from '../../../../../app/features/loan/application/usecases/loans-by-search.usecase';
import { MerchantLoansComparisonStatisticsUseCase } from '../../../../../app/features/loan/application/usecases/loans-comparison-statistics.usecase';
import { MerchantLoansListUseCase } from '../../../../../app/features/loan/application/usecases/loans-list.usecase';
import { GetLoansStatementSummaryUseCase } from '../../../../../app/features/loan/application/usecases/loans-statement-summary.usecase';
import { MerchantLoansStatisticsUseCase } from '../../../../../app/features/loan/application/usecases/loans-statistics.usecase';
import { RefundSearchUseCase } from '../../../../../app/features/refund/application/usecases/refund-search.usecase';
import { SearchComponent } from '../../../../../app/features/shared/components/search.component';
import { UserStoreService } from '../../../../../app/features/user/src/application/services/user-store.service';
import { EventManagerService } from '../../../../../app/services/event-manger.service';
import { SelectedLoanStore } from '../../../../../app/services/selected-loan.store';
import { SharedCriteria } from '../../../../../app/services/shared-criteria.store';

const pageable = (content: any) => ({
  content,
  pageable: {
    pageNumber: 0,
    pageSize: 10,
    sort: [],
    offset: 0,
    paged: true,
    unpaged: false,
  },
  totalPages: content?.length ? content.length / 10 : 0,
  totalElements: content?.length ?? 0,
  last: false,
  numberOfElements: (content?.length ?? 0) > 10 ? 10 : content.length,
  first: true,
  size: 10,
  number: 0,
  sort: [],
  empty: content?.length === 0,
});

const loanStatementSummaryMock = {
  nextPayment: 1000,
  latestPayment: 500,
};

const loansStatisticsMock = {
  merchantId: 199,
  salesAmount: 405,
  salesOrder: 3,
  salesAmountAvg: 135,
  customers: 3,
};

const loansComparisonStatisticsMock = {
  comparisonStats: {
    merchantId: 199,
    salesAmount: -0.4536406495547407,
    salesOrder: 1.6666666666666665,
    salesAmountAvg: -0.7951152435830278,
    customers: 0.6666666666666667,
  },
  currentMonth: {
    merchantId: 199,
    salesAmount: 1043,
    salesOrder: 8,
    salesAmountAvg: 130.375,
    customers: 5,
  },
  previousMonth: {
    merchantId: 199,
    salesAmount: 1909,
    salesOrder: 3,
    salesAmountAvg: 636.3333333333334,
    customers: 3,
  },
};

const setup = (
  args: {
    loansList?: MerchantLoan[];
    stats?: any;
    search?: any[];
    comparisonStats?: any;
    loanStatement?: any;
    selectedLoan?: any;
  } = {}
) => {
  const defaultArgs = {
    loansList: [
      {
        id: 193327,
        creationDate: '2025-07-02T07:26:00.908775',
        total: 150,
        cartId: 'QA-Automation-REST-OFFLINE-01-786554',
        shopId: '422',
        status: 'APROBADO',
        customerUrl:
          'https://merchantdash.aplazo.net/api/v1/merchant/customer/35714',
        branch: 'QA_Automation_Dont_Use',
        productUrl:
          'https://merchantdash.aplazo.net/api/v1/merchant/loan/193327/product',
        fees: 9.86,
      },
      {
        id: 193335,
        creationDate: '2025-07-02T08:03:14.250377',
        total: 105,
        cartId: '1XgbRU13QEGWP5DWt2XuL',
        shopId: '422',
        status: 'APROBADO',
        customerUrl:
          'https://merchantdash.aplazo.net/api/v1/merchant/customer/27136',
        branch: 'QA_Automation_Dont_Use',
        productUrl:
          'https://merchantdash.aplazo.net/api/v1/merchant/loan/193335/product',
        fees: 7.25,
      },
      {
        id: 193361,
        creationDate: '2025-07-02T10:20:34.749404',
        total: 150,
        cartId: 'QA-Automation-REST-OFFLINE-01-808437',
        shopId: '422',
        status: 'APROBADO',
        customerUrl:
          'https://merchantdash.aplazo.net/api/v1/merchant/customer/35724',
        branch: 'QA_Automation_Dont_Use',
        productUrl:
          'https://merchantdash.aplazo.net/api/v1/merchant/loan/193361/product',
        fees: 9.86,
      },
    ],
  };

  const finalConfig = {
    loansList: args.loansList ? args.loansList : defaultArgs.loansList,
  };

  TestBed.configureTestingModule({
    imports: [
      AsyncPipe,
      I18nPluralPipe,
      ReactiveFormsModule,
      AplazoDynamicPipe,
      AplazoButtonComponent,
      AplazoCardComponent,
      AplazoFormDatepickerComponent,
      AplazoFormFieldDirectives,
      AplazoIconComponent,
      AplazoSearchInputComponent,
      AplazoSelectComponents,
      AplazoStatsComponent,
      AplazoCommonMessageComponents,
      AplazoPaginationComponent,
      AplazoSimpleTableComponents,
      AplazoDropdownComponents,
      SelectedLoanComponent,
      AplazoMetricCardComponents,
      AplazoTooltipDirective,
      SearchComponent,
    ],
    providers: [
      provideNotifierTesting(),
      provideTemporal(),
      provideRedirecterTesting(),
      provideI18NTesting('dashboard', 'balance'),
      SharedCriteria,
      CriteriaBalanceService,
      AplazoIconRegistryService,
      DialogService,
      SelectedLoanStore,
      {
        provide: EventManagerService,
        useValue: {
          sendTrackEvent: () => {
            return void 0;
          },
        },
      },
      {
        provide: MERCHANT_CORE_ENVIRONMENT,
        useValue: { redirectionsLandingPage: 'https://example.com' },
      },
      {
        provide: RequestRefundUseCase,
        useValue: {
          execute: jasmine
            .createSpy('execute')
            .and.returnValue(of({ refundStatus: 'APPROVED' })),
        },
      },
      {
        provide: RefundSearchUseCase,
        useValue: {
          execute: (args: any) => of(null),
        },
      },
      {
        provide: PaymentBalanceAllReceiptsDownloadUseCase,
        useValue: {
          execute: jasmine.createSpy('execute').and.returnValue(of(null)),
        },
      },
      {
        provide: PaymentBalanceAllInvoicesDownloadUsecase,
        useValue: {
          execute: jasmine.createSpy('execute').and.returnValue(of(null)),
        },
      },
      {
        provide: PaymentBalanceSummaryReportUsecase,
        useValue: {
          execute: jasmine.createSpy('execute').and.returnValue(of(null)),
        },
      },
      {
        provide: UserStoreService,
        useValue: { merchantId$: of(199) },
      },
      {
        provide: MerchantLoansListUseCase,
        useValue: {
          execute: (args: any) => {
            return of(pageable(finalConfig.loansList));
          },
        },
      },
      {
        provide: MerchantLoansBySearchUseCase,
        useValue: {
          execute: (args: any) => {
            return of(finalConfig.loansList);
          },
        },
      },
      {
        provide: GetLoansStatementSummaryUseCase,
        useValue: {
          execute: (args: any) => {
            return of(loanStatementSummaryMock);
          },
        },
      },
      {
        provide: MerchantLoansStatisticsUseCase,
        useValue: {
          execute: (args: any) => {
            return of(loansStatisticsMock);
          },
        },
      },
      {
        provide: MerchantLoansComparisonStatisticsUseCase,
        useValue: {
          execute: (args: any) => {
            return of(loansComparisonStatisticsMock);
          },
        },
      },
      {
        provide: LoanDetailsUseCase,
        useValue: {
          execute: (args: any) => {
            return of({ customer: {}, products: [] });
          },
        },
      },
      {
        provide: GetActiveBannerUsecase,
        useValue: {
          execute: () => of(null),
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(DashboardComponent);
  const component = fixture.componentInstance;
  fixture.detectChanges();

  const loansListUseCase = TestBed.inject(MerchantLoansListUseCase);
  const loansBySearchUseCase = TestBed.inject(MerchantLoansBySearchUseCase);
  const loansStatisticsUseCase = TestBed.inject(MerchantLoansStatisticsUseCase);
  const loansComparisonStatisticsUseCase = TestBed.inject(
    MerchantLoansComparisonStatisticsUseCase
  );
  const loanDetailsUseCase = TestBed.inject(LoanDetailsUseCase);
  const refundSearchUseCase = TestBed.inject(RefundSearchUseCase);
  const requestRefundUseCase = TestBed.inject(RequestRefundUseCase);
  const eventManagerService = TestBed.inject(EventManagerService);
  const dialogService = TestBed.inject(DialogService);
  const selectedLoanStore = TestBed.inject(SelectedLoanStore);
  const sharedCriteria = TestBed.inject(SharedCriteria);
  const redirectionService = TestBed.inject(RedirectionService);
  const downloadReceiptsUseCase = TestBed.inject(
    PaymentBalanceAllReceiptsDownloadUseCase
  );
  const downloadInvoicesUseCase = TestBed.inject(
    PaymentBalanceAllInvoicesDownloadUsecase
  );
  const downloadSummaryUseCase = TestBed.inject(
    PaymentBalanceSummaryReportUsecase
  );

  return {
    fixture,
    component,
    loansListUseCase,
    loansBySearchUseCase,
    loansStatisticsUseCase,
    loansComparisonStatisticsUseCase,
    loanDetailsUseCase,
    refundSearchUseCase,
    requestRefundUseCase,
    eventManagerService,
    dialogService,
    selectedLoanStore,
    sharedCriteria,
    redirectionService,
    downloadReceiptsUseCase,
    downloadInvoicesUseCase,
    downloadSummaryUseCase,
  };
};

describe('DashboardComponent', () => {
  it('should create', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should display search input', () => {
    const { fixture } = setup();
    const searchInput = fixture.debugElement.query(
      By.directive(AplazoSearchInputComponent)
    );
    expect(searchInput).toBeTruthy();
  });

  it('should display date range picker', () => {
    const { fixture } = setup();
    const datePicker = fixture.debugElement.query(
      By.directive(AplazoFormDatepickerComponent)
    );
    expect(datePicker).toBeTruthy();
  });

  it('should display order type selector', () => {
    const { fixture } = setup();
    const orderTypeSelect = fixture.debugElement.query(
      By.directive(AplazoSelectComponent)
    );
    expect(orderTypeSelect).toBeTruthy();
  });

  it('should show pagination when there are multiple pages of loans', () => {
    const { fixture } = setup({
      loansList: [
        {
          id: 193327,
          creationDate: '2025-07-02T07:26:00.908775',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-786554',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35714',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193327/product',
          fees: 9.86,
        },
        {
          id: 193335,
          creationDate: '2025-07-02T08:03:14.250377',
          total: 105,
          cartId: '1XgbRU13QEGWP5DWt2XuL',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/27136',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193335/product',
          fees: 7.25,
        },
        {
          id: 193361,
          creationDate: '2025-07-02T10:20:34.749404',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-808437',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35724',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193361/product',
          fees: 9.86,
        },
        {
          id: 193327,
          creationDate: '2025-07-02T07:26:00.908775',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-786554',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35714',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193327/product',
          fees: 9.86,
        },
        {
          id: 193335,
          creationDate: '2025-07-02T08:03:14.250377',
          total: 105,
          cartId: '1XgbRU13QEGWP5DWt2XuL',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/27136',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193335/product',
          fees: 7.25,
        },
        {
          id: 193361,
          creationDate: '2025-07-02T10:20:34.749404',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-808437',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35724',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193361/product',
          fees: 9.86,
        },
        {
          id: 193327,
          creationDate: '2025-07-02T07:26:00.908775',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-786554',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35714',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193327/product',
          fees: 9.86,
        },
        {
          id: 193335,
          creationDate: '2025-07-02T08:03:14.250377',
          total: 105,
          cartId: '1XgbRU13QEGWP5DWt2XuL',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/27136',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193335/product',
          fees: 7.25,
        },
        {
          id: 193361,
          creationDate: '2025-07-02T10:20:34.749404',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-808437',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35724',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193361/product',
          fees: 9.86,
        },
        {
          id: 193327,
          creationDate: '2025-07-02T07:26:00.908775',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-786554',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35714',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193327/product',
          fees: 9.86,
        },
        {
          id: 193335,
          creationDate: '2025-07-02T08:03:14.250377',
          total: 105,
          cartId: '1XgbRU13QEGWP5DWt2XuL',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/27136',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193335/product',
          fees: 7.25,
        },
        {
          id: 193361,
          creationDate: '2025-07-02T10:20:34.749404',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-808437',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35724',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193361/product',
          fees: 9.86,
        },
        {
          id: 193327,
          creationDate: '2025-07-02T07:26:00.908775',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-786554',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35714',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193327/product',
          fees: 9.86,
        },
        {
          id: 193335,
          creationDate: '2025-07-02T08:03:14.250377',
          total: 105,
          cartId: '1XgbRU13QEGWP5DWt2XuL',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/27136',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193335/product',
          fees: 7.25,
        },
        {
          id: 193361,
          creationDate: '2025-07-02T10:20:34.749404',
          total: 150,
          cartId: 'QA-Automation-REST-OFFLINE-01-808437',
          shopId: '422',
          status: 'APROBADO',
          customerUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/customer/35724',
          branch: 'QA_Automation_Dont_Use',
          productUrl:
            'https://merchantdash.aplazo.net/api/v1/merchant/loan/193361/product',
          fees: 9.86,
        },
      ],
    });
    const paginationComponent = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );
    expect(paginationComponent).toBeTruthy();
  });

  it('should not show pagination when there is only one page of loans', () => {
    const { fixture } = setup({
      loansList: [],
    });
    const paginationComponent = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );
    expect(paginationComponent).toBeFalsy();
  });

  it('should show a message when there are no loans', () => {
    const { fixture } = setup({
      loansList: [],
    });
    const emptyMessage = fixture.debugElement.query(
      By.directive(AplazoCommonMessageComponent)
    );

    expect(emptyMessage).toBeTruthy();
  });

  it('should call setPageNum on page change', () => {
    const { component, sharedCriteria, eventManagerService } = setup();

    const spy = spyOn(sharedCriteria, 'setPageNum').and.callThrough();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    component.changePage(2);

    expect(spy).toHaveBeenCalledWith(2);
    expect(eventSpy).toHaveBeenCalledWith('pagination', { pageNum: 2 });
  });

  it('should call setSearch when search value changes', fakeAsync(() => {
    const { fixture, component, sharedCriteria, eventManagerService } = setup();

    const spy = spyOn(sharedCriteria, 'setSearch').and.callThrough();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    component.searchControl.setValue('test search');

    fixture.detectChanges();

    tick(500);

    expect(spy).toHaveBeenCalledWith('test search');
    expect(eventSpy).toHaveBeenCalledWith('search', {
      searchTerm: 'test search',
    });
  }));

  it('should call setStatus when order type changes', fakeAsync(() => {
    const { fixture, component, sharedCriteria, eventManagerService } = setup();

    const spy = spyOn(sharedCriteria, 'setStatus').and.callThrough();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    component.orderTypeControl.setValue('Todos los pedidos');

    fixture.detectChanges();

    tick();

    expect(spy).toHaveBeenCalledWith('all');
    expect(eventSpy).toHaveBeenCalledWith('status', {
      status: 'all',
    });
  }));

  it('should call setDateRange when date range changes', fakeAsync(() => {
    const { fixture, component, sharedCriteria, eventManagerService } = setup();

    const spy = spyOn(sharedCriteria, 'setDateRange').and.callThrough();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    const dates = [new Date('2024-01-01'), new Date('2024-01-31')];
    component.dateControl.setValue(dates);

    fixture.detectChanges();

    tick();

    expect(spy).toHaveBeenCalled();
    expect(eventSpy).toHaveBeenCalled();
  }));

  it('should redirect to landing page when empty loans button is clicked', () => {
    const { component, redirectionService } = setup({
      loansList: [],
    });

    const spy = spyOn(
      redirectionService,
      'externalNavigation'
    ).and.callThrough();

    component.clickEmptyLoansButton();

    expect(spy).toHaveBeenCalledWith(
      'https://example.com/para-comercios',
      '_blank'
    );
  });

  it('should execute download summary report', () => {
    const { component, downloadSummaryUseCase, eventManagerService } = setup();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    component.downloadSummaryReport();

    expect(downloadSummaryUseCase.execute).toHaveBeenCalled();
    expect(eventSpy).toHaveBeenCalled();
  });

  it('should execute download all receipts', () => {
    const { component, downloadReceiptsUseCase, eventManagerService } = setup();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    component.downloadAllReceipts();

    expect(downloadReceiptsUseCase.execute).toHaveBeenCalled();
    expect(eventSpy).toHaveBeenCalled();
  });

  it('should execute download all invoices', () => {
    const { component, downloadInvoicesUseCase, eventManagerService } = setup();
    const eventSpy = spyOn(
      eventManagerService,
      'sendTrackEvent'
    ).and.callThrough();

    component.downloadAllInvoices();

    expect(downloadInvoicesUseCase.execute).toHaveBeenCalled();
    expect(eventSpy).toHaveBeenCalled();
  });

  it('should handle error when download fails', () => {
    const { component, downloadSummaryUseCase } = setup();
    (downloadSummaryUseCase.execute as jasmine.Spy).and.returnValue(
      throwError(() => new Error('Download failed'))
    );

    expect(() => component.downloadSummaryReport()).not.toThrow();
  });

  it('should clear selected loan on destroy', () => {
    const { component, selectedLoanStore } = setup();
    const spy = spyOn(selectedLoanStore, 'clearLoan').and.callThrough();

    component.ngOnDestroy();

    expect(spy).toHaveBeenCalled();
  });

  it('should display correct number of metric cards', async () => {
    const { fixture } = setup();

    const metricCards = fixture.debugElement.queryAll(
      By.directive(MetricCardComponent)
    );

    expect(metricCards.length).toBe(9);
  });
});
