import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { SelectedLoanStore } from 'src/app/services/selected-loan.store';
import { IMerchantLoanUiDto } from '../../../../../app/features/loan/domain/dtos/merchant-loan-ui.dto';
import { RefundSearchRepository } from '../../../../../app/features/refund/domain/repositories/refund-search.repository';

describe('SelectedLoanStore', () => {
  let selectedLoanStore: SelectedLoanStore;
  let refundSearchRepositoryMock: jasmine.SpyObj<RefundSearchRepository>;

  const loan: IMerchantLoanUiDto = {
    loan: {
      id: 1,
      branch: {} as any,
      cartId: 'ab1',
      total: 122,
    } as any,
    customer: {
      email: '<EMAIL>',
      name: 'Test',
      phoneNumber: '**********',
    },
    products: [],
  };

  beforeEach(() => {
    refundSearchRepositoryMock = jasmine.createSpyObj<RefundSearchRepository>(
      'RefundSearchRepository',
      ['findRefund']
    );
    refundSearchRepositoryMock.findRefund.and.returnValue(of([]));
    TestBed.configureTestingModule({
      providers: [
        SelectedLoanStore,
        {
          provide: RefundSearchRepository,
          useValue: refundSearchRepositoryMock,
        },
      ],
    });

    selectedLoanStore = TestBed.inject(SelectedLoanStore);
  });

  it('should be created', () => {
    expect(selectedLoanStore).toBeTruthy();
    expect(selectedLoanStore).toBeInstanceOf(SelectedLoanStore);
  });

  it('should set loan info', done => {
    let result: any;

    selectedLoanStore.loan$.subscribe(loan => {
      result = loan;
    });

    selectedLoanStore.setInfo(loan);

    expect(result).toEqual(loan);
    done();
  });

  it('should throw error when loan is null', () => {
    expect(() => selectedLoanStore.setInfo(null)).toThrowError();
  });

  it('should clear loan', done => {
    let result: any;

    selectedLoanStore.loan$.subscribe(loan => {
      result = loan;
    });

    selectedLoanStore.setInfo(loan);

    expect(result).toEqual(loan);

    selectedLoanStore.clearLoan();

    expect(result).toBeNull();
    done();
  });

  it('should reflect hasSelectedLoan', () => {
    expect(selectedLoanStore.hasSelectedLoan).toBeFalse();

    selectedLoanStore.setInfo(loan);

    expect(selectedLoanStore.hasSelectedLoan).toBeTrue();

    selectedLoanStore.clearLoan();

    expect(selectedLoanStore.hasSelectedLoan).toBeFalse();
  });
});
