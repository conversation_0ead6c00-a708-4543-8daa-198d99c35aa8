import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { RequestRefundWithHttpRepository } from 'src/app/features/dashboard/infra/repositories/refunds-with-http.repository';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../app/config/merchant-core.environment';

describe('RequestRefundWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let service: RequestRefundWithHttpRepository;
  let httpSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        RequestRefundWithHttpRepository,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMicroserviceBaseUrl: 'https://merchantdash.aplazo.net/',
          },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(RequestRefundWithHttpRepository);
    httpSpy = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should executed successfully', () => {
    const expected = {
      refundId: 124,
      merchantId: 199,
      cartId: 'cartId',
      refundStatus: 'REQUESTED',
      refundDate: new Date('2024-05-01T00:00:00Z').toISOString(),
    };

    service
      .createOne({
        cartId: 'cartId',
        totalAmount: 100,
        reason: 'reason',
      })
      .subscribe({
        next: response => {
          expect(response).toBeTruthy();
          expect(response).toEqual(expected);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/refund'
    );

    expect(req.request.method).toBe('POST');

    req.flush(expected);
  });

  it('should throw error when execute throw error', () => {
    service
      .createOne({
        cartId: 'cartId',
        totalAmount: 100,
        reason: 'reason',
      })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeTruthy();
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(httpSpy).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      'https://merchantdash.aplazo.net/api/v1/merchant/refund'
    );

    req.flush('Deliberate error', { status: 404, statusText: 'Not Found' });
  });
});
