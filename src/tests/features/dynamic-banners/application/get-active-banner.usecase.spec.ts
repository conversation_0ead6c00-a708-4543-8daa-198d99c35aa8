import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetActiveBannerUsecase } from 'src/app/features/dynamic-banners/application/get-active-banner.usecase';
import {
  DynamicBanner,
  fromDynamicBannerToDynamicBannerContentUI,
} from 'src/app/features/dynamic-banners/domain/entities/banner';
import { DynamicBannerRepository } from 'src/app/features/dynamic-banners/domain/repositories/dynamic-banner.repository';

const setup = () => {
  const repositorySpy = jasmine.createSpyObj('DynamicBannerRepository', [
    'getTheOneActiveBanner',
  ]);
  const loaderSpy = jasmine.createSpyObj('LoaderService', ['show', 'hide']);
  const errorHandlerSpy = jasmine.createSpyObj('UseCaseErrorHandler', [
    'handle',
  ]);

  TestBed.configureTestingModule({
    providers: [
      GetActiveBannerUsecase,
      provideLoaderTesting(),
      provideUseCaseErrorHandlerTesting(),
      { provide: DynamicBannerRepository, useValue: repositorySpy },
      { provide: LoaderService, useValue: loaderSpy },
      { provide: UseCaseErrorHandler, useValue: errorHandlerSpy },
    ],
  });

  const usecase = TestBed.inject(GetActiveBannerUsecase);
  const repository = TestBed.inject(
    DynamicBannerRepository
  ) as jasmine.SpyObj<DynamicBannerRepository>;
  const loader = TestBed.inject(LoaderService) as jasmine.SpyObj<LoaderService>;
  const errorHandler = TestBed.inject(
    UseCaseErrorHandler
  ) as jasmine.SpyObj<UseCaseErrorHandler>;

  // Set up common spy behavior
  loader.show.and.returnValue('loader-id');

  return {
    usecase,
    repository,
    loader,
    errorHandler,
  };
};

describe('GetActiveBannerUsecase', () => {
  // Test constants - defined once at the top level
  const testRequest = {
    merchantId: 1,
    branchId: 2,
  };

  const mockBanner: DynamicBanner = {
    id: 1,
    title: 'Test Banner',
    message: 'Test Message',
    applyAll: true,
    merchantIds: [1],
    branchIds: [2],
    buttonVisible: true,
    buttonLabel: 'Click Me',
    redirectUrl: 'https://example.com',
    bannerVisible: true,
    imageUrl: 'https://example.com/image.png',
    endAt: '2023-12-31T23:59:59Z',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  };

  describe('Input Validation', () => {
    it('should throw an error when merchantId is missing', () => {
      // Arrange
      const { usecase } = setup();
      const invalidRequest = { merchantId: undefined as any, branchId: 1 };

      // Act & Assert
      expect(() => {
        usecase.execute(invalidRequest);
      }).toThrow(
        new RuntimeMerchantError(
          'El ID del comercio es requerido',
          'GetActiveBannerUsecase::null::merchantId'
        )
      );
    });

    it('should throw an error when merchantId is invalid (≤ 0)', () => {
      // Arrange
      const { usecase } = setup();
      const invalidRequest = { merchantId: 0, branchId: 1 };

      // Act & Assert
      expect(() => {
        usecase.execute(invalidRequest);
      }).toThrow(
        new RuntimeMerchantError(
          'El ID del comercio debe ser mayor que 0',
          'GetActiveBannerUsecase::invalid::merchantId'
        )
      );
    });

    it('should throw an error when merchantId is non-numeric', () => {
      // Arrange
      const { usecase } = setup();
      const invalidRequest = { merchantId: 'abc' as any, branchId: 1 };

      // Act & Assert
      expect(() => {
        usecase.execute(invalidRequest);
      }).toThrow();
    });
  });

  describe('Execution Flow', () => {
    it('should show loader, call repository, and hide loader on successful execution', fakeAsync(() => {
      // Arrange
      const { usecase, repository, loader } = setup();
      const expectedUI = fromDynamicBannerToDynamicBannerContentUI(mockBanner);
      repository.getTheOneActiveBanner.and.returnValue(of(mockBanner));

      // Act
      let result: any;
      usecase.execute(testRequest).subscribe(res => {
        result = res;
      });
      tick(); // Process async operations

      // Assert
      expect(result).toEqual(expectedUI);
      expect(loader.show).toHaveBeenCalled();
      expect(repository.getTheOneActiveBanner).toHaveBeenCalledWith(
        testRequest
      );
      expect(loader.hide).toHaveBeenCalledWith('loader-id');
    }));

    it('should return null when repository returns null', fakeAsync(() => {
      // Arrange
      const { usecase, repository, loader } = setup();
      repository.getTheOneActiveBanner.and.returnValue(of(null));

      // Act
      let result: any;
      usecase.execute(testRequest).subscribe(res => {
        result = res;
      });
      tick(); // Process async operations

      // Assert
      expect(result).toBeNull();
      expect(loader.show).toHaveBeenCalled();
      expect(repository.getTheOneActiveBanner).toHaveBeenCalledWith(
        testRequest
      );
      expect(loader.hide).toHaveBeenCalledWith('loader-id');
    }));

    it('should handle repository errors correctly', fakeAsync(() => {
      // Arrange
      const { usecase, repository, errorHandler, loader } = setup();
      const error = new Error('Repository error');
      repository.getTheOneActiveBanner.and.returnValue(throwError(() => error));
      errorHandler.handle.and.returnValue(throwError(() => error));

      // Act
      let caughtError: any;
      usecase.execute(testRequest).subscribe({
        error: err => {
          caughtError = err;
        },
      });
      tick(); // Process async operations

      // Assert
      expect(caughtError).toBe(error);
      expect(loader.show).toHaveBeenCalled();
      expect(repository.getTheOneActiveBanner).toHaveBeenCalledWith(
        testRequest
      );
      expect(errorHandler.handle).toHaveBeenCalledWith(error);
      expect(loader.hide).toHaveBeenCalledWith('loader-id');
    }));
  });
});
