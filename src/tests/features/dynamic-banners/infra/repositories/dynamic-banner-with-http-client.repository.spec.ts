import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from 'src/app/config/merchant-core.environment';
import { DynamicBanner } from 'src/app/features/dynamic-banners/domain/entities/banner';
import { DynamicBannerWithHttpClientRepository } from 'src/app/features/dynamic-banners/infra/repositories/dynamic-banner-with-http-client.repository';

describe('DynamicBannerWithHttpClientRepository', () => {
  let repository: DynamicBannerWithHttpClientRepository;
  let httpMock: HttpTestingController;
  let environment: { promoApiUrl: string };

  const testRequest = {
    merchantId: 1,
    branchId: 2,
  };

  const mockBanner: DynamicBanner = {
    id: 1,
    title: 'Test Banner',
    message: 'Test Message',
    applyAll: true,
    merchantIds: [1],
    branchIds: [2],
    buttonVisible: true,
    buttonLabel: 'Click Me',
    redirectUrl: 'https://example.com',
    bannerVisible: true,
    imageUrl: 'https://example.com/image.png',
    endAt: '2023-12-31T23:59:59Z',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        DynamicBannerWithHttpClientRepository,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: { promoApiUrl: 'https://api.test/' },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    repository = TestBed.inject(DynamicBannerWithHttpClientRepository);
    httpMock = TestBed.inject(HttpTestingController);
    environment = TestBed.inject(MERCHANT_CORE_ENVIRONMENT);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should correctly form the API URL and handle the response', () => {
    // Act
    let result: any;
    repository.getTheOneActiveBanner(testRequest).subscribe(res => {
      result = res;
    });

    // Expect a GET request to the correct URL
    const req = httpMock.expectOne(
      `${environment.promoApiUrl}api/v1/banners?merchantId=${testRequest.merchantId}&platform=MP`
    );
    expect(req.request.method).toBe('GET');

    // Respond with mock data
    req.flush(mockBanner);

    // Assert
    expect(result).toEqual(mockBanner);
  });

  it('should handle empty response', () => {
    // Act
    let result: any;
    repository.getTheOneActiveBanner(testRequest).subscribe(res => {
      result = res;
    });

    // Expect a GET request to the correct URL
    const req = httpMock.expectOne(
      `${environment.promoApiUrl}api/v1/banners?merchantId=${testRequest.merchantId}&platform=MP`
    );

    // Respond with null data
    req.flush(null);

    // Assert
    expect(result).toBeNull();
  });

  it('should handle error response', () => {
    // Act
    let error: any;
    repository.getTheOneActiveBanner(testRequest).subscribe({
      error: err => (error = err),
    });

    // Expect a GET request to the correct URL
    const req = httpMock.expectOne(
      `${environment.promoApiUrl}api/v1/banners?merchantId=${testRequest.merchantId}&platform=MP`
    );

    // Respond with an error
    req.flush('Not Found', { status: 404, statusText: 'Not Found' });

    // Assert
    expect(error).toBeTruthy();
    expect(error.status).toBe(404);
    expect(error.statusText).toBe('Not Found');
  });
});
