import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { lastValueFrom, Observable, of } from 'rxjs';
import { GetAllBranchOfficesUsecase } from 'src/app/features/branch-offices/src/application/usecases/get-all-branch-offices.usecase';
import { IBranchOfficeUIDto } from '../../../../app/features/branch-offices/src/domain/entities/branch-office';
import { getBranchOffices } from '../../../../app/features/home/<USER>/branchOffice.resolver';
import { BranchOfficesStore } from '../../../../app/services/branch-offices.store';

@Component({
  standalone: true,
  template: '',
})
export class TestComponent {}

describe('getBranchOffices', () => {
  let usecaseSpy: jasmine.Spy;
  let storeSpy: jasmine.Spy;
  let resolver: Observable<IBranchOfficeUIDto[]>;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [TestComponent],
      providers: [
        {
          provide: BranchOfficesStore,
          useValue: {
            setBranchOffices: () => {
              void 0;
            },
          },
        },
        {
          provide: GetAllBranchOfficesUsecase,
          useValue: {
            execute: () => {
              return of([
                { id: 1, name: 'branch office 1' },
                { id: 2, name: 'branch office 2' },
              ]);
            },
          },
        },
        provideRouter([
          {
            path: '',
            component: TestComponent,
            resolve: { branchOffices: getBranchOffices },
          },
          { path: '**', component: TestComponent },
        ]),
      ],
    });

    usecaseSpy = spyOn(
      TestBed.inject(GetAllBranchOfficesUsecase),
      'execute'
    ).and.callThrough();
    storeSpy = spyOn(
      TestBed.inject(BranchOfficesStore),
      'setBranchOffices'
    ).and.callThrough();

    resolver = await TestBed.runInInjectionContext(
      () =>
        getBranchOffices({} as any, {} as any) as Observable<
          IBranchOfficeUIDto[]
        >
    );
  });

  it('should call usecase and store', async () => {
    const result = await lastValueFrom(resolver);

    expect(result.length).toBe(2);
    expect(
      result.findIndex(i => i.id === 1) < result.findIndex(i => i.id === 2)
    )
      .withContext('Branch offices should be sorted by id')
      .toBeTrue();
    expect(usecaseSpy).toHaveBeenCalledTimes(1);
    expect(storeSpy).toHaveBeenCalledTimes(1);
  });
});
