import { As<PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import {
  provideRouter,
  Route,
  Router,
  RouterLink,
  RouterOutlet,
} from '@angular/router';
import {
  RouterTestingHarness,
  RouterTestingModule,
} from '@angular/router/testing';
import { RedirectionService } from '@aplazo/merchant/shared';
import { LocalRedirecter } from '@aplazo/merchant/shared-testing';
import { AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDashboardComponents } from '@aplazo/shared-ui/dashboard';
import { AplazoDetailsComponents } from '@aplazo/shared-ui/details';
import {
  AplazoDropdownComponent,
  AplazoDropdownComponents,
} from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { AplazoSidenavLinkComponent } from '@aplazo/shared-ui/sidenav';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { DialogService } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { of } from 'rxjs';
import { RouteByRoleDirective } from 'src/app/features/home/<USER>/route-by-role.directive';
import { DASH_ROUTES } from '../../../../app/config/app-route-core';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../app/config/merchant-core.environment';
import { HomeComponent } from '../../../../app/features/home/<USER>/home.component';
import { UserStoreService } from '../../../../app/features/user/src/application/services/user-store.service';
import { UserLogoutUseCase } from '../../../../app/features/user/src/application/usecases/logout.usecase';

@Component({
  standalone: true,
  template: '',
})
class TestComponent {}

const testRoutes: Route[] = [
  {
    path: '',
    component: HomeComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: DASH_ROUTES.dashboard,
      },
      {
        path: DASH_ROUTES.unavailable,
        component: TestComponent,
        data: {
          title: 'Servicio no disponible',
        },
      },
      {
        path: DASH_ROUTES.dashboard,
        component: TestComponent,
        data: {
          title: 'Panel',
        },
      },
      {
        path: DASH_ROUTES.legacyReport,
        component: TestComponent,
        data: {
          title: 'Reportes',
        },
      },
      {
        path: DASH_ROUTES.report,
        component: TestComponent,
        data: {
          title: 'Reportes de Pago',
        },
      },
      {
        path: DASH_ROUTES.refunds,
        component: TestComponent,
        data: {
          title: 'Reporte de Devoluciones',
        },
      },
      {
        path: DASH_ROUTES.clarifications,
        component: TestComponent,
        data: {
          title: 'Aclaraciones',
        },
      },
      {
        path: DASH_ROUTES.account,
        component: TestComponent,
        data: {
          title: 'Información del Comercio',
        },
      },
      {
        path: DASH_ROUTES.loansDailyStatistics,
        component: TestComponent,
        data: {
          title: 'Ventas por día',
        },
      },
      {
        path: DASH_ROUTES.balance,
        component: TestComponent,
        data: {
          title: 'Estado de Cuenta',
        },
      },
    ],
  },
];

describe('HomeComponent', () => {
  let routerHarness: RouterTestingHarness;
  let component: HomeComponent;
  let internalRedirectionSpy: jasmine.Spy;
  let openDialogSpy: jasmine.Spy;
  let usecaseSpy: jasmine.Spy;
  let finishFlagSpy: jasmine.Spy;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [
        RouterLink,
        RouterOutlet,
        AsyncPipe,
        NgIf,
        AplazoIconComponent,
        AplazoButtonComponent,
        AplazoDetailsComponents,
        AplazoDropdownComponents,
        AplazoDashboardComponents,
        AplazoSidenavLinkComponent,
        RouteByRoleDirective,
        RouterTestingModule,
      ],
      providers: [
        provideTranslocoScope('home'),
        AplazoMatchMediaService,
        AplazoIconRegistryService,
        DialogService,

        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            redirectionsLandingPage: 'https://aplazo.net',
          },
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: UserStoreService,
          useValue: {
            username$: of('username'),
            getRole: () => 'ROLE_MERCHANT',
          },
        },
        {
          provide: StatsigService,
          useValue: {
            updateUserAsync: () => {
              void 0;
            },
            getClient: () => {
              return {
                getContext: () => {
                  return { user: {} };
                },
                $on: () => {
                  return of({ user: {} });
                },
              };
            },
            checkGate: () => {
              return true;
            },
          },
        },
        {
          provide: TranslocoService,
          useValue: {
            selectTranslateObject: () =>
              of({
                title: '¿Está seguro de cerrar sesión?',
                cancelButton: 'Cancelar',
                acceptButton: 'Cerrar sesión',
              }),
          },
        },
        {
          provide: UserLogoutUseCase,
          useValue: {
            execute: () => of(void 0),
          },
        },
        provideRouter(testRoutes),
      ],
    });

    routerHarness = await RouterTestingHarness.create();
    component = await routerHarness.navigateByUrl('/', HomeComponent);

    routerHarness.detectChanges();

    internalRedirectionSpy = spyOn(
      TestBed.inject(RedirectionService),
      'internalNavigation'
    );
    openDialogSpy = spyOn(
      TestBed.inject(DialogService),
      'open'
    ).and.callThrough();
    usecaseSpy = spyOn(
      TestBed.inject(UserLogoutUseCase),
      'execute'
    ).and.callThrough();
    finishFlagSpy = spyOn(
      TestBed.inject(StatsigService),
      'updateUserAsync'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should redirect to dashboard', async () => {
    expect(TestBed.inject(Router).url).toEqual(`/${DASH_ROUTES.dashboard}`);
  });

  it('should have a title', () => {
    const title = routerHarness.routeDebugElement.query(By.css('h2'));
    const expected = testRoutes[0].children.find(
      i => i.path === DASH_ROUTES.dashboard
    ).data.title;

    expect(title.nativeElement.textContent.trim()).toEqual(expected);
  });

  it('should have a username', () => {
    const username = routerHarness.routeDebugElement.query(
      By.css('nav button > span.lowercase')
    );

    expect(username.nativeElement.textContent.trim())
      .withContext(
        'The username should be the same that the user store username$'
      )
      .toEqual('username');
  });

  it('should trigger and show dropdown main menu', fakeAsync(() => {
    const logoutSpy = spyOn(component, 'logout').and.callThrough();
    const openMenuButton = routerHarness.routeDebugElement.query(
      By.css('nav button')
    );

    openMenuButton.nativeElement.click();
    routerHarness.detectChanges();

    const mainMenu = routerHarness.routeDebugElement.query(
      By.directive(AplazoDropdownComponent)
    );

    const logoutButton = mainMenu.query(By.css('button'));

    logoutButton.nativeElement.click();

    routerHarness.detectChanges();
    tick();

    const closeDialog = document.querySelector('.ngneat-close-dialog') as any;

    closeDialog.click();

    routerHarness.detectChanges();

    expect(logoutSpy).toHaveBeenCalledTimes(1);
  }));

  it('should call internalNavigation when logo is clicked', () => {
    const logo = routerHarness.routeDebugElement.query(
      By.directive(AplazoLogoComponent)
    );

    logo.nativeElement.click();

    routerHarness.detectChanges();

    expect(internalRedirectionSpy).toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy).toHaveBeenCalledWith('/');
  });

  it('should call logout usecase when dialog confirmation is accepted', fakeAsync(() => {
    component.logout();

    routerHarness.detectChanges();

    expect(openDialogSpy).toHaveBeenCalledTimes(1);

    const dialog = document.querySelector('.ngneat-dialog-content');

    const buttons = dialog.querySelectorAll(
      '.aplazo-confirm-dialog__actions button'
    );

    (buttons.item(1) as any).click();

    routerHarness.detectChanges();
    tick();

    expect(usecaseSpy).toHaveBeenCalledTimes(1);
    expect(usecaseSpy).toHaveBeenCalledWith(`/${DASH_ROUTES.authentication}`);
    expect(finishFlagSpy).toHaveBeenCalledTimes(1);
  }));

  it('should not call logout usecase when dialog confirmation is rejected', fakeAsync(() => {
    component.logout();

    routerHarness.detectChanges();

    expect(openDialogSpy).toHaveBeenCalledTimes(1);

    const dialog = document.querySelector('.ngneat-dialog-content');

    const buttons = dialog.querySelectorAll(
      '.aplazo-confirm-dialog__actions button'
    );

    (buttons.item(0) as any).click();

    routerHarness.detectChanges();
    tick();

    expect(usecaseSpy).toHaveBeenCalledTimes(0);
    expect(finishFlagSpy).toHaveBeenCalledTimes(0);
  }));
});
