import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter, Router, UrlTree } from '@angular/router';
import { of, throwError } from 'rxjs';
import { DASH_ROUTES } from '../../../../app/config/app-route-core';
import { preventNavigationByRole } from '../../../../app/features/home/<USER>/role.guard';
import { UserStoreService } from '../../../../app/features/user/src/application/services/user-store.service';

@Component({
  standalone: true,
  selector: 'app-test',
  template: '',
})
export class TestComponent {}

const setup = (user: unknown, route: unknown) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      { provide: UserStoreService, useValue: user },
      provideRouter([
        {
          path: '',
          component: TestComponent,
          canActivate: [preventNavigationByRole],
        },
        { path: '**', component: TestComponent },
      ]),
    ],
  });

  const guard = async () =>
    await TestBed.runInInjectionContext(
      () =>
        preventNavigationByRole(route as any, {} as any) as Promise<
          boolean | UrlTree
        >
    );
  const router = TestBed.inject(Router);
  const parseUrlSpy = spyOn(router, 'parseUrl').and.callThrough();
  return { guard, router, parseUrlSpy };
};

describe('preventNavigationByRole', async () => {
  it('should allow navigation when roles by route includes role', async () => {
    const { guard } = await setup(
      { role$: of('ROLE_MERCHANT') },
      { routeConfig: { path: DASH_ROUTES.dashboard } }
    );
    const result = await guard();

    expect(result)
      .withContext('Expected guard to be true and allow navigation')
      .toBeTrue();
  });

  it('should allow navigation when roles by route includes role. Different combination', async () => {
    const { guard } = await setup(
      { role$: of('ROLE_PANEL_FINANCE') },
      { routeConfig: { path: DASH_ROUTES.refunds } }
    );
    const result = await guard();

    expect(result)
      .withContext('Expected guard to be true and allow navigation')
      .toBeTrue();
  });

  it('should redirect to report when role is ROLE_PANEL_FINANCE and route is not allowed', async () => {
    const { guard, parseUrlSpy } = await setup(
      { role$: of('ROLE_PANEL_FINANCE') },
      { routeConfig: { path: DASH_ROUTES.dashboard } }
    );
    const result = await guard();

    expect(result)
      .withContext('Expected guard to be UrlTree')
      .toBeInstanceOf(UrlTree);
    expect(parseUrlSpy)
      .withContext('Expected parseUrl to be called with report path')
      .toHaveBeenCalledWith(`/${DASH_ROUTES.rootApp}/${DASH_ROUTES.report}`);
  });

  it('should redirect to dashboard when role is not ROLE_PANEL_FINANCE and route is not allowed', async () => {
    const { guard, parseUrlSpy } = await setup(
      { role$: of('ROLE_PANEL_MARKETING') },
      { routeConfig: { path: DASH_ROUTES.refunds } }
    );
    const result = await guard();

    expect(result)
      .withContext('Expected guard to be UrlTree')
      .toBeInstanceOf(UrlTree);
    expect(parseUrlSpy)
      .withContext('Expected parseUrl to be called with dashboard path')
      .toHaveBeenCalledWith(`/${DASH_ROUTES.rootApp}/${DASH_ROUTES.dashboard}`);
  });

  it('should handle error when unexpected error occurs', async () => {
    const { guard } = await setup(
      { role$: throwError(() => 'Unexpected error') },
      { routeConfig: { path: DASH_ROUTES.refunds } }
    );

    const result = await guard();

    expect(result).withContext('Expected guard to be false').toBeFalse();
  });
});
