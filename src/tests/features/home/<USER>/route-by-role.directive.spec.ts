import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DASH_ROUTES } from '../../../../app/config/app-route-core';
import { RouteByRoleDirective } from '../../../../app/features/home/<USER>/route-by-role.directive';
import { UserStoreService } from '../../../../app/features/user/src/application/services/user-store.service';

@Component({
  selector: 'app-host-test',
  imports: [RouteByRoleDirective],
  template: `
    <div *aplazoRoutesByRole="[routes.balance]">
      <p>Test</p>
    </div>
  `,
})
export class HostTestComponent {
  routes = DASH_ROUTES;
}

const setup = (store: unknown) => {
  TestBed.configureTestingModule({
    imports: [HostTestComponent, RouteByRoleDirective],
    providers: [
      {
        provide: UserStoreService,
        useValue: store,
      },
    ],
  });

  const fixture = TestBed.createComponent(HostTestComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  return { fixture, component };
};

describe('RouteByRoleDirective', () => {
  it('should create', () => {
    const { component } = setup({ getRole: () => 'ROLE_MERCHANT' });

    expect(component).toBeTruthy();
  });

  it('should render the template', () => {
    const { fixture } = setup({ getRole: () => 'ROLE_MERCHANT' });

    const element = fixture.debugElement.query(By.css('p'));

    expect(element).toBeTruthy();
    expect(element.nativeElement.textContent).toBe('Test');
  });

  it('should not render the template', () => {
    const { fixture } = setup({ getRole: () => 'ROLE_PANEL_MARKETING' });

    const element = fixture.debugElement.query(By.css('p'));

    expect(element).toBeFalsy();
  });
});
