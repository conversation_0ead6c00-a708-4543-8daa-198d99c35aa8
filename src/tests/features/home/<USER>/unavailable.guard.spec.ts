import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter, Router, UrlTree } from '@angular/router';
import { StatsigService } from '@statsig/angular-bindings';
import { DASH_ROUTES } from 'src/app/config/app-route-core';
import { preventNavigationByUnavailableFlag } from '../../../../app/features/home/<USER>/unavailable.guard';

@Component({
  standalone: true,

  template: '',
})
export class TestComponent {}

const setup = async (flag: boolean, route: string) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      {
        provide: StatsigService,
        useValue: {
          checkGate: () => flag,
        },
      },
      provideRouter([
        {
          path: '',
          component: TestComponent,
          canActivate: [preventNavigationByUnavailableFlag],
        },
        { path: '**', component: TestComponent },
      ]),
    ],
  });

  const router = TestBed.inject(Router);
  const parseUrlSpy = spyOn(router, 'parseUrl').and.callThrough();

  const guard = TestBed.runInInjectionContext(
    () =>
      preventNavigationByUnavailableFlag(
        {
          url: [{ path: route }],
        } as any,
        {} as any
      ) as Promise<boolean | UrlTree>
  );

  return { guard, parseUrlSpy };
};

describe('preventNavigationByUnavailableFlag', () => {
  const flag = true;

  it('should allow navigation when flag is disabled', async () => {
    const { guard } = await setup(false, DASH_ROUTES.dashboard);

    const result = await guard;

    expect(result)
      .withContext('Expected guard to be true and allow navigation')
      .toBeTrue();
  });

  it('should allow navigation when route is not in unavailable routes', async () => {
    const { guard } = await setup(flag, DASH_ROUTES.account);

    const result = await guard;

    expect(result)
      .withContext('Expected guard to be true and allow navigation')
      .toBeTrue();
  });

  it('should redirect to unavailable route when flag is enabled', async () => {
    const { guard, parseUrlSpy } = await setup(flag, DASH_ROUTES.refunds);

    const result = await guard;

    expect(result)
      .withContext('Expected guard to be UrlTree')
      .toBeInstanceOf(UrlTree);
    expect(parseUrlSpy)
      .withContext('Expected parseUrl to be called with unavailable path')
      .toHaveBeenCalledWith(
        `/${DASH_ROUTES.rootApp}/${DASH_ROUTES.unavailable}`
      );
  });
});
