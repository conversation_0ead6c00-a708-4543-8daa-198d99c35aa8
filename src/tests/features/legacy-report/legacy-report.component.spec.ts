import { Async<PERSON>ipe, NgIf } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideTemporal, TemporalService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormDatepickerComponent } from '@aplazo/shared-ui/forms';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { of } from 'rxjs';
import { MerchantLoansReportLegacyUseCase } from 'src/app/features/loan/application/usecases/merchant-loans-report.legacy.usecase';
import { LegacyReportComponent } from '../../../app/features/legacy-report/legacy-report.component';
import { EventManagerService } from '../../../app/services/event-manger.service';
import { SharedCriteria } from '../../../app/services/shared-criteria.store';

describe('LegacyReportComponent', () => {
  let fixture: ComponentFixture<LegacyReportComponent>;
  let component: LegacyReportComponent;
  let temporal: TemporalService;
  let eventManager: EventManagerService;
  let usecase: jasmine.SpyObj<MerchantLoansReportLegacyUseCase>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        AsyncPipe,
        AplazoCardComponent,
        AplazoButtonComponent,
        AplazoFormDatepickerComponent,
      ],
      providers: [
        provideTranslocoScope('legacy-report'),
        {
          provide: TranslocoService,
          useValue: {
            selectTranslateObject: (key: string) => {
              const obj = {
                daterangePicker: {
                  tooltip: 'Selecciona un rango de fecha',
                  intervalError:
                    'La descarga del reporte solo permite un período máximo de  90 días.',
                },
                content: {
                  title: 'title',
                  description: 'description',
                  actionButton: 'actionButton',
                },
              };
              return of(obj[key]);
            },
          },
        },
        {
          provide: EventManagerService,
          useValue: {
            sendTrackEvent: () => {
              void 0;
            },
          },
        },
        SharedCriteria,
        provideTemporal(),
        {
          provide: MerchantLoansReportLegacyUseCase,
          useValue: jasmine.createSpyObj('MerchantLoansReportLegacyUseCase', [
            'execute',
          ]),
        },
      ],
    });

    fixture = TestBed.createComponent(LegacyReportComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    temporal = TestBed.inject(TemporalService);
    eventManager = TestBed.inject(EventManagerService);
    usecase = TestBed.inject(
      MerchantLoansReportLegacyUseCase
    ) as jasmine.SpyObj<MerchantLoansReportLegacyUseCase>;
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should start with todayFormated as temporal todayRawDayFirst', () => {
    expect(component.todayFormated).toBe(temporal.todayRawDayFirst);
  });

  it('should have settlementsImg as a specific string', () => {
    expect(component.settlementsImg).toContain(
      'https://cdn.aplazo.mx/merchant-dash-assets/settlements-payments.png'
    );
  });

  it('should call sendTrackEvent when getReport is called', () => {
    const spy = spyOn(eventManager, 'sendTrackEvent');
    usecase.execute.and.returnValue(of([]));

    component.getReport();

    expect(spy).toHaveBeenCalledWith('buttonClick', {
      buttonName: 'downloadLegacyReport',
    });
    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('should call legacyReportUseCase.execute when getReport is called', () => {
    usecase.execute.and.returnValue(of([]));

    component.getReport();

    expect(usecase.execute).toHaveBeenCalledTimes(1);
  });
});
