import { mergeCurrentAndPreviousStatistics } from 'src/app/features/loan/application/mappers/general-loans-statistics.mapper';
import { IGeneralLoanStatisticsDto } from 'src/app/features/loan/domain/dtos/general-loans-statistics.dto';

describe('mergeCurrentAndPreviousStatistics', () => {
  const currentMonthStats: IGeneralLoanStatisticsDto = {
    merchantId: 199,
    salesAmount: 9955,
    salesOrder: 58,
    salesAmountAvg: 171.63793103448276,
    customers: 5,
    latestPayment: 0,
    nextPayment: 0,
  };

  const previousMonthStats: IGeneralLoanStatisticsDto = {
    merchantId: -1,
    customers: 0,
    salesAmount: 0,
    salesOrder: 0,
    salesAmountAvg: 0,
    latestPayment: 0,
    nextPayment: 0,
  };

  it('should return the correct statistics', () => {
    const result = mergeCurrentAndPreviousStatistics(
      currentMonthStats,
      previousMonthStats
    );
    expect(result).toEqual(currentMonthStats);
  });

  it('should throw an error if the current month stats does not have a valid salesAmount', () => {
    expect(() => {
      mergeCurrentAndPreviousStatistics(
        {
          ...currentMonthStats,
          // @ts-expect-error: testing purposes
          salesAmount: 'invalid',
        },
        previousMonthStats
      );
    }).toThrowError('Invalid value to compute stats');
  });
});
