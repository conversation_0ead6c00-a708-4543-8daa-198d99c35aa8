import { DAYS } from '@aplazo/merchant/shared';
import { dailyLoanStatsByDayHoursResponseToUIDto } from 'src/app/features/loan/application/mappers/statistics-by-day-hours.mapper';
import { IDayliLoansStatisticsReponseDto } from 'src/app/features/loan/domain/dtos/dayli-loans-statistics.dto';

describe('dailyLoanStatsByDayHoursResponseToUIDto', () => {
  const mockStats: IDayliLoansStatisticsReponseDto[] = [
    {
      dayWeek: 2,
      salesAmount: 3697,
      salesOrder: 26,
      salesAmountAvg: 142.19230769230768,
      customers: 1,
      hourDay: 8,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 2500,
      salesOrder: 1,
      salesAmountAvg: 2500,
      customers: 1,
      hourDay: 10,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 976,
      salesOrder: 8,
      salesAmountAvg: 122,
      customers: 1,
      hourDay: 11,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 150,
      salesOrder: 1,
      salesAmountAvg: 150,
      customers: 1,
      hourDay: 12,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 300,
      salesOrder: 2,
      salesAmountAvg: 150,
      customers: 1,
      hourDay: 13,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 1143,
      salesOrder: 9,
      salesAmountAvg: 127,
      customers: 2,
      hourDay: 14,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 689,
      salesOrder: 6,
      salesAmountAvg: 114.83333333333333,
      customers: 2,
      hourDay: 15,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 2,
      salesAmount: 100,
      salesOrder: 1,
      salesAmountAvg: 100,
      customers: 1,
      hourDay: 16,
      latestPayment: 0,
      nextPayment: 0,
    },
  ];

  it('should transform the stats into the correct UI format', () => {
    const result = dailyLoanStatsByDayHoursResponseToUIDto(mockStats);

    // Check the structure of the result
    expect(result.graphs).toBeDefined();
    expect(result.content).toBeDefined();

    // Check graphs array
    expect(Array.isArray(result.graphs)).toBe(true);
    expect(result.graphs.length).toBe(Object.keys(DAYS).length);

    // Check content array
    expect(Array.isArray(result.content)).toBe(true);
    expect(result.content.length).toBe(24); // 24 hours

    // Check specific values for day 2 (Tuesday)
    const tuesday = result.graphs.find(day => day.weekday === 2);
    expect(tuesday).toBeDefined();

    if (tuesday) {
      // Check hour 8 data
      expect(tuesday.customers[8]).toBe(1);
      expect(tuesday.salesAmount[8]).toBe(3697);
      expect(tuesday.salesOrder[8]).toBe(26);
      expect(tuesday.salesAmountAvg[8]).toBe(142.19230769230768);

      // Check hour 10 data
      expect(tuesday.customers[10]).toBe(1);
      expect(tuesday.salesAmount[10]).toBe(2500);
      expect(tuesday.salesOrder[10]).toBe(1);
      expect(tuesday.salesAmountAvg[10]).toBe(2500);

      // Check hour 9 (should be 0 as no data provided)
      expect(tuesday.customers[9]).toBe(0);
      expect(tuesday.salesAmount[9]).toBe(0);
      expect(tuesday.salesOrder[9]).toBe(0);
      expect(tuesday.salesAmountAvg[9]).toBe(0);
    }

    // Check content for hour 8
    const hour8 = result.content.find(hour => hour.timePeriod === 8);
    expect(hour8).toBeDefined();

    if (hour8) {
      expect(hour8.customers).toBe(1);
      expect(hour8.salesAmount).toBe(3697);
      expect(hour8.salesOrder).toBe(26);
      expect(hour8.salesAmountAvg).toBe(142.19230769230768);
    }

    // Check content for hour 14
    const hour14 = result.content.find(hour => hour.timePeriod === 14);
    expect(hour14).toBeDefined();

    if (hour14) {
      expect(hour14.customers).toBe(2);
      expect(hour14.salesAmount).toBe(1143);
      expect(hour14.salesOrder).toBe(9);
      expect(hour14.salesAmountAvg).toBe(127);
    }
  });

  it('should handle empty stats array', () => {
    const result = dailyLoanStatsByDayHoursResponseToUIDto([]);

    // Check structure
    expect(result.graphs).toBeDefined();
    expect(result.content).toBeDefined();

    // Check graphs array
    expect(Array.isArray(result.graphs)).toBe(true);
    expect(result.graphs.length).toBe(Object.keys(DAYS).length);

    // Check content array
    expect(Array.isArray(result.content)).toBe(true);
    expect(result.content.length).toBe(24); // 24 hours

    // All values should be 0
    result.graphs.forEach(day => {
      expect(day.customers.every(val => val === 0)).toBe(true);
      expect(day.salesAmount.every(val => val === 0)).toBe(true);
      expect(day.salesOrder.every(val => val === 0)).toBe(true);
      expect(day.salesAmountAvg.every(val => val === 0)).toBe(true);
    });

    result.content.forEach(hour => {
      expect(hour.customers).toBe(0);
      expect(hour.salesAmount).toBe(0);
      expect(hour.salesOrder).toBe(0);
      expect(hour.salesAmountAvg).toBe(0);
    });
  });
});
