import {
  fromPartialResponseToDto,
  fromResponseToGraphDto,
} from 'src/app/features/loan/application/mappers/statistics-by-hours.mapper';
import { IDayliLoansStatisticsReponseDto } from 'src/app/features/loan/domain/dtos/dayli-loans-statistics.dto';

const mockStats: IDayliLoansStatisticsReponseDto[] = [
  {
    dayWeek: 0,
    salesAmount: 144,
    salesOrder: 1,
    salesAmountAvg: 144,
    customers: 1,
    hourDay: 1,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 200,
    salesOrder: 1,
    salesAmountAvg: 200,
    customers: 1,
    hourDay: 8,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 1000,
    salesOrder: 2,
    salesAmountAvg: 500,
    customers: 1,
    hourDay: 9,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 1200,
    salesOrder: 1,
    salesAmountAvg: 1200,
    customers: 1,
    hourDay: 10,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 2800,
    salesOrder: 4,
    salesAmountAvg: 700,
    customers: 3,
    hourDay: 11,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 423,
    salesOrder: 2,
    salesAmountAvg: 211.5,
    customers: 2,
    hourDay: 12,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 1355,
    salesOrder: 4,
    salesAmountAvg: 338.75,
    customers: 4,
    hourDay: 13,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 530,
    salesOrder: 2,
    salesAmountAvg: 265,
    customers: 2,
    hourDay: 14,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 1000,
    salesOrder: 3,
    salesAmountAvg: 333.3333333333333,
    customers: 3,
    hourDay: 15,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 825,
    salesOrder: 5,
    salesAmountAvg: 165,
    customers: 5,
    hourDay: 16,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 3898,
    salesOrder: 7,
    salesAmountAvg: 556.8571428571429,
    customers: 5,
    hourDay: 17,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 200,
    salesOrder: 2,
    salesAmountAvg: 100,
    customers: 1,
    hourDay: 18,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 100,
    salesOrder: 1,
    salesAmountAvg: 100,
    customers: 1,
    hourDay: 19,
    latestPayment: 0,
    nextPayment: 0,
  },
  {
    dayWeek: 0,
    salesAmount: 200,
    salesOrder: 2,
    salesAmountAvg: 100,
    customers: 1,
    hourDay: 20,
    latestPayment: 0,
    nextPayment: 0,
  },
];

describe('fromResponseToGraphDto', () => {
  it('should transform stats into graph format with arrays for each metric', () => {
    const result = fromResponseToGraphDto(mockStats);

    // Check structure
    expect(result.customers).toBeDefined();
    expect(result.salesAmount).toBeDefined();
    expect(result.salesOrder).toBeDefined();
    expect(result.salesAmountAvg).toBeDefined();

    // Check array lengths
    expect(result.customers.length).toBe(24);
    expect(result.salesAmount.length).toBe(24);
    expect(result.salesOrder.length).toBe(24);
    expect(result.salesAmountAvg.length).toBe(24);

    // Check specific values for hours with data
    expect(result.customers[1]).toBe(1);
    expect(result.salesAmount[1]).toBe(144);
    expect(result.salesOrder[1]).toBe(1);
    expect(result.salesAmountAvg[1]).toBe(144);

    expect(result.customers[11]).toBe(3);
    expect(result.salesAmount[11]).toBe(2800);
    expect(result.salesOrder[11]).toBe(4);
    expect(result.salesAmountAvg[11]).toBe(700);

    expect(result.customers[17]).toBe(5);
    expect(result.salesAmount[17]).toBe(3898);
    expect(result.salesOrder[17]).toBe(7);
    expect(result.salesAmountAvg[17]).toBe(556.8571428571429);

    // Check hours without data (should be 0)
    expect(result.customers[0]).toBe(0);
    expect(result.salesAmount[0]).toBe(0);
    expect(result.salesOrder[0]).toBe(0);
    expect(result.salesAmountAvg[0]).toBe(0);

    expect(result.customers[23]).toBe(0);
    expect(result.salesAmount[23]).toBe(0);
    expect(result.salesOrder[23]).toBe(0);
    expect(result.salesAmountAvg[23]).toBe(0);
  });

  it('should handle empty stats array', () => {
    const result = fromResponseToGraphDto([]);

    // Check structure
    expect(result.customers).toBeDefined();
    expect(result.salesAmount).toBeDefined();
    expect(result.salesOrder).toBeDefined();
    expect(result.salesAmountAvg).toBeDefined();

    // Check array lengths
    expect(result.customers.length).toBe(24);
    expect(result.salesAmount.length).toBe(24);
    expect(result.salesOrder.length).toBe(24);
    expect(result.salesAmountAvg.length).toBe(24);

    // All values should be 0
    expect(result.customers.every(val => val === 0)).toBe(true);
    expect(result.salesAmount.every(val => val === 0)).toBe(true);
    expect(result.salesOrder.every(val => val === 0)).toBe(true);
    expect(result.salesAmountAvg.every(val => val === 0)).toBe(true);
  });

  it('should handle null or undefined stats', () => {
    const resultNull = fromResponseToGraphDto(null);
    const resultUndefined = fromResponseToGraphDto(undefined);

    // Check null case
    expect(resultNull.customers.every(val => val === 0)).toBe(true);
    expect(resultNull.salesAmount.every(val => val === 0)).toBe(true);
    expect(resultNull.salesOrder.every(val => val === 0)).toBe(true);
    expect(resultNull.salesAmountAvg.every(val => val === 0)).toBe(true);

    // Check undefined case
    expect(resultUndefined.customers.every(val => val === 0)).toBe(true);
    expect(resultUndefined.salesAmount.every(val => val === 0)).toBe(true);
    expect(resultUndefined.salesOrder.every(val => val === 0)).toBe(true);
    expect(resultUndefined.salesAmountAvg.every(val => val === 0)).toBe(true);
  });
});

describe('fromPartialResponseToDto', () => {
  it('should transform stats into an array of hourly data objects', () => {
    const result = fromPartialResponseToDto(mockStats);

    // Check structure
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(24);

    // Check specific hours with data
    const hour1 = result.find(hour => hour.timePeriod === 1);
    expect(hour1).toBeDefined();
    if (hour1) {
      expect(hour1.customers).toBe(1);
      expect(hour1.salesAmount).toBe(144);
      expect(hour1.salesOrder).toBe(1);
      expect(hour1.salesAmountAvg).toBe(144);
    }

    const hour13 = result.find(hour => hour.timePeriod === 13);
    expect(hour13).toBeDefined();
    if (hour13) {
      expect(hour13.customers).toBe(4);
      expect(hour13.salesAmount).toBe(1355);
      expect(hour13.salesOrder).toBe(4);
      expect(hour13.salesAmountAvg).toBe(338.75);
    }

    const hour17 = result.find(hour => hour.timePeriod === 17);
    expect(hour17).toBeDefined();
    if (hour17) {
      expect(hour17.customers).toBe(5);
      expect(hour17.salesAmount).toBe(3898);
      expect(hour17.salesOrder).toBe(7);
      expect(hour17.salesAmountAvg).toBe(556.8571428571429);
    }

    // Check hours without data (should be initialized with 0)
    const hour0 = result.find(hour => hour.timePeriod === 0);
    expect(hour0).toBeDefined();
    if (hour0) {
      expect(hour0.customers).toBe(0);
      expect(hour0.salesAmount).toBe(0);
      expect(hour0.salesOrder).toBe(0);
      expect(hour0.salesAmountAvg).toBe(0);
    }

    const hour23 = result.find(hour => hour.timePeriod === 23);
    expect(hour23).toBeDefined();
    if (hour23) {
      expect(hour23.customers).toBe(0);
      expect(hour23.salesAmount).toBe(0);
      expect(hour23.salesOrder).toBe(0);
      expect(hour23.salesAmountAvg).toBe(0);
    }
  });

  it('should handle empty stats array', () => {
    const result = fromPartialResponseToDto([]);

    // Check structure
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(24);

    // All hours should have 0 values
    result.forEach(hour => {
      expect(hour.customers).toBe(0);
      expect(hour.salesAmount).toBe(0);
      expect(hour.salesOrder).toBe(0);
      expect(hour.salesAmountAvg).toBe(0);
    });

    // Check time periods are set correctly
    for (let i = 0; i < 24; i++) {
      expect(result[i].timePeriod).toBe(i);
    }
  });

  it('should handle null or undefined stats', () => {
    const resultNull = fromPartialResponseToDto(null);
    const resultUndefined = fromPartialResponseToDto(undefined);

    // Check null case
    expect(resultNull.length).toBe(24);
    resultNull.forEach(hour => {
      expect(hour.customers).toBe(0);
      expect(hour.salesAmount).toBe(0);
      expect(hour.salesOrder).toBe(0);
      expect(hour.salesAmountAvg).toBe(0);
    });

    // Check undefined case
    expect(resultUndefined.length).toBe(24);
    resultUndefined.forEach(hour => {
      expect(hour.customers).toBe(0);
      expect(hour.salesAmount).toBe(0);
      expect(hour.salesOrder).toBe(0);
      expect(hour.salesAmountAvg).toBe(0);
    });
  });
});
