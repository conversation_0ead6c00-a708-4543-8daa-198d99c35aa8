import {
  fromWeekdayPartialResponseToDto,
  fromWeekdayResponseToGraphDto,
} from 'src/app/features/loan/application/mappers/statistics-by-weekday.mapper';
import { IDayliLoansStatisticsReponseDto } from 'src/app/features/loan/domain/dtos/dayli-loans-statistics.dto';

describe('Statistics by Weekday Mapper', () => {
  const mockStats: IDayliLoansStatisticsReponseDto[] = [
    {
      dayWeek: 1,
      salesAmount: 463,
      salesOrder: 3,
      salesAmountAvg: 154.33333333333334,
      customers: 1,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 3,
      salesAmount: 1945,
      salesOrder: 11,
      salesAmountAvg: 176.8181818181818,
      customers: 7,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 4,
      salesAmount: 4109,
      salesOrder: 10,
      salesAmountAvg: 410.9,
      customers: 9,
      latestPayment: 0,
      nextPayment: 0,
    },
    {
      dayWeek: 5,
      salesAmount: 7358,
      salesOrder: 13,
      salesAmountAvg: 566,
      customers: 12,
      latestPayment: 0,
      nextPayment: 0,
    },
  ];

  describe('fromWeekdayResponseToGraphDto', () => {
    it('should transform stats into the correct graph format', () => {
      const result = fromWeekdayResponseToGraphDto(mockStats);

      // Check structure
      expect(result).toBeDefined();
      expect(result.customers).toBeDefined();
      expect(result.salesAmount).toBeDefined();
      expect(result.salesOrder).toBeDefined();
      expect(result.salesAmountAvg).toBeDefined();

      // Check array lengths
      expect(result.customers.length).toBe(7);
      expect(result.salesAmount.length).toBe(7);
      expect(result.salesOrder.length).toBe(7);
      expect(result.salesAmountAvg.length).toBe(7);

      // Check specific values for days with data
      // Day 1 (Monday)
      expect(result.customers[1]).toBe(1);
      expect(result.salesAmount[1]).toBe(463);
      expect(result.salesOrder[1]).toBe(3);
      expect(result.salesAmountAvg[1]).toBe(154.33333333333334);

      // Day 3 (Wednesday)
      expect(result.customers[3]).toBe(7);
      expect(result.salesAmount[3]).toBe(1945);
      expect(result.salesOrder[3]).toBe(11);
      expect(result.salesAmountAvg[3]).toBe(176.8181818181818);

      // Day 4 (Thursday)
      expect(result.customers[4]).toBe(9);
      expect(result.salesAmount[4]).toBe(4109);
      expect(result.salesOrder[4]).toBe(10);
      expect(result.salesAmountAvg[4]).toBe(410.9);

      // Day 5 (Friday)
      expect(result.customers[5]).toBe(12);
      expect(result.salesAmount[5]).toBe(7358);
      expect(result.salesOrder[5]).toBe(13);
      expect(result.salesAmountAvg[5]).toBe(566);

      // Check days without data (should be 0)
      expect(result.customers[0]).toBe(0);
      expect(result.salesAmount[0]).toBe(0);
      expect(result.salesOrder[0]).toBe(0);
      expect(result.salesAmountAvg[0]).toBe(0);

      expect(result.customers[2]).toBe(0);
      expect(result.salesAmount[2]).toBe(0);
      expect(result.salesOrder[2]).toBe(0);
      expect(result.salesAmountAvg[2]).toBe(0);

      expect(result.customers[6]).toBe(0);
      expect(result.salesAmount[6]).toBe(0);
      expect(result.salesOrder[6]).toBe(0);
      expect(result.salesAmountAvg[6]).toBe(0);
    });

    it('should handle empty stats array', () => {
      const result = fromWeekdayResponseToGraphDto([]);

      // Check structure
      expect(result).toBeDefined();
      expect(result.customers).toBeDefined();
      expect(result.salesAmount).toBeDefined();
      expect(result.salesOrder).toBeDefined();
      expect(result.salesAmountAvg).toBeDefined();

      // Check array lengths
      expect(result.customers.length).toBe(7);
      expect(result.salesAmount.length).toBe(7);
      expect(result.salesOrder.length).toBe(7);
      expect(result.salesAmountAvg.length).toBe(7);

      // All values should be 0
      expect(result.customers.every(val => val === 0)).toBe(true);
      expect(result.salesAmount.every(val => val === 0)).toBe(true);
      expect(result.salesOrder.every(val => val === 0)).toBe(true);
      expect(result.salesAmountAvg.every(val => val === 0)).toBe(true);
    });

    it('should handle null or undefined stats', () => {
      const resultNull = fromWeekdayResponseToGraphDto(null as any);
      const resultUndefined = fromWeekdayResponseToGraphDto(undefined as any);

      // Check both results have all zeros
      expect(resultNull.customers.every(val => val === 0)).toBe(true);
      expect(resultNull.salesAmount.every(val => val === 0)).toBe(true);
      expect(resultNull.salesOrder.every(val => val === 0)).toBe(true);
      expect(resultNull.salesAmountAvg.every(val => val === 0)).toBe(true);

      expect(resultUndefined.customers.every(val => val === 0)).toBe(true);
      expect(resultUndefined.salesAmount.every(val => val === 0)).toBe(true);
      expect(resultUndefined.salesOrder.every(val => val === 0)).toBe(true);
      expect(resultUndefined.salesAmountAvg.every(val => val === 0)).toBe(true);
    });
  });

  describe('fromWeekdayPartialResponseToDto', () => {
    it('should transform stats into the correct content format', () => {
      const result = fromWeekdayPartialResponseToDto(mockStats);

      // Check structure
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(7);

      // Check specific values for days with data
      // Day 1 (Monday)
      const monday = result.find(day => day.timePeriod === 1);
      expect(monday).toBeDefined();
      expect(monday?.customers).toBe(1);
      expect(monday?.salesAmount).toBe(463);
      expect(monday?.salesOrder).toBe(3);
      expect(monday?.salesAmountAvg).toBe(154.33333333333334);

      // Day 3 (Wednesday)
      const wednesday = result.find(day => day.timePeriod === 3);
      expect(wednesday).toBeDefined();
      expect(wednesday?.customers).toBe(7);
      expect(wednesday?.salesAmount).toBe(1945);
      expect(wednesday?.salesOrder).toBe(11);
      expect(wednesday?.salesAmountAvg).toBe(176.8181818181818);

      // Day 4 (Thursday)
      const thursday = result.find(day => day.timePeriod === 4);
      expect(thursday).toBeDefined();
      expect(thursday?.customers).toBe(9);
      expect(thursday?.salesAmount).toBe(4109);
      expect(thursday?.salesOrder).toBe(10);
      expect(thursday?.salesAmountAvg).toBe(410.9);

      // Day 5 (Friday)
      const friday = result.find(day => day.timePeriod === 5);
      expect(friday).toBeDefined();
      expect(friday?.customers).toBe(12);
      expect(friday?.salesAmount).toBe(7358);
      expect(friday?.salesOrder).toBe(13);
      expect(friday?.salesAmountAvg).toBe(566);

      // Check days without data (should be 0)
      const sunday = result.find(day => day.timePeriod === 0);
      expect(sunday).toBeDefined();
      expect(sunday?.customers).toBe(0);
      expect(sunday?.salesAmount).toBe(0);
      expect(sunday?.salesOrder).toBe(0);
      expect(sunday?.salesAmountAvg).toBe(0);

      const tuesday = result.find(day => day.timePeriod === 2);
      expect(tuesday).toBeDefined();
      expect(tuesday?.customers).toBe(0);
      expect(tuesday?.salesAmount).toBe(0);
      expect(tuesday?.salesOrder).toBe(0);
      expect(tuesday?.salesAmountAvg).toBe(0);

      const saturday = result.find(day => day.timePeriod === 6);
      expect(saturday).toBeDefined();
      expect(saturday?.customers).toBe(0);
      expect(saturday?.salesAmount).toBe(0);
      expect(saturday?.salesOrder).toBe(0);
      expect(saturday?.salesAmountAvg).toBe(0);
    });

    it('should handle empty stats array', () => {
      const result = fromWeekdayPartialResponseToDto([]);

      // Check structure
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(7);

      // All days should have zero values
      result.forEach(day => {
        expect(day.customers).toBe(0);
        expect(day.salesAmount).toBe(0);
        expect(day.salesOrder).toBe(0);
        expect(day.salesAmountAvg).toBe(0);
      });
    });

    it('should handle null or undefined stats', () => {
      const resultNull = fromWeekdayPartialResponseToDto(null as any);
      const resultUndefined = fromWeekdayPartialResponseToDto(undefined as any);

      // Check both results have all zeros
      resultNull.forEach(day => {
        expect(day.customers).toBe(0);
        expect(day.salesAmount).toBe(0);
        expect(day.salesOrder).toBe(0);
        expect(day.salesAmountAvg).toBe(0);
      });

      resultUndefined.forEach(day => {
        expect(day.customers).toBe(0);
        expect(day.salesAmount).toBe(0);
        expect(day.salesOrder).toBe(0);
        expect(day.salesAmountAvg).toBe(0);
      });
    });
  });
});
