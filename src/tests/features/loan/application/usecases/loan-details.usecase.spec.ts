import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { LoanDetailsUseCase } from '../../../../../app/features/loan/application/usecases/loan-details.usecase';
import { ILoanDetailsRequestDto } from '../../../../../app/features/loan/domain/dtos/loan-details-request.dto';
import { BriefCustomer } from '../../../../../app/features/loan/domain/entities/brief-customer';
import { BriefProduct } from '../../../../../app/features/loan/domain/entities/brief-product';
import { BriefCustomerRepository } from '../../../../../app/features/loan/domain/repositories/brief-customer.repository';
import { BriefProductRepository } from '../../../../../app/features/loan/domain/repositories/brief-product.repository';

describe('LoanDetailsUseCase', () => {
  let usecase: LoanDetailsUseCase;
  let customerRepository: jasmine.SpyObj<
    BriefCustomerRepository<string, Observable<BriefCustomer>>
  >;
  let productRepository: jasmine.SpyObj<
    BriefProductRepository<string, Observable<BriefProduct[]>>
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;
  let consoleErrorSpy: jasmine.Spy;

  const mockRequest: ILoanDetailsRequestDto = {
    customerUrl: 'test-customer-url',
    productUrl: 'test-product-url',
  };

  const mockCustomer: BriefCustomer = {
    email: '<EMAIL>',
    name: 'Test Customer',
    phoneNumber: '**********',
  };

  const mockProducts: BriefProduct[] = [
    {
      id: '1',
      description: 'Product 1 desc',
      title: 'Product 1',
      count: 1,
      price: 100,
    },
    {
      id: '2',
      description: 'Product 2 desc',
      title: 'Product 2',
      count: 2,
      price: 50,
    },
  ];

  const emptyCustomer: BriefCustomer = {
    email: '',
    name: '',
    phoneNumber: '',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: BriefCustomerRepository,
          useValue: jasmine.createSpyObj('BriefCustomerRepository', [
            'getInfo',
          ]),
        },
        {
          provide: BriefProductRepository,
          useValue: jasmine.createSpyObj('BriefProductRepository', ['getInfo']),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        LoanDetailsUseCase,
      ],
    });

    usecase = TestBed.inject(LoanDetailsUseCase);
    customerRepository = TestBed.inject(
      BriefCustomerRepository
    ) as jasmine.SpyObj<
      BriefCustomerRepository<string, Observable<BriefCustomer>>
    >;
    productRepository = TestBed.inject(
      BriefProductRepository
    ) as jasmine.SpyObj<
      BriefProductRepository<string, Observable<BriefProduct[]>>
    >;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    );
    consoleErrorSpy = spyOn(console, 'error');
  });

  it('should create an instance of LoanDetailsUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(LoanDetailsUseCase);
  });

  it('should execute successfully and return customer and product info', async () => {
    customerRepository.getInfo.and.returnValue(of(mockCustomer));
    productRepository.getInfo.and.returnValue(of(mockProducts));

    const result = await lastValueFrom(usecase.execute(mockRequest));

    expect(result).toEqual({
      customer: mockCustomer,
      products: mockProducts,
    });
    expect(customerRepository.getInfo).toHaveBeenCalledOnceWith(
      mockRequest.customerUrl
    );
    expect(productRepository.getInfo).toHaveBeenCalledOnceWith(
      mockRequest.productUrl
    );
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  describe('when customerUrl is not provided', () => {
    it('should only fetch product info and return it with an empty customer', async () => {
      const requestWithoutCustomerUrl = { ...mockRequest, customerUrl: null };
      productRepository.getInfo.and.returnValue(of(mockProducts));

      const result = await lastValueFrom(
        usecase.execute(requestWithoutCustomerUrl)
      );

      expect(result).toEqual({
        customer: emptyCustomer,
        products: mockProducts,
      });
      expect(productRepository.getInfo).toHaveBeenCalledOnceWith(
        mockRequest.productUrl
      );
      expect(customerRepository.getInfo).not.toHaveBeenCalled();
      expect(loaderShowSpy).toHaveBeenCalledTimes(1);
      expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    });
  });

  it('should handle customer repository error and return default customer', async () => {
    customerRepository.getInfo.and.returnValue(
      throwError(() => new Error('customer error'))
    );
    productRepository.getInfo.and.returnValue(of(mockProducts));

    const result = await lastValueFrom(usecase.execute(mockRequest));

    expect(result.customer).toEqual(emptyCustomer);
    expect(result.products).toEqual(mockProducts);
    expect(customerRepository.getInfo).toHaveBeenCalledTimes(1);
    expect(productRepository.getInfo).toHaveBeenCalledTimes(1);
    expect(consoleErrorSpy).toHaveBeenCalled();
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle product repository error and return empty products', async () => {
    customerRepository.getInfo.and.returnValue(of(mockCustomer));
    productRepository.getInfo.and.returnValue(
      throwError(() => new Error('product error'))
    );

    const result = await lastValueFrom(usecase.execute(mockRequest));

    expect(result.customer).toEqual(mockCustomer);
    expect(result.products).toEqual([]);
    expect(customerRepository.getInfo).toHaveBeenCalledTimes(1);
    expect(productRepository.getInfo).toHaveBeenCalledTimes(1);
    expect(consoleErrorSpy).toHaveBeenCalled();
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle product repository error when customerUrl is null', async () => {
    const requestWithoutCustomerUrl = { ...mockRequest, customerUrl: null };
    productRepository.getInfo.and.returnValue(
      throwError(() => new Error('product error'))
    );

    const result = await lastValueFrom(
      usecase.execute(requestWithoutCustomerUrl)
    );

    expect(result.customer).toEqual(emptyCustomer);
    expect(result.products).toEqual([]);
    expect(productRepository.getInfo).toHaveBeenCalledTimes(1);
    expect(customerRepository.getInfo).not.toHaveBeenCalled();
    expect(consoleErrorSpy).toHaveBeenCalled();
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });
});
