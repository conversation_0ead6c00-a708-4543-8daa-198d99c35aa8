import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { MerchantLoansBySearchUseCase } from '../../../../../app/features/loan/application/usecases/loans-by-search.usecase';
import { MerchantLoan } from '../../../../../app/features/loan/domain/entities/merchant-loan';
import { MerchantSearchLoansRepository } from '../../../../../app/features/loan/domain/repositories/merchant-search-loans.repository';
import { MerchantShowSensitiveDataRepository } from '../../../../../app/features/loan/domain/repositories/merchant-show-sensitive-data.repository';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../../../app/features/shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../../../app/features/shared/shared-criteria.mapper';

describe('MerchantLoansBySearchUseCase', () => {
  let usecase: MerchantLoansBySearchUseCase;
  let searchLoansRepository: jasmine.SpyObj<
    MerchantSearchLoansRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<MerchantLoan[]>
    >
  >;
  let sensitiveDataRepository: jasmine.SpyObj<
    MerchantShowSensitiveDataRepository<Observable<boolean>>
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;
  let sharedCriteriaMapperSpy: jasmine.Spy;

  const mockLoans: MerchantLoan[] = [
    {
      id: 1,
      fees: 10,
      status: 'paid',
      total: 110,
      cartId: 'cart-1',
      shopId: 'shop-1',
      creationDate: '2022-01-01',
      customerUrl: 'https://example.com/customer/1',
      branch: 'branch-1',
      productUrl: 'https://example.com/product/1',
    },
    {
      id: 2,
      fees: 20,
      status: 'pending',
      total: 220,
      cartId: 'cart-2',
      shopId: 'shop-2',
      creationDate: '2022-01-02',
      customerUrl: 'https://example.com/customer/2',
      branch: 'branch-2',
      productUrl: 'https://example.com/product/2',
    },
  ];

  const mockCriteria: SharedCriteriaWithBranchOfficesUIDto = {
    element: 'test',
    branchOffices: [1],
    status: 'approved',
    dateRange: { startDate: '01/01/2023', endDate: '31/01/2023' },
    pageNum: 1,
    pageSize: 10,
  };

  const mockRepoCriteria: SharedCriteriaWithBranchOfficesRepositoryDto = {
    search: 'test',
    branchOffices: [1],
    status: 'approved',
    dateRange: { startDate: '01/01/2023', endDate: '31/01/2023' },
    pageNum: 1,
    pageSize: 10,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantSearchLoansRepository,
          useValue: jasmine.createSpyObj('MerchantSearchLoansRepository', [
            'searchByIdPhoneOrEmail',
          ]),
        },
        {
          provide: MerchantShowSensitiveDataRepository,
          useValue: jasmine.createSpyObj(
            'MerchantShowSensitiveDataRepository',
            ['getFlag']
          ),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        MerchantLoansBySearchUseCase,
      ],
    });

    usecase = TestBed.inject(MerchantLoansBySearchUseCase);
    searchLoansRepository = TestBed.inject(
      MerchantSearchLoansRepository
    ) as jasmine.SpyObj<
      MerchantSearchLoansRepository<
        SharedCriteriaWithBranchOfficesRepositoryDto,
        Observable<MerchantLoan[]>
      >
    >;
    sensitiveDataRepository = TestBed.inject(
      MerchantShowSensitiveDataRepository
    ) as jasmine.SpyObj<
      MerchantShowSensitiveDataRepository<Observable<boolean>>
    >;
    loader = TestBed.inject(LoaderService);

    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();

    sharedCriteriaMapperSpy = spyOn(
      SharedCriteriaMapper,
      'fromUIToRepository'
    ).and.returnValue(mockRepoCriteria);
  });

  afterEach(() => {
    // Restore the original method after each test
    sharedCriteriaMapperSpy.and.callThrough();
  });

  it('should create an instance of MerchantLoansBySearchUseCase', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return loans with customerUrl when sensitive data is allowed', async () => {
    searchLoansRepository.searchByIdPhoneOrEmail.and.returnValue(of(mockLoans));
    sensitiveDataRepository.getFlag.and.returnValue(of(true));

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(mockLoans);
    expect(searchLoansRepository.searchByIdPhoneOrEmail).toHaveBeenCalledWith(
      mockRepoCriteria
    );
    expect(sensitiveDataRepository.getFlag).toHaveBeenCalled();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should return loans without customerUrl when sensitive data is not allowed', async () => {
    searchLoansRepository.searchByIdPhoneOrEmail.and.returnValue(of(mockLoans));
    sensitiveDataRepository.getFlag.and.returnValue(of(false));

    const expectedLoans = mockLoans.map(loan => ({
      ...loan,
      customerUrl: '',
    }));

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(expectedLoans);
    expect(searchLoansRepository.searchByIdPhoneOrEmail).toHaveBeenCalledWith(
      mockRepoCriteria
    );
    expect(sensitiveDataRepository.getFlag).toHaveBeenCalled();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle error from search repository', async () => {
    const error = new Error('Search error');
    searchLoansRepository.searchByIdPhoneOrEmail.and.returnValue(
      throwError(() => error)
    );
    sensitiveDataRepository.getFlag.and.returnValue(of(true));

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(error);

    expect(usecaseErrorHandlerSpy).toHaveBeenCalledWith(error);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error from sensitive data repository', async () => {
    const error = new Error('Sensitive data error');
    searchLoansRepository.searchByIdPhoneOrEmail.and.returnValue(of(mockLoans));
    sensitiveDataRepository.getFlag.and.returnValue(throwError(() => error));

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(error);

    expect(usecaseErrorHandlerSpy).toHaveBeenCalledWith(error);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error from criteria mapper', async () => {
    const error = new Error('Mapper error');
    sharedCriteriaMapperSpy.and.throwError(error);

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(error);

    expect(usecaseErrorHandlerSpy).toHaveBeenCalledWith(
      error,
      jasmine.any(Observable)
    );
    expect(searchLoansRepository.searchByIdPhoneOrEmail).not.toHaveBeenCalled();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });
});
