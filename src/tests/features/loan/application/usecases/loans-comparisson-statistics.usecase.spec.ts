import { TestBed } from '@angular/core/testing';
import {
  DateCalculator,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { mergeCurrentAndPreviousStatistics } from '../../../../../app/features/loan/application/mappers/general-loans-statistics.mapper';
import {
  emptyResponse,
  MerchantLoansComparisonStatisticsUseCase,
} from '../../../../../app/features/loan/application/usecases/loans-comparison-statistics.usecase';
import { IGeneralLoanStatisticsDto } from '../../../../../app/features/loan/domain/dtos/general-loans-statistics.dto';
import { IMerchantLoansComparisonStatisticsDto } from '../../../../../app/features/loan/domain/dtos/merchant-loans-comparison-statistics.dto';
import { MerchantLoansStatisticsRepository } from '../../../../../app/features/loan/domain/repositories/merchant-loans-statistics.repository';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../../../app/features/shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../../../app/features/shared/shared-criteria.mapper';

describe('MerchantLoansComparisonStatisticsUseCase', () => {
  let usecase: MerchantLoansComparisonStatisticsUseCase;
  let statsRepository: jasmine.SpyObj<
    MerchantLoansStatisticsRepository<
      unknown,
      Observable<IGeneralLoanStatisticsDto>
    >
  >;
  let dateCalculator: jasmine.SpyObj<DateCalculator>;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;

  const mockCriteria: SharedCriteriaWithBranchOfficesUIDto = {
    status: 'approved',
    dateRange: {
      startDate: '01/01/2024',
      endDate: '31/01/2024',
    },
    branchOffices: [1, 2],
    pageNum: 1,
    pageSize: 10,
    element: '',
  };

  const currentMonthDateRange = {
    startDate: '01/01/2024',
    endDate: '31/01/2024',
  } as const;
  const previousMonthDateRange = {
    startDate: '01/01/2024',
    endDate: '31/01/2024',
  } as const;

  const mockCurrentMonthStats: IGeneralLoanStatisticsDto = {
    merchantId: 1,
    customers: 10,
    salesAmount: 1000,
    salesOrder: 5,
    salesAmountAvg: 200,
    latestPayment: **********,
    nextPayment: **********,
  };

  const mockPreviousMonthStats: IGeneralLoanStatisticsDto = {
    merchantId: 1,
    customers: 8,
    salesAmount: 800,
    salesOrder: 4,
    salesAmountAvg: 200,
    latestPayment: **********,
    nextPayment: **********,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantLoansStatisticsRepository,
          useValue: jasmine.createSpyObj('MerchantLoansStatisticsRepository', [
            'getStatisticsByDateRange',
          ]),
        },
        {
          provide: DateCalculator,
          useValue: jasmine.createSpyObj('DateCalculator', [
            'getCurrentMonthDateRangeUntilCurrentDay',
            'getPreviousMonthDateRangeSamePeriodCurrenMonth',
          ]),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        MerchantLoansComparisonStatisticsUseCase,
      ],
    });

    usecase = TestBed.inject(MerchantLoansComparisonStatisticsUseCase);
    statsRepository = TestBed.inject(
      MerchantLoansStatisticsRepository
    ) as jasmine.SpyObj<
      MerchantLoansStatisticsRepository<
        unknown,
        Observable<IGeneralLoanStatisticsDto>
      >
    >;
    dateCalculator = TestBed.inject(
      DateCalculator
    ) as jasmine.SpyObj<DateCalculator>;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callFake((_err, res) => res);

    dateCalculator.getCurrentMonthDateRangeUntilCurrentDay.and.returnValue(
      currentMonthDateRange
    );
    dateCalculator.getPreviousMonthDateRangeSamePeriodCurrenMonth.and.returnValue(
      previousMonthDateRange
    );
  });

  it('should create an instance of MerchantLoansComparisonStatisticsUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(MerchantLoansComparisonStatisticsUseCase);
  });

  it('should execute successfully and return comparison statistics', async () => {
    statsRepository.getStatisticsByDateRange.and.returnValues(
      of(mockCurrentMonthStats),
      of(mockPreviousMonthStats)
    );

    const expectedResult: IMerchantLoansComparisonStatisticsDto = {
      comparisonStats: mergeCurrentAndPreviousStatistics(
        mockCurrentMonthStats,
        mockPreviousMonthStats
      ),
      currentMonth: mockCurrentMonthStats,
      previousMonth: mockPreviousMonthStats,
    };

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(expectedResult);
    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(2);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle one of the stats calls returning null', async () => {
    statsRepository.getStatisticsByDateRange.and.returnValues(
      of(mockCurrentMonthStats),
      of(null)
    );

    const expectedResult: IMerchantLoansComparisonStatisticsDto = {
      comparisonStats: mergeCurrentAndPreviousStatistics(
        mockCurrentMonthStats,
        emptyResponse
      ),
      currentMonth: mockCurrentMonthStats,
      previousMonth: emptyResponse,
    };

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(expectedResult);
    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(2);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle both stats calls returning null', async () => {
    statsRepository.getStatisticsByDateRange.and.returnValues(
      of(null),
      of(null)
    );

    const expectedResult: IMerchantLoansComparisonStatisticsDto = {
      comparisonStats: mergeCurrentAndPreviousStatistics(
        emptyResponse,
        emptyResponse
      ),
      currentMonth: emptyResponse,
      previousMonth: emptyResponse,
    };

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(expectedResult);
    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(2);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle error from stats repository', async () => {
    const testError = new Error('test error');
    statsRepository.getStatisticsByDateRange.and.returnValue(
      throwError(() => testError)
    );

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(testError);

    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(2);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledWith(
      testError,
      jasmine.any(Observable)
    );
  });

  it('should throw an error if SharedCriteriaMapper throws an error', async () => {
    const error = new Error('Mapping error');
    spyOn(SharedCriteriaMapper, 'fromUIToRepository').and.throwError(error);

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(error);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledWith(
      error,
      jasmine.any(Observable)
    );
    expect(statsRepository.getStatisticsByDateRange).not.toHaveBeenCalled();
  });
});
