import { TestBed } from '@angular/core/testing';
import {
  B2BDateRange,
  LoaderService,
  Pageable,
  Sort,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { MerchantLoansListUseCase } from '../../../../../app/features/loan/application/usecases/loans-list.usecase';
import { MerchantLoansResponseDto } from '../../../../../app/features/loan/domain/dtos/merchant-loans-response.dto';
import { MerchantLoansRepository } from '../../../../../app/features/loan/domain/repositories/merchant-loans.repository';
import { MerchantShowSensitiveDataRepository } from '../../../../../app/features/loan/domain/repositories/merchant-show-sensitive-data.repository';
import {
  SharedCriteriaWithBranchOfficesRepositoryDto,
  SharedCriteriaWithBranchOfficesUIDto,
} from '../../../../../app/features/shared/shared-criteria';
import { SharedCriteriaMapper } from '../../../../../app/features/shared/shared-criteria.mapper';

describe('MerchantLoansListUseCase', () => {
  let usecase: MerchantLoansListUseCase;
  let loansRepository: jasmine.SpyObj<
    MerchantLoansRepository<
      SharedCriteriaWithBranchOfficesRepositoryDto,
      Observable<MerchantLoansResponseDto>
    >
  >;
  let sensitiveDataRepository: jasmine.SpyObj<
    MerchantShowSensitiveDataRepository<Observable<boolean>>
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;

  const mockDateRange: B2BDateRange = {
    startDate: '01/01/2023',
    endDate: '31/01/2023',
  };

  const mockPageable: Pageable = {
    sort: {
      sorted: false,
      unsorted: true,
      empty: true,
    },
    offset: 0,
    pageNumber: 0,
    pageSize: 10,
    paged: true,
    unpaged: false,
  };

  const mockSort: Sort = {
    sorted: false,
    unsorted: true,
    empty: true,
  };

  const mockLoansResponse: MerchantLoansResponseDto = {
    content: [
      {
        id: 1,
        fees: 10,
        status: 'paid',
        total: 1000,
        cartId: 'cart-1',
        shopId: 'shop-1',
        creationDate: new Date().toISOString(),
        customerUrl: '/customer/1',
        branch: 'Main',
        productUrl: '/product/1',
      },
    ],
    pageable: mockPageable,
    totalElements: 1,
    totalPages: 1,
    last: true,
    first: true,
    numberOfElements: 1,
    sort: mockSort,
    size: 10,
    number: 0,
    empty: false,
  };

  const mockCriteria: SharedCriteriaWithBranchOfficesUIDto = {
    status: 'all',
    dateRange: mockDateRange,
    branchOffices: [1, 2],
    pageNum: 0,
    pageSize: 10,
    element: 'test',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantLoansRepository,
          useValue: jasmine.createSpyObj('MerchantLoansRepository', [
            'getLoans',
          ]),
        },
        {
          provide: MerchantShowSensitiveDataRepository,
          useValue: jasmine.createSpyObj(
            'MerchantShowSensitiveDataRepository',
            ['getFlag']
          ),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        MerchantLoansListUseCase,
      ],
    });

    usecase = TestBed.inject(MerchantLoansListUseCase);
    loansRepository = TestBed.inject(MerchantLoansRepository) as jasmine.SpyObj<
      MerchantLoansRepository<
        SharedCriteriaWithBranchOfficesRepositoryDto,
        Observable<MerchantLoansResponseDto>
      >
    >;
    sensitiveDataRepository = TestBed.inject(
      MerchantShowSensitiveDataRepository
    ) as jasmine.SpyObj<
      MerchantShowSensitiveDataRepository<Observable<boolean>>
    >;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should create an instance of MerchantLoansListUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(MerchantLoansListUseCase);
  });

  it('should execute successfully and show sensitive data', async () => {
    loansRepository.getLoans.and.returnValue(of(mockLoansResponse));
    sensitiveDataRepository.getFlag.and.returnValue(of(true));

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(mockLoansResponse);
    expect(loansRepository.getLoans).toHaveBeenCalledTimes(1);
    expect(sensitiveDataRepository.getFlag).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should execute successfully and hide sensitive data', async () => {
    loansRepository.getLoans.and.returnValue(of(mockLoansResponse));
    sensitiveDataRepository.getFlag.and.returnValue(of(false));

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    const expectedResponse = {
      ...mockLoansResponse,
      content: mockLoansResponse.content.map(item => ({
        ...item,
        customerUrl: '',
      })),
    };

    expect(result).toEqual(expectedResponse);
    expect(loansRepository.getLoans).toHaveBeenCalledTimes(1);
    expect(sensitiveDataRepository.getFlag).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle repository error', async () => {
    const error = new Error('test error');
    loansRepository.getLoans.and.returnValue(throwError(() => error));
    usecaseErrorHandlerSpy.and.returnValue(throwError(() => error));

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(error);

    expect(loansRepository.getLoans).toHaveBeenCalledTimes(1);
    expect(sensitiveDataRepository.getFlag).not.toHaveBeenCalled();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle synchronous error from mapper', async () => {
    const error = new Error('mapper error');
    spyOn(SharedCriteriaMapper, 'fromUIToRepository').and.throwError(error);
    usecaseErrorHandlerSpy.and.returnValue(throwError(() => error));

    await expectAsync(
      lastValueFrom(usecase.execute(mockCriteria))
    ).toBeRejectedWith(error);

    expect(SharedCriteriaMapper.fromUIToRepository).toHaveBeenCalledWith(
      mockCriteria
    );
    expect(loansRepository.getLoans).not.toHaveBeenCalled();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(1);
  });
});
