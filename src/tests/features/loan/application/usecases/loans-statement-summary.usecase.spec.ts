import { TestBed } from '@angular/core/testing';
import { LoaderService } from '@aplazo/merchant/shared';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import { GetLoansStatementSummaryUseCase } from '../../../../../app/features/loan/application/usecases/loans-statement-summary.usecase';
import { IStatementSummaryDto } from '../../../../../app/features/loan/domain/dtos/statement-summary.dto';
import { MerchantLoansStatisticsRepository } from '../../../../../app/features/loan/domain/repositories/merchant-loans-statistics.repository';

describe('GetLoansStatementSummaryUseCase', () => {
  let usecase: GetLoansStatementSummaryUseCase;
  let statsRepository: jasmine.SpyObj<
    MerchantLoansStatisticsRepository<unknown, Observable<unknown>>
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantLoansStatisticsRepository,
          useValue: jasmine.createSpyObj('MerchantLoansStatisticsRepository', [
            'getStatementSummary',
          ]),
        },
        provideLoaderTesting(),
        GetLoansStatementSummaryUseCase,
      ],
    });

    usecase = TestBed.inject(GetLoansStatementSummaryUseCase);
    statsRepository = TestBed.inject(
      MerchantLoansStatisticsRepository
    ) as jasmine.SpyObj<
      MerchantLoansStatisticsRepository<unknown, Observable<unknown>>
    >;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    spyOn(console, 'error');
  });

  it('should create an instance of GetLoansStatementSummaryUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetLoansStatementSummaryUseCase);
  });

  it('should execute successfully and return statement summary', async () => {
    const mockSummary: IStatementSummaryDto = {
      latestPayment: 1706745600,
      nextPayment: 1709251200,
    };

    statsRepository.getStatementSummary.and.returnValue(of(mockSummary));

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual(mockSummary);
    expect(statsRepository.getStatementSummary).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error and return default summary', async () => {
    statsRepository.getStatementSummary.and.returnValue(
      throwError(() => new Error('test error'))
    );

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual({ latestPayment: 0, nextPayment: 0 });
    expect(statsRepository.getStatementSummary).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(console.error).toHaveBeenCalled();
  });

  it('should return default summary for empty response', async () => {
    statsRepository.getStatementSummary.and.returnValue(of(null));

    const result = await lastValueFrom(usecase.execute());

    expect(result).toEqual({ latestPayment: 0, nextPayment: 0 });
    expect(statsRepository.getStatementSummary).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });
});
