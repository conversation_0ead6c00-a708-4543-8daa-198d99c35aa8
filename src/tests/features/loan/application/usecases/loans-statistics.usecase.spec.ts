import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of, throwError } from 'rxjs';
import {
  emptyDefaultLoansStatisticsResponse,
  MerchantLoansStatisticsUseCase,
} from '../../../../../app/features/loan/application/usecases/loans-statistics.usecase';
import { IGeneralLoanStatisticsDto } from '../../../../../app/features/loan/domain/dtos/general-loans-statistics.dto';
import { MerchantLoansStatisticsRepository } from '../../../../../app/features/loan/domain/repositories/merchant-loans-statistics.repository';
import { SharedCriteriaWithBranchOfficesUIDto } from '../../../../../app/features/shared/shared-criteria';

describe('MerchantLoansStatisticsUseCase', () => {
  let usecase: MerchantLoansStatisticsUseCase;
  let statsRepository: jasmine.SpyObj<
    MerchantLoansStatisticsRepository<
      unknown,
      Observable<IGeneralLoanStatisticsDto>
    >
  >;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let usecaseErrorHandlerSpy: jasmine.Spy;

  const mockCriteria: SharedCriteriaWithBranchOfficesUIDto = {
    status: 'approved',
    dateRange: {
      startDate: '01/01/2024',
      endDate: '31/01/2024',
    },
    branchOffices: [1, 2],
    pageNum: 1,
    pageSize: 10,
    element: '',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantLoansStatisticsRepository,
          useValue: jasmine.createSpyObj('MerchantLoansStatisticsRepository', [
            'getStatisticsByDateRange',
          ]),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        MerchantLoansStatisticsUseCase,
      ],
    });

    usecase = TestBed.inject(MerchantLoansStatisticsUseCase);
    statsRepository = TestBed.inject(
      MerchantLoansStatisticsRepository
    ) as jasmine.SpyObj<
      MerchantLoansStatisticsRepository<
        unknown,
        Observable<IGeneralLoanStatisticsDto>
      >
    >;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    usecaseErrorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    );
  });

  it('should create an instance of MerchantLoansStatisticsUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(MerchantLoansStatisticsUseCase);
  });

  it('should execute successfully and return statistics', async () => {
    const mockStats: IGeneralLoanStatisticsDto = {
      merchantId: 1,
      customers: 10,
      salesAmount: 1000,
      salesOrder: 5,
      salesAmountAvg: 200,
      latestPayment: 1706745600,
      nextPayment: 1709251200,
    };

    statsRepository.getStatisticsByDateRange.and.returnValue(of(mockStats));

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(mockStats);
    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });

  it('should handle error and return default statistics', async () => {
    statsRepository.getStatisticsByDateRange.and.returnValue(
      throwError(() => new Error('test error'))
    );
    usecaseErrorHandlerSpy.and.callThrough();

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(emptyDefaultLoansStatisticsResponse);
    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
  });

  it('should return default statistics for empty response', async () => {
    statsRepository.getStatisticsByDateRange.and.returnValue(of(null));

    const result = await lastValueFrom(usecase.execute(mockCriteria));

    expect(result).toEqual(emptyDefaultLoansStatisticsResponse);
    expect(statsRepository.getStatisticsByDateRange).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorHandlerSpy).not.toHaveBeenCalled();
  });
});
