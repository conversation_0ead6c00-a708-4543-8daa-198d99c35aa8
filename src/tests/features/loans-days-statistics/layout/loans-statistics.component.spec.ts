import { Ng<PERSON><PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideRouter, Router, RouterOutlet } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoTabGroupComponent,
  AplazoTabsComponents,
} from '@aplazo/shared-ui/tabs';
import {
  dailyLoansLabels,
  LoansStatisticsComponent,
} from '../../../../app/features/loans-days-statistics/layout/loans-statistics.component';

@Component({
  standalone: true,
  template: '',
})
export class TestComponent {}

describe('LoansStatisticsComponent', () => {
  let component: LoansStatisticsComponent;
  let routerHarness: RouterTestingHarness;
  let router: Router;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [
        TestComponent,
        LoansStatisticsComponent,
        NgFor,
        RouterOutlet,
        AplazoCardComponent,
        AplazoTabsComponents,
      ],
      providers: [
        provideRouter([
          {
            path: '',
            component: LoansStatisticsComponent,
            children: [
              {
                path: '',
                redirectTo: dailyLoansLabels.SEMANA,
                pathMatch: 'full',
              },
              { path: dailyLoansLabels.SEMANA, component: TestComponent },
              { path: dailyLoansLabels.HORA, component: TestComponent },
              { path: dailyLoansLabels.DÍAS, component: TestComponent },
            ],
          },
        ]),
      ],
    });

    routerHarness = await RouterTestingHarness.create();
    component = await routerHarness.navigateByUrl(
      '/',
      LoansStatisticsComponent
    );
    router = TestBed.inject(Router);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have as many tabs as labels in dailyLoansLabels', () => {
    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    expect(tabs.length)
      .withContext('Expected as many tabs as labels in dailyLoansLabels')
      .toBe(Object.keys(dailyLoansLabels).length);
  });

  it('should have one tab active only', () => {
    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    const activeTabs = tabs
      .flatMap(tab =>
        tab.nativeElement.classList
          .values()
          .some(val => val === 'aplazo-tab--active')
      )
      .reduce((acc, val) => {
        if (val) {
          acc += 1;
        }
        return acc;
      }, 0);

    expect(activeTabs).withContext('Expected one tab active only').toBe(1);
  });

  it('should navigate when tab is clicked', fakeAsync(async () => {
    const tabgroup = routerHarness.routeDebugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    const tabs = tabgroup.queryAll(By.css('button.aplazo-tab'));

    expect(router.url).toContain(dailyLoansLabels.SEMANA);

    const tab = tabs[1];
    tab.nativeElement.click();

    routerHarness.detectChanges();
    tick();

    expect(router.url).toContain(dailyLoansLabels.HORA);
  }));
});
