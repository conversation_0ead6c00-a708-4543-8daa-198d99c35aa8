import { AplazoPadWithPipe } from 'src/app/features/loans-days-statistics/pipes/pad-with.pipe';

describe('PadWithPipe', () => {
  let pipe: AplazoPadWithPipe;

  beforeEach(() => {
    pipe = new AplazoPadWithPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it("should pad with 0's for numbers less than 10", () => {
    const expected = '01:00';

    const result = pipe.transform(1);

    expect(result).toEqual(expected);
  });

  it("should pad with 0's for numbert greater than 10", () => {
    const expected = '10:00';

    const result = pipe.transform(10);

    expect(result).toEqual(expected);
  });

  it('should return 00 for null values', () => {
    const expected = '00';

    const result = pipe.transform(null);

    expect(result).toEqual(expected);
  });

  it('should return 00 for NaN values', () => {
    const expected = '00';

    const result = pipe.transform(NaN);

    expect(result).toEqual(expected);
  });

  it('should return 00 for undefined values', () => {
    const expected = '00';

    const result = pipe.transform(undefined);

    expect(result).toEqual(expected);
  });
});
