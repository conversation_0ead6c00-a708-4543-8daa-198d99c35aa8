import { AplazoWeekdayPipe } from '../../../../app/features/loans-days-statistics/pipes/weekday.pipe';

describe('WeekdayPipe', () => {
  let pipe: AplazoWeekdayPipe;

  beforeEach(() => {
    pipe = new AplazoWeekdayPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return empty string for null values', () => {
    const expected = '';

    const result = pipe.transform(null);

    expect(result).toEqual(expected);
  });

  it('should return empty string for NaN values', () => {
    const expected = '';

    const result = pipe.transform(NaN);

    expect(result).toEqual(expected);
  });

  it('should return empty string for negative values', () => {
    const expected = '';

    const result = pipe.transform(-1);

    expect(result).toEqual(expected);
  });

  it('should return empty string for values greater than 6', () => {
    const expected = '';

    const result = pipe.transform(7);

    expect(result).toEqual(expected);
  });

  it('should return "Domingo" for 0', () => {
    const expected = 'Domingo';

    const result = pipe.transform(0);

    expect(result).toEqual(expected);
  });

  it('should return "Lunes" for 1', () => {
    const expected = 'Lunes';

    const result = pipe.transform(1);

    expect(result).toEqual(expected);
  });

  it('should return "Sábado" for 6', () => {
    const expected = 'Sábado';

    const result = pipe.transform(6);

    expect(result).toEqual(expected);
  });
});
