import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { setInitialDaysToCriteria } from '../../../../app/features/loans-days-statistics/resolvers/criteria.resolver';
import { SharedCriteria } from '../../../../app/services/shared-criteria.store';

@Component({
  standalone: true,
  template: '',
})
export class TestComponent {}

describe('setInitialDaysToCriteria', () => {
  let resolver: () => Promise<boolean>;
  let criteria: SharedCriteria;
  let setDaysSpy: jasmine.Spy;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [TestComponent],
      providers: [
        {
          provide: SharedCriteria,
          useValue: {
            setDays: () => {
              void 0;
            },
          },
        },
        provideRouter([
          {
            path: '',
            component: TestComponent,
            resolve: {
              setInitialDaysToCriteria,
            },
          },
          {
            path: '**',
            component: TestComponent,
            resolve: {
              setInitialDaysToCriteria,
            },
          },
        ]),
      ],
    });

    resolver = async () =>
      await TestBed.runInInjectionContext(
        () => setInitialDaysToCriteria({} as any, {} as any) as Promise<boolean>
      );
    criteria = TestBed.inject(SharedCriteria);
    setDaysSpy = spyOn(criteria, 'setDays').and.callThrough();
  });

  it('should return true', async () => {
    const result = await resolver();
    expect(result).toBeTrue();
  });

  it('should call setDays with the correct values', async () => {
    await resolver();

    expect(setDaysSpy).toHaveBeenCalledWith([0, 1, 2, 3, 4, 5, 6]);
    expect(setDaysSpy).toHaveBeenCalledTimes(1);
  });
});
