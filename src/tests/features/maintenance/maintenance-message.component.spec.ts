import { AsyncPipe } from '@angular/common';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { I18NService, I18NTranslator } from '@aplazo/i18n';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { provideI18NTesting } from 'src/tests/i18n.local';
import { MaintenanceMessageComponent } from '../../../app/features/maintenance/maintenance-message.component';

describe('MaintenanceMessageComponent', () => {
  let fixture: ComponentFixture<MaintenanceMessageComponent>;
  let component: MaintenanceMessageComponent;
  let i18nService: I18NTranslator;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AsyncPipe, AplazoLogoComponent],
      providers: [provideI18NTesting('maintenance')],
    });

    fixture = TestBed.createComponent(MaintenanceMessageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    i18nService = TestBed.inject(I18NService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have the aplazo logo', () => {
    const logo = fixture.debugElement.query(By.directive(AplazoLogoComponent));

    expect(logo).toBeTruthy();
  });

  it('should have the title that the i18n service provides', fakeAsync(() => {
    const title = fixture.debugElement.query(By.css('h1'));
    let expectedTile = '';

    i18nService
      .getTranslateObjectByKey({
        key: 'unavailableScreen',
        scope: 'maintenance',
      })
      .subscribe({
        next: (res: any) => {
          expectedTile = res.title;
        },
        error: fail,
      });

    tick();

    expect(title.nativeElement.textContent.trim()).toBe(expectedTile);
  }));

  it('should have an image with the correct src', fakeAsync(() => {
    const image = fixture.debugElement.query(
      By.css('[data-testid="maintenance-image"]')
    );
    let expectedSrc = '';

    i18nService
      .getTranslateObjectByKey({
        key: 'unavailableScreen',
        scope: 'maintenance',
      })
      .subscribe({
        next: (res: any) => {
          console.log(res);
          expectedSrc = res.imageUrl;
        },
        error: fail,
      });

    tick();

    expect(image.nativeElement.src).toBe(expectedSrc);
  }));
});
