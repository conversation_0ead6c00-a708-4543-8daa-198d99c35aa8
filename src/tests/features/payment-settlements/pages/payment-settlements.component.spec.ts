import {
  <PERSON>ync<PERSON><PERSON><PERSON>,
  I18nPluralPipe,
  Ng<PERSON>lass,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON>f,
} from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import {
  LoaderService,
  NotifierService,
  provideTemporal,
  RedirectionService,
} from '@aplazo/merchant/shared';
import {
  FileGeneratorService,
  provideCsvMapper,
} from '@aplazo/merchant/shared-dash';
import {
  LocalLoader,
  LocalNotifier,
  LocalRedirecter,
} from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponent,
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
  MetricCardComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import {
  AplazoSimpleTableComponent,
  AplazoSimpleTableComponents,
  AplazoSimpleTableHeaderCellDirective,
} from '@aplazo/shared-ui/simple-table';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { of } from 'rxjs';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../app/config/merchant-core.environment';
import { SettlementsPaymentResponseDto } from '../../../../app/features/payment-settlement/domain/dtos/settlements-payment-response.dto';
import { SettlementsPaymentStatistics } from '../../../../app/features/payment-settlement/domain/entities/settlements-pyament-statistics';
import { SearchSettlementsPaymentRepository } from '../../../../app/features/payment-settlement/domain/repositories/search-settlements-payment.repository';
import { SettlementsPaymentReportRepository } from '../../../../app/features/payment-settlement/domain/repositories/settlements-payment-report.repository';
import { SettlementsPaymentStatisticsRepository } from '../../../../app/features/payment-settlement/domain/repositories/settlements-payment-statistics.repository';
import { SettlementsPaymentRepository } from '../../../../app/features/payment-settlement/domain/repositories/settlements-payment.repository';
import { PaymentSettlementComponent } from '../../../../app/features/payment-settlement/infra/pages/payment-settlement.component';
import { SearchComponent } from '../../../../app/features/shared/components/search.component';
import { BranchOfficesStore } from '../../../../app/services/branch-offices.store';
import { EventManagerService } from '../../../../app/services/event-manger.service';
import { SharedCriteria } from '../../../../app/services/shared-criteria.store';
import { getTranslocoModule } from '../../shared/transloco-testing';
import {
  mockAllBranches,
  mockListRepoResp,
  mockReportResp,
  mockSummaryResp,
} from '../mock-payments';

let fixture: ComponentFixture<PaymentSettlementComponent>;
let component: PaymentSettlementComponent;

const setup = (
  list?: SettlementsPaymentResponseDto,
  stats?: SettlementsPaymentStatistics
) => {
  if (list) {
    TestBed.overrideProvider(SettlementsPaymentRepository, {
      useValue: {
        getList: () => of(list),
      },
    });
  }

  if (stats) {
    TestBed.overrideProvider(SettlementsPaymentStatisticsRepository, {
      useValue: {
        getStats: () => of(stats),
      },
    });
  }

  fixture = TestBed.createComponent(PaymentSettlementComponent);
  component = fixture.componentInstance;

  fixture.detectChanges();
};

describe('PaymentSettlementComponent', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        NgFor,
        NgClass,
        AsyncPipe,
        ReactiveFormsModule,
        I18nPluralPipe,
        AplazoDynamicPipe,
        AplazoIconComponent,
        AplazoCardComponent,
        AplazoButtonComponent,
        AplazoMetricCardComponents,
        AplazoPaginationComponent,
        AplazoSimpleTableComponents,
        AplazoCommonMessageComponents,
        AplazoFormFieldDirectives,
        AplazoSelectComponents,
        AplazoFormDatepickerComponent,
        SearchComponent,
        getTranslocoModule(),
      ],
      providers: [
        provideTranslocoScope('settlements'),
        SharedCriteria,
        TranslocoService,
        AplazoIconRegistryService,
        provideTemporal(),
        provideCsvMapper(),
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            redirectionsLandingPage: 'https://aplazo.net',
          },
        },
        {
          provide: FileGeneratorService,
          useValue: {
            generateFileAndDownload: (data: string[][]) => {
              return data;
            },
          },
        },
        {
          provide: EventManagerService,
          useValue: {
            sendTrackEvent: () => {
              void 0;
            },
          },
        },
        {
          provide: SettlementsPaymentRepository,
          useValue: {
            getList: () => of(mockListRepoResp),
          },
        },
        {
          provide: SettlementsPaymentStatisticsRepository,
          useValue: {
            getStatistics: () => of(mockSummaryResp),
          },
        },
        {
          provide: SettlementsPaymentReportRepository,
          useValue: {
            getContentForReport: () => of(mockReportResp),
          },
        },
        {
          provide: SearchSettlementsPaymentRepository,
          useValue: {
            searchByIdPhoneEmail: () => [],
          },
        },
        {
          provide: BranchOfficesStore,
          useValue: { branchOffices$: () => of(mockAllBranches) },
        },
      ],
    });
  });

  it('should be created', () => {
    setup();
    expect(component).toBeTruthy();
  });

  it('should have a counter label and includes the total of refunds', done => {
    setup();
    let total = 0;
    const counterLabel = fixture.debugElement.query(By.css('h5 > span'));

    component.loansCounting$.subscribe(count => {
      total = count;
    });

    expect(counterLabel.nativeElement.textContent.includes(total))
      .withContext('The counter label should include the total of refunds')
      .toBeTrue();

    done();
  });

  it('should have a counter label and not includes the total of refunds', done => {
    setup({
      ...mockListRepoResp,
      totalElements: 0,
      content: [],
      numberOfElements: 0,
      hasContent: false,
      number: 0,
      totalPages: 0,
    });

    let total = 0;
    const counterLabel = fixture.debugElement.query(By.css('h5 > span'));

    component.loansCounting$.subscribe(count => {
      total = count;
    });

    expect(counterLabel.nativeElement.textContent.includes(total))
      .withContext('The counter label should NOT include the total of refunds')
      .toBeFalse();

    done();
  });

  it('should have a table with 9 columns', () => {
    setup();

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    expect(table).toBeDefined();

    const columHeaders = table.queryAll(
      By.directive(AplazoSimpleTableHeaderCellDirective)
    );

    expect(columHeaders.length).toEqual(9);
  });

  it('should show pagination', done => {
    setup();

    let totalPages = 0;

    component.loansCounting$.subscribe(pages => {
      totalPages = pages;
    });

    const pagination = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );

    expect(totalPages)
      .withContext('The total pages should be greater than 0')
      .toBeGreaterThan(0);
    expect(pagination)
      .withContext('The page should show the pagination component')
      .toBeDefined();

    done();
  });

  it('should not show pagination', done => {
    setup({
      ...mockListRepoResp,
      totalElements: 0,
      content: [],
      numberOfElements: 0,
      hasContent: false,
      number: 0,
      totalPages: 0,
    });

    let totalPages = 0;

    component.loansCounting$.subscribe(pages => {
      totalPages = pages;
    });

    const pagination = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );

    expect(totalPages).withContext('The total pages should be 0').toEqual(0);
    expect(pagination)
      .withContext('The page should NOT show the pagination component')
      .toBeNull();

    done();
  });

  it('should show a message when there are no refunds', () => {
    setup({
      ...mockListRepoResp,
      totalElements: 0,
      content: [],
      numberOfElements: 0,
      hasContent: false,
      number: 0,
      totalPages: 0,
    });

    const message = fixture.debugElement.query(
      By.directive(AplazoCommonMessageComponent)
    );

    expect(message).toBeDefined();
  });

  it('should update page on changePage', done => {
    setup();

    const eventTrackSpy = spyOn(
      TestBed.inject(EventManagerService),
      'sendTrackEvent'
    ).and.callThrough();

    let result = 0;
    const newPage = 2;

    component.currentPage$.subscribe(page => {
      result = page;
    });

    component.changePage(newPage);

    expect(eventTrackSpy)
      .withContext('The event track should be called when the page changes')
      .toHaveBeenCalledTimes(1);
    expect(result)
      .withContext('The current page should be the same that the new page')
      .toBe(newPage);

    done();
  });

  it('should call reportRespository and fileGenerator on download', () => {
    setup();

    const eventTrackSpy = spyOn(
      TestBed.inject(EventManagerService),
      'sendTrackEvent'
    ).and.callThrough();

    const fileGeneratorSpy = spyOn(
      TestBed.inject(FileGeneratorService),
      'generateFileAndDownload'
    ).and.callThrough();

    const reportRepositorySpy = spyOn(
      TestBed.inject(SettlementsPaymentReportRepository),
      'getContentForReport'
    ).and.callThrough();

    component.download();

    expect(eventTrackSpy)
      .withContext('The event track should be called when the download starts')
      .toHaveBeenCalledTimes(1);
    expect(fileGeneratorSpy)
      .withContext(
        'The file generator should be called when the download starts'
      )
      .toHaveBeenCalledTimes(1);
    expect(reportRepositorySpy)
      .withContext(
        'The report repository should be called when the download starts'
      )
      .toHaveBeenCalledTimes(1);
  });

  it('should have a four card statistics', () => {
    setup();

    const statistics = fixture.debugElement.queryAll(
      By.directive(MetricCardComponent)
    );

    expect(statistics.length).toEqual(4);
  });
});
