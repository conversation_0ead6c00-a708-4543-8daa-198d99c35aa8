import { B2BDateRange, RawDateDayFirst } from '@aplazo/merchant/shared';
import {
  emptyDateRangeGenericRefundErrorMessage,
  emptyStartDateGenericRefundErrorMessage,
  emptyStatusGenericRefundErrorMessage,
  invalidFormatStartDateGenericRefundErrorMessage,
  invalidStatusGenericRefundErrorMessage,
  RefundListMapper,
} from '../../../../../app/features/refund/application/mappers/refund-list.mapper';
import { RefundListCriteriaDto } from '../../../../../app/features/refund/domain/entities/refund-list-criteria.dto';
import { RefundListUIRequestDto } from '../../../../../app/features/refund/domain/entities/refund-request.dto';
import { VALID_REFUND_STATUS } from '../../../../../app/features/refund/domain/entities/valid-refund-status';

describe('RefundListMapper', () => {
  describe('fromRequestToCriteria', () => {
    const requestTest: RefundListUIRequestDto = {
      refundsStatus: 'requested',
      dateRange: {
        startDate: '12/12/2019' as RawDateDayFirst,
        endDate: '12/12/2019' as RawDateDayFirst,
      },
      pageNum: 0,
      pageSize: 10,
    };

    it('should throw an error when not receive status as argument', () => {
      // @ts-expect-error: Testing invalid argument
      const testNoStatus: RefundListUIRequestDto = {
        dateRange: {
          startDate: '12/12/2019' as RawDateDayFirst,
          endDate: '12/12/2019' as RawDateDayFirst,
        },
        pageNum: 0,
        pageSize: 10,
      };

      expect(() =>
        RefundListMapper.fromRequestToCriteria(testNoStatus)
      ).toThrowError(emptyStatusGenericRefundErrorMessage);
    });

    it('should throw an error when not receive dateRange as argument', () => {
      // @ts-expect-error: testing invalid argument
      const testNoDate: RefundListUIRequestDto = {
        refundsStatus: 'requested',
        pageNum: 0,
        pageSize: 10,
      };

      expect(() =>
        RefundListMapper.fromRequestToCriteria(testNoDate)
      ).toThrowError(emptyDateRangeGenericRefundErrorMessage);
    });

    it('should throw an error when not receive valid startDate as argument', () => {
      // @ts-expect-error: Testing invalid argument
      const dateRange: B2BDateRange = {
        endDate: '12/12/2019' as RawDateDayFirst,
      };
      const testNoDate: RefundListUIRequestDto = {
        refundsStatus: 'requested',
        dateRange,
        pageNum: 0,
        pageSize: 10,
      };

      expect(() =>
        RefundListMapper.fromRequestToCriteria(testNoDate)
      ).toThrowError(emptyStartDateGenericRefundErrorMessage);
    });

    it('should throw an error when not receive valid startDate format', () => {
      const testNoDate: RefundListUIRequestDto = {
        refundsStatus: 'requested',
        dateRange: {
          startDate: '12-12-2019' as RawDateDayFirst,
          endDate: '12/12/2019' as RawDateDayFirst,
        },
        pageNum: 0,
        pageSize: 10,
      };

      expect(() =>
        RefundListMapper.fromRequestToCriteria(testNoDate)
      ).toThrowError(invalidFormatStartDateGenericRefundErrorMessage);
    });

    it('should throw an error when not receive validStatus as argument', () => {
      const testWrongStatus: RefundListUIRequestDto = {
        ...requestTest,
        // @ts-expect-error: Testing invalid argument
        refundsStatus: 'WRONG',
      };
      expect(() =>
        RefundListMapper.fromRequestToCriteria(testWrongStatus)
      ).toThrowError(invalidStatusGenericRefundErrorMessage);

      const testWrongStatus2 = { ...requestTest, refundsStatus: [] };

      expect(() =>
        RefundListMapper.fromRequestToCriteria(testWrongStatus2 as any)
      ).toThrowError(invalidStatusGenericRefundErrorMessage);
    });

    it('should return a string when receives validStatus', () => {
      const testValidStatus: RefundListUIRequestDto = {
        ...requestTest,
        refundsStatus: 'requested',
      };

      const expectedResult: RefundListCriteriaDto = {
        status: [
          VALID_REFUND_STATUS.requested,
          VALID_REFUND_STATUS.failedTransformacion,
          VALID_REFUND_STATUS.processing,
        ].join(','),
        pageNum: 0,
        pageSize: 10,
        endDate: '12/12/2019' as RawDateDayFirst,
        startDate: '12/12/2019' as RawDateDayFirst,
      };

      expect(RefundListMapper.fromRequestToCriteria(testValidStatus)).toEqual(
        expectedResult
      );
    });
  });
});
