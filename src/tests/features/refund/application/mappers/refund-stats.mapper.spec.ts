import { RefundStatsMapper } from '../../../../../app/features/refund/application/mappers/refund-stats.mapper';
import { RefundStatsResponseDto } from '../../../../../app/features/refund/domain/entities/refund-stats-response.dto';

describe('RefundStatsMapper', () => {
  it('should map successfully the request', () => {
    const request = [
      {
        salesAmount: 0,
        refundAmount: 911,
        totalRefunds: 6,
        customers: 0,
        status: 'REQUESTED',
      },
      {
        salesAmount: 0,
        refundAmount: 100,
        totalRefunds: 1,
        customers: 0,
        status: 'REFUNDED',
      },
    ];

    const expectedResult = {
      refunded: {
        refundAmount: 100,
        totalRefunds: 1,
      },
      requested: {
        refundAmount: 911,
        totalRefunds: 6,
      },
    };

    expect(RefundStatsMapper.fromResponseToUI(request)).toEqual(expectedResult);
  });

  it('should map to a default response when some data is not present', () => {
    const request = [
      {
        salesAmount: 0,
        refundAmount: 100,
        totalRefunds: 1,
        customers: 0,
        status: 'REFUNDED',
      },
    ];

    const expectedResult = {
      refunded: {
        refundAmount: 100,
        totalRefunds: 1,
      },
      requested: {
        refundAmount: 0,
        totalRefunds: 0,
      },
    };

    expect(RefundStatsMapper.fromResponseToUI(request)).toEqual(expectedResult);

    const request1 = [
      {
        salesAmount: 0,
        refundAmount: 100,
        totalRefunds: 1,
        customers: 0,
        status: 'REQUESTED',
      },
    ];

    const expectedResult1 = {
      requested: {
        refundAmount: 100,
        totalRefunds: 1,
      },
      refunded: {
        refundAmount: 0,
        totalRefunds: 0,
      },
    };

    expect(RefundStatsMapper.fromResponseToUI(request1)).toEqual(
      expectedResult1
    );

    const request2: RefundStatsResponseDto[] = [];

    const expectedResult2 = {
      requested: {
        refundAmount: 0,
        totalRefunds: 0,
      },
      refunded: {
        refundAmount: 0,
        totalRefunds: 0,
      },
    };

    expect(RefundStatsMapper.fromResponseToUI(request2)).toEqual(
      expectedResult2
    );
  });
});
