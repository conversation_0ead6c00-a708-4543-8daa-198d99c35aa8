import { B2BDateRange, RawDateDayFirst } from '@aplazo/merchant/shared';
import {
  RefundsMapper,
  emptyStatusGenericRefundsMapperErrorMessage,
  invalidStatusGenericRefundsMapperErrorMessage,
} from '../../../../../app/features/refund/application/mappers/refunds.mapper';
import { RefundListUIRequestDto } from '../../../../../app/features/refund/domain/entities/refund-request.dto';
import {
  RefundStatusKey,
  VALID_REFUND_STATUS,
} from '../../../../../app/features/refund/domain/entities/valid-refund-status';

describe('RefundsMapper', () => {
  const requestTest: {
    refundsStatus: RefundStatusKey[];
    dateRange: B2BDateRange;
  } = {
    refundsStatus: ['requested'],
    dateRange: {
      startDate: '12/12/2019' as RawDateDayFirst,
      endDate: '12/12/2019' as RawDateDayFirst,
    },
  };

  it('should throw an error when not receive valid status as argument', () => {
    const testWrongStatus: RefundListUIRequestDto = {
      ...requestTest,
      // @ts-expect-error: Testing invalid argument
      refundsStatus: ['wrongStatus'],
    };

    expect(() =>
      RefundsMapper.fromUiRequestToRepositoryRequest(testWrongStatus as any)
    ).toThrowError(invalidStatusGenericRefundsMapperErrorMessage);

    const testWrongStatus2 = { ...requestTest, refundsStatus: [] };

    expect(() =>
      RefundsMapper.fromUiRequestToRepositoryRequest(testWrongStatus2 as any)
    ).toThrowError(emptyStatusGenericRefundsMapperErrorMessage);
  });

  it('should map the input successfully', () => {
    const testValidStatus: {
      refundsStatus: RefundStatusKey[];
      dateRange: B2BDateRange;
    } = {
      ...requestTest,
      refundsStatus: ['requested'],
    };

    const expectedResult = {
      status: VALID_REFUND_STATUS.requested,
      startDate: '12/12/2019' as RawDateDayFirst,
      endDate: '12/12/2019' as RawDateDayFirst,
    };

    expect(
      RefundsMapper.fromUiRequestToRepositoryRequest(testValidStatus as any)
    ).toEqual(expectedResult);

    const testValidStatus2: {
      refundsStatus: RefundStatusKey[];
      dateRange: B2BDateRange;
    } = {
      ...requestTest,
      refundsStatus: ['requested', 'refunded'],
    };

    const expectedResult2 = {
      status: [
        VALID_REFUND_STATUS.requested,
        VALID_REFUND_STATUS.refunded,
      ].join(','),
      startDate: '12/12/2019' as RawDateDayFirst,
      endDate: '12/12/2019' as RawDateDayFirst,
    };

    expect(
      RefundsMapper.fromUiRequestToRepositoryRequest(testValidStatus2 as any)
    ).toEqual(expectedResult2);
  });
});
