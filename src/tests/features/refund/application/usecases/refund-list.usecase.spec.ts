import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  RawDateDayFirst,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { RefundListUseCase } from '../../../../../app/features/refund/application/usecases/refund-list.usecase';
import { RefundListResponseDto } from '../../../../../app/features/refund/domain/entities/refund-list-response.dto';
import { RefundListUIRequestDto } from '../../../../../app/features/refund/domain/entities/refund-request.dto';
import { RefundListRepository } from '../../../../../app/features/refund/domain/repositories/refund-list.repository';

const pending = [
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'SOLICITADO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66402,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Product not as described',
    created: '2023-10-13T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'SOLICITADO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66401,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-14T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'SOLICITADO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66404,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Product not as described',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'SOLICITADO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66412,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'SOLICITADO',
    saleAmount: 200.0,
    refundAmount: 100.0,
    merchantCancelId: 3364,
    loanId: 66416,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'RP',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'SOLICITADO',
    saleAmount: 200.0,
    refundAmount: 100.0,
    merchantCancelId: 3364,
    loanId: 66420,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'RP',
    merchantId: null,
  },
];
const refunded = [
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'DEVUELTO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66402,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Product not as described',
    created: '2023-10-13T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'DEVUELTO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66401,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-14T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'DEVUELTO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66404,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Product not as described',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'DEVUELTO',
    saleAmount: 200.0,
    refundAmount: 200.0,
    merchantCancelId: 3364,
    loanId: 66412,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'R',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'DEVUELTO',
    saleAmount: 200.0,
    refundAmount: 100.0,
    merchantCancelId: 3364,
    loanId: 66416,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'RP',
    merchantId: null,
  },
  {
    statusLoan: 'APROBADO',
    statusPayment: 'EN PROCESO',
    statusRefund: 'DEVUELTO',
    saleAmount: 200.0,
    refundAmount: 100.0,
    merchantCancelId: 3364,
    loanId: 66420,
    cartId: 'OQgwPguY5rIVZWt3BZsKA',
    reason: 'Wrong item received',
    created: '2023-10-12T17:28:32.493259',
    requestType: 'RP',
    merchantId: null,
  },
];

describe('RefundListUseCase', () => {
  let usecase: RefundListUseCase;
  let repository: jasmine.SpyObj<RefundListRepository>;
  let loader: LoaderService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        RefundListUseCase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: RefundListRepository,
          useValue: jasmine.createSpyObj<RefundListRepository>(
            'RefundListRepository',
            ['getList']
          ),
        },
      ],
    });

    usecase = TestBed.inject(RefundListUseCase);
    repository = TestBed.inject(
      RefundListRepository
    ) as jasmine.SpyObj<RefundListRepository>;
    loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    errorHandlerSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should create an instance of usecase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(RefundListUseCase);
  });

  it('should retrieve a list of refunds with pending status', fakeAsync(() => {
    const expectedResult: RefundListResponseDto = {
      size: 5,
      totalElements: 6,
      totalPages: 2,
      hasContent: true,
      numberOfElements: 6,
      first: true,
      last: false,
      content: pending.slice(0, 5),
      number: 0,
      pageable: null,
    };
    const request: RefundListUIRequestDto = {
      refundsStatus: 'requested',
      dateRange: {
        startDate: '12/10/2023' as RawDateDayFirst,
        endDate: '14/10/2023' as RawDateDayFirst,
      },
      pageNum: 0,
      pageSize: 5,
    };

    repository.getList.and.returnValue(of(expectedResult));

    let response: any;

    usecase.execute(request).subscribe({
      next: res => {
        response = res;
      },
      error: fail,
    });

    tick();

    expect(response).toBeTruthy();
    expect(repository.getList).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledWith({
      status: 'REQUESTED,TRANSFORMER_FAILED,PROCESSING',
      pageNum: 0,
      pageSize: 5,
      startDate: '12/10/2023',
      endDate: '14/10/2023',
    });
    expect(response.content).toBeInstanceOf(Array);
    expect(response.content.length).toBe(expectedResult.size);
    expect(response.totalElements).toBe(expectedResult.totalElements);
    expect(response.totalPages).toBe(expectedResult.totalPages);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).not.toHaveBeenCalled();
  }));
  it('should retrieve a list of refunds with refunded status', fakeAsync(() => {
    const expectedResult: RefundListResponseDto = {
      size: 5,
      totalElements: 6,
      totalPages: 2,
      hasContent: true,
      numberOfElements: 6,
      first: true,
      last: false,
      content: refunded.slice(0, 5),
      number: 0,
      pageable: null,
    };
    const request: RefundListUIRequestDto = {
      refundsStatus: 'refunded',
      dateRange: {
        startDate: '12/10/2023' as RawDateDayFirst,
        endDate: '14/10/2023' as RawDateDayFirst,
      },
      pageNum: 0,
      pageSize: 5,
    };

    repository.getList.and.returnValue(of(expectedResult));

    let response: any;

    usecase.execute(request).subscribe({
      next: res => {
        response = res;
      },
      error: fail,
    });

    tick();

    expect(response).toBeTruthy();
    expect(repository.getList).toHaveBeenCalledTimes(1);
    expect(repository.getList).toHaveBeenCalledWith({
      status: 'REFUNDED',
      pageNum: 0,
      pageSize: 5,
      startDate: '12/10/2023',
      endDate: '14/10/2023',
    });
    expect(response.content).toBeInstanceOf(Array);
    expect(response.content.length).toBe(expectedResult.size);
    expect(response.totalElements).toBe(expectedResult.totalElements);
    expect(response.totalPages).toBe(expectedResult.totalPages);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).not.toHaveBeenCalled();
  }));

  it('should retrieve default response on invalid refund status is sended', fakeAsync(() => {
    const request: RefundListUIRequestDto = {
      // @ts-expect-error: testing purposes
      refundsStatus: 'invalid',
      dateRange: {
        startDate: '12/10/2023' as RawDateDayFirst,
        endDate: '14/10/2023' as RawDateDayFirst,
      },
      pageNum: 0,
      pageSize: 5,
    };

    const expectedResult: RefundListResponseDto = {
      size: 0,
      totalElements: 0,
      totalPages: 0,
      hasContent: false,
      numberOfElements: 0,
      first: true,
      last: false,
      content: [],
      number: 0,
      pageable: null,
    };

    repository.getList.and.returnValue(of(expectedResult));

    let response: any;

    usecase.execute(request).subscribe({
      next: res => {
        response = res;
      },
      error: fail,
    });

    tick();

    expect(response).toBeTruthy();
    expect(response.content).toBeInstanceOf(Array);
    expect(response.content.length).toBe(expectedResult.size);
    expect(response.totalElements).toBe(expectedResult.totalElements);
    expect(response.totalPages).toBe(expectedResult.totalPages);
    expect(repository.getList).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  }));

  it('should retrieve default response on repository error', fakeAsync(() => {
    const request: RefundListUIRequestDto = {
      refundsStatus: 'requested',
      dateRange: {
        startDate: '12/10/2023' as RawDateDayFirst,
        endDate: '14/10/2023' as RawDateDayFirst,
      },
      pageNum: 0,
      pageSize: 5,
    };

    const expectedResult: RefundListResponseDto = {
      size: 0,
      totalElements: 0,
      totalPages: 0,
      hasContent: false,
      numberOfElements: 0,
      first: true,
      last: false,
      content: [],
      number: 0,
      pageable: null,
    };

    repository.getList.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let response: any;

    usecase.execute(request).subscribe({
      next: res => {
        response = res;
      },
      error: fail,
    });

    tick();

    expect(response).toBeTruthy();
    expect(response.content).toBeInstanceOf(Array);
    expect(response.content.length).toBe(expectedResult.size);
    expect(response.totalElements).toBe(expectedResult.totalElements);
    expect(response.totalPages).toBe(expectedResult.totalPages);
    expect(repository.getList).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  }));
});
