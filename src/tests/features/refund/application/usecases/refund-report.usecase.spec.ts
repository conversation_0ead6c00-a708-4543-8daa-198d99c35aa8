import {
  LoaderService,
  NotifierService,
  RawDateDayFirst,
} from '@aplazo/merchant/shared';
import {
  CsvMapperService,
  CsvMapperWithNativeApi,
  FileGeneratorService,
} from '@aplazo/merchant/shared-dash';
import {
  LocalFileGenerator,
  LocalLoader,
  LocalNotifier,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom } from 'rxjs';
import { emptyStartDateGenericRefundsMapperErrorMessage } from 'src/app/features/refund/application/mappers/refunds.mapper';
import {
  emptyRefundReportDefaultMessage,
  RefundsReportUseCase,
  successRefundReportDefaultMessage,
} from '../../../../../app/features/refund/application/usecases/refund-report.usecase';
import { RefundsReportRequestUIDto } from '../../../../../app/features/refund/domain/entities/refund-report-request-ui.dto';
import { RefundReportRepository } from '../../../../../app/features/refund/domain/repositories/refund-report.repository';
import { LocalRefundReportRepository } from '../../infra/local-refund-report.repository';

describe('RefundReportUseCase', () => {
  let usecase: RefundsReportUseCase;
  let repository: RefundReportRepository;
  let loader: LoaderService;
  let notifier: NotifierService;
  let fileGenerator: FileGeneratorService<Promise<string[][]>>;
  let csvMapper: CsvMapperService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;
  let notifierSuccessSpy: jasmine.Spy;

  beforeEach(() => {
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    repository = new LocalRefundReportRepository();
    fileGenerator = new LocalFileGenerator();
    csvMapper = new CsvMapperWithNativeApi();
    usecase = new RefundsReportUseCase(
      repository,
      loader,
      notifier,
      fileGenerator,
      csvMapper
    );
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    notifierWarningSpy = spyOn(notifier, 'warning').and.callThrough();
    notifierSuccessSpy = spyOn(notifier, 'success').and.callThrough();
  });

  it('should create an instance of usecase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(RefundsReportUseCase);
  });

  it('should retrieve a report of refunds', async () => {
    const request: RefundsReportRequestUIDto = {
      refundsStatus: 'requested',
      dateRange: {
        startDate: '12/10/2023' as RawDateDayFirst,
        endDate: '14/10/2023' as RawDateDayFirst,
      },
    };

    const response = await lastValueFrom(usecase.execute(request));

    const expectedRows = 7; // 6 rows + 1 header row

    expect(response).toBeTruthy();
    expect(response).toBeInstanceOf(Array);
    expect(response.length).toBe(expectedRows);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).not.toHaveBeenCalled();
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'Reporte generado',
      message: successRefundReportDefaultMessage,
    });
  });

  it('should retrieve an empty report of refunds', async () => {
    const request: RefundsReportRequestUIDto = {
      refundsStatus: 'requested',
      dateRange: {
        startDate: '01/10/2023' as RawDateDayFirst,
        endDate: '02/10/2023' as RawDateDayFirst,
      },
    };

    const response = await lastValueFrom(usecase.execute(request));

    const expectedRows = 0;

    expect(response).toBeTruthy();
    expect(response).toBeInstanceOf(Array);
    expect(response.length).toBe(expectedRows);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'Reporte vacío',
      message: emptyRefundReportDefaultMessage,
    });
  });

  it('should retrieve an empty report of refunds when the start date is invalid', async () => {
    // @ts-expect-error: without start date for testing purposes
    const invalidDateRange: B2BDateRange = {
      endDate: '02/10/2023' as RawDateDayFirst,
    };
    const request: RefundsReportRequestUIDto = {
      refundsStatus: 'requested',
      dateRange: invalidDateRange,
    };

    const response = await lastValueFrom(usecase.execute(request));

    const expectedRows = 0;

    expect(response).toBeTruthy();
    expect(response).toBeInstanceOf(Array);
    expect(response.length).toBe(expectedRows);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: emptyStartDateGenericRefundsMapperErrorMessage,
    });
  });
});
