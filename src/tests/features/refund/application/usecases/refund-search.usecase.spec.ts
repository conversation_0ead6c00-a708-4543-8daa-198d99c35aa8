import { HttpErrorResponse } from '@angular/common/http';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, throwError } from 'rxjs';
import { RefundSearchUseCase } from '../../../../../app/features/refund/application/usecases/refund-search.usecase';
import { Refund } from '../../../../../app/features/refund/domain/entities/refund';
import { RefundSearchRepository } from '../../../../../app/features/refund/domain/repositories/refund-search.repository';
import { LocalRefundSearchRepository } from '../../infra/local-refund-search.repository';

describe('RefundSearchUseCase', () => {
  let usecase: RefundSearchUseCase;
  let repository: RefundSearchRepository;
  let loader: LoaderService;
  let notifier: NotifierService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;

  beforeEach(() => {
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    repository = new LocalRefundSearchRepository();
    usecase = new RefundSearchUseCase(repository, loader, notifier);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    notifierWarningSpy = spyOn(notifier, 'warning').and.callThrough();
  });

  it('should create an instance of usecase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(RefundSearchUseCase);
  });

  it('should return a list of refunds on valid loanId', async () => {
    const expectedResult: Refund[] = [
      {
        statusLoan: 'APROBADO',
        statusPayment: 'EN PROCESO',
        statusRefund: 'SOLICITADO',
        saleAmount: 333,
        refundAmount: 333,
        merchantCancelId: 3357,
        loanId: 65599,
        cartId: 'BdIop-DZ43mGJZ6oaPy3-',
        reason: 'Refund Walmart-Aplazo rejected by Cashi',
        created: '2023-10-11T12:29:56.992754',
        requestType: 'R',
      },
    ];

    const result = await lastValueFrom(usecase.execute('65599'));

    expect(result).toBeTruthy();
    expect(result).toEqual(expectedResult);
    expect(result).toBeInstanceOf(Array);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).not.toHaveBeenCalled();
  });

  it('should return an empty list on invalid loanId', async () => {
    const expectedResult: Refund[] = [];

    const result = await lastValueFrom(usecase.execute('1'));

    expect(result).toBeTruthy();
    expect(result).toEqual(expectedResult);
    expect(result).toBeInstanceOf(Array);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).not.toHaveBeenCalled();
  });

  it('should return an empty list when repository throws http error', async () => {
    spyOn(repository, 'findRefund').and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: 'test error',
            status: 500,
          })
      )
    );
    const result = await lastValueFrom(usecase.execute('error'));

    expect(result).toBeTruthy();
    expect(result).toEqual([]);
    expect(result).toBeInstanceOf(Array);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
  });
});
