import { HttpErrorResponse } from '@angular/common/http';
import {
  B2BDateRange,
  LoaderService,
  NotifierService,
  RawDateDayFirst,
} from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, throwError } from 'rxjs';
import { RefundStatsUseCase } from '../../../../../app/features/refund/application/usecases/refund-stats.usecase';
import { RefundStats } from '../../../../../app/features/refund/domain/entities/refund-stats';
import { RefundStatsRepository } from '../../../../../app/features/refund/domain/repositories/refund-stats.repository';
import { LocalRefundStatsRepository } from '../../infra/local-refund-stats.repository';

describe('RefundStatsUseCase', () => {
  let usecase: RefundStatsUseCase;
  let repository: RefundStatsRepository;
  let loader: LoaderService;
  let notifier: NotifierService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;

  beforeEach(() => {
    repository = new LocalRefundStatsRepository();
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    usecase = new RefundStatsUseCase(repository, loader, notifier);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    notifierWarningSpy = spyOn(notifier, 'warning').and.callThrough();
  });

  it('should create an instance of usecase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(RefundStatsUseCase);
  });

  it('should retrieve a list of refunds', async () => {
    const request: B2BDateRange = {
      startDate: '12/10/2023' as RawDateDayFirst,
      endDate: '14/10/2023' as RawDateDayFirst,
    };

    const response = await lastValueFrom(usecase.execute(request));

    const expectedResponse: RefundStats = {
      refunded: { refundAmount: 0, totalRefunds: 0 },
      requested: { refundAmount: 911, totalRefunds: 6 },
    };

    expect(response).toBeTruthy();
    expect(response.refunded.refundAmount).toBe(
      expectedResponse.refunded.refundAmount
    );
    expect(response.refunded.totalRefunds).toBe(
      expectedResponse.refunded.totalRefunds
    );
    expect(response.requested.refundAmount).toBe(
      expectedResponse.requested.refundAmount
    );
    expect(response.requested.totalRefunds).toBe(
      expectedResponse.requested.totalRefunds
    );
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).not.toHaveBeenCalled();
  });

  it('should show a warning message when an error occurs and retrieve default data', async () => {
    spyOn(repository, 'getStats').and.returnValue(
      throwError(
        () => new HttpErrorResponse({ error: 'test error', status: 400 })
      )
    );

    const request: B2BDateRange = {
      startDate: '12/10/2023' as RawDateDayFirst,
      endDate: '14/10/2023' as RawDateDayFirst,
    };

    const response = await lastValueFrom(usecase.execute(request));

    const expectedResponse: RefundStats = {
      refunded: { refundAmount: 0, totalRefunds: 0 },
      requested: { refundAmount: 0, totalRefunds: 0 },
    };

    expect(response).toBeTruthy();
    expect(response.refunded.refundAmount).toBe(
      expectedResponse.refunded.refundAmount
    );
    expect(response.refunded.totalRefunds).toBe(
      expectedResponse.refunded.totalRefunds
    );
    expect(response.requested.refundAmount).toBe(
      expectedResponse.requested.refundAmount
    );
    expect(response.requested.totalRefunds).toBe(
      expectedResponse.requested.totalRefunds
    );
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
  });
});
