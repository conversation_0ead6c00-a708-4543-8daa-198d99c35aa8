import { Observable, of } from 'rxjs';
import { RefundListCriteriaDto } from '../../../../app/features/refund/domain/entities/refund-list-criteria.dto';
import { RefundListResponseDto } from '../../../../app/features/refund/domain/entities/refund-list-response.dto';
import { RefundListRepository } from '../../../../app/features/refund/domain/repositories/refund-list.repository';
import refunds from './local-refunds-list.db.json';

export class LocalRefundListRepository implements RefundListRepository {
  readonly #items = refunds;

  getList(args: RefundListCriteriaDto): Observable<RefundListResponseDto> {
    const [startDay, startMonth, startYear] = args.startDate.split('/');
    const [endDay, endMonth, endYear] = args.endDate.split('/');

    const filteredByDate = this.#items.filter(b => {
      const [date] = b.created.split('T');
      const [bYear, bMonth, bDay] = date.split('-');

      return (
        bYear >= startYear &&
        bYear <= endYear &&
        bMonth >= startMonth &&
        bMonth <= endMonth &&
        bDay >= startDay &&
        bDay <= endDay
      );
    });

    const filteredByStatus = filteredByDate.filter(b => {
      if (args.status.toLowerCase().includes('refunded')) {
        return b.statusRefund === 'DEVUELTO';
      }

      if (args.status.toLowerCase().includes('requested')) {
        return b.statusRefund === 'SOLICITADO';
      }

      return true;
    });

    const initialIndex = args.pageNum * args.pageSize;
    const finalIndex = initialIndex + args.pageSize;
    const totalPages = Math.ceil(
      filteredByStatus.length / Number(args.pageSize)
    );

    if (initialIndex > filteredByStatus.length) {
      return of({
        content: [],
        number: Number(args.pageNum),
        size: Number(args.pageSize),
        totalElements: filteredByStatus.length,
        totalPages,
        hasContent: filteredByStatus.length > 0,
        numberOfElements: filteredByStatus.length,
        first: Number(args.pageNum) === 0,
        last: Number(args.pageNum) === totalPages - 1,
      });
    }

    return of({
      content: filteredByStatus.slice(initialIndex, finalIndex),
      number: Number(args.pageNum),
      size: Number(args.pageSize),
      totalElements: filteredByStatus.length,
      totalPages,
      hasContent: filteredByStatus.length > 0,
      numberOfElements: filteredByStatus.length,
      first: Number(args.pageNum) === 0,
      last: Number(args.pageNum) === totalPages - 1,
    });
  }
}
