import { Observable, of } from 'rxjs';
import { IRefundsReportRepositoryParamsDto } from '../../../../app/features/refund/domain/entities/refund-report-repository-params.dto';
import { RefundReportRepository } from '../../../../app/features/refund/domain/repositories/refund-report.repository';
import refunds from './local-refunds-list.db.json';

export class LocalRefundReportRepository implements RefundReportRepository {
  readonly #items = refunds;
  readonly #headerCols = [
    'loanId',
    'cartId',
    'merchantCancelId',
    'statusLoan',
    'statusPayment',
    'statusRefund',
    'saleAmount',
    'refundAmount',
    'reason',
    'created',
    'requestType',
    'merchantId',
  ];

  readonly #headerRow = this.#headerCols.join(',') + '\r\n';

  getRefundsByDaterangeAndStatus(
    args: IRefundsReportRepositoryParamsDto
  ): Observable<string> {
    const [startDay, startMonth, startYear] = args.startDate.split('/');
    const [endDay, endMonth, endYear] = args.endDate.split('/');

    const filteredByDate = this.#items.filter(b => {
      const [date] = b.created.split('T');
      const [bYear, bMonth, bDay] = date.split('-');

      return (
        bYear >= startYear &&
        bYear <= endYear &&
        bMonth >= startMonth &&
        bMonth <= endMonth &&
        bDay >= startDay &&
        bDay <= endDay
      );
    });

    const filteredByStatus = filteredByDate.filter(b => {
      if (args.status.toLowerCase().includes('refunded')) {
        return b.statusRefund === 'DEVUELTO';
      }

      if (args.status.toLowerCase().includes('requested')) {
        return b.statusRefund === 'SOLICITADO';
      }

      return true;
    });

    const csv = this.#fromJSONToCSV(filteredByStatus);

    return of(csv);
  }

  #fromJSONToCSV = (json: Array<Record<string, any>>): string => {
    const rows = json.map(b => {
      const row = this.#headerCols.map(col => {
        return b[col];
      });

      return row.join(',');
    });

    return this.#headerRow + rows.join('\r\n');
  };
}
