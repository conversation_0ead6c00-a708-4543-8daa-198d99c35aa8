import { Observable, of } from 'rxjs';
import { Refund } from '../../../../app/features/refund/domain/entities/refund';
import { RefundSearchRepository } from '../../../../app/features/refund/domain/repositories/refund-search.repository';
import refunds from './local-refunds-list.db.json';

export class LocalRefundSearchRepository implements RefundSearchRepository {
  readonly #items = refunds;

  findRefund(value: string): Observable<Refund[]> {
    const result = this.#items.find(
      item =>
        String(item.loanId) === value || value.includes(String(item.loanId))
    );

    if (!result) {
      return of([] as Refund[]);
    }

    const { merchantId, ...cleanedResult } = result;
    merchantId;
    return of([cleanedResult]);
  }
}
