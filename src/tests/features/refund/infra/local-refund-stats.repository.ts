import { Observable, of } from 'rxjs';
import { IRefundStatsCriteriaDto } from '../../../../app/features/refund/domain/entities/refund-stats-criteria.dto';
import { RefundStatsResponseDto } from '../../../../app/features/refund/domain/entities/refund-stats-response.dto';
import { RefundStatsRepository } from '../../../../app/features/refund/domain/repositories/refund-stats.repository';
import refunds from './local-refunds-list.db.json';

export class LocalRefundStatsRepository implements RefundStatsRepository {
  readonly #items = refunds;

  getStats(
    args: IRefundStatsCriteriaDto
  ): Observable<RefundStatsResponseDto[]> {
    const [startDay, startMonth, startYear] = args.startDate.split('/');
    const [endDay, endMonth, endYear] = args.endDate.split('/');

    const filteredByDate = this.#items.filter(b => {
      const [date] = b.created.split('T');
      const [bYear, bMonth, bDay] = date.split('-');

      return (
        bYear >= startYear &&
        bYear <= endYear &&
        bMonth >= startMonth &&
        bMonth <= endMonth &&
        bDay >= startDay &&
        bDay <= endDay
      );
    });

    const filteredByStatus = filteredByDate.filter(b => {
      let isItemAddeable = false;

      if (
        args.status.toLowerCase().includes('refunded') &&
        b.statusRefund === 'APROBADO'
      ) {
        isItemAddeable = true;
      }

      if (
        args.status.toLowerCase().includes('requested') &&
        b.statusRefund === 'SOLICITADO'
      ) {
        isItemAddeable = true;
      }

      return isItemAddeable;
    });

    const reduced: RefundStatsResponseDto[] = filteredByStatus.reduce(
      (acc, curr) => {
        const requestedData = acc.find(
          i => i.status.toLowerCase() === 'requested'
        );

        const approvedData = acc.find(
          i => i.status.toLowerCase() === 'refunded'
        );

        if (curr.statusRefund === 'SOLICITADO') {
          if (requestedData) {
            requestedData.refundAmount += curr.refundAmount;
            requestedData.totalRefunds += 1;
          } else {
            acc.push({
              salesAmount: 0,
              refundAmount: curr.refundAmount,
              totalRefunds: 1,
              customers: 0,
              status: 'REQUESTED',
            });
          }
        }

        if (curr.statusRefund === 'APROBADO') {
          if (approvedData) {
            approvedData.refundAmount += curr.refundAmount;
            approvedData.totalRefunds += 1;
          } else {
            acc.push({
              salesAmount: 0,
              refundAmount: curr.refundAmount,
              totalRefunds: 1,
              customers: 0,
              status: 'REFUNDED',
            });
          }
        }

        return acc;
      },
      [] as RefundStatsResponseDto[]
    );

    return of(reduced);
  }
}
