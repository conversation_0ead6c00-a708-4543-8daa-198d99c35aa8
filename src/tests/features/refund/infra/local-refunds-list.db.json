[{"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 333.0, "refundAmount": 333.0, "merchantCancelId": 3357, "loanId": 65599, "cartId": "BdIop-DZ43mGJZ6oaPy3-", "reason": "Refund Walmart-Aplazo rejected by <PERSON><PERSON>", "created": "2023-10-11T12:29:56.992754", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 200.0, "refundAmount": 200.0, "merchantCancelId": 3360, "loanId": 65631, "cartId": "0n5XA7-mZ78jFD-TwFRyq", "reason": "Refund Walmart-Aplazo rejected by <PERSON><PERSON>", "created": "2023-10-11T16:55:56.607759", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 111.0, "refundAmount": 111.0, "merchantCancelId": 3364, "loanId": 65855, "cartId": "OQgwPguY5rIVZWt3BZsKA", "reason": "Refund Walmart-Aplazo rejected by <PERSON><PERSON>", "created": "2023-10-12T17:28:32.493259", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 112.0, "refundAmount": 112.0, "merchantCancelId": 3396, "loanId": 66419, "cartId": "u1YmH7hBGfQXKkc4lL3BY", "reason": "Refund Walmart-Aplazo rejected by <PERSON><PERSON>", "created": "2023-10-19T00:19:51.439044", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 1111.0, "refundAmount": 1111.0, "merchantCancelId": 5292, "loanId": 77298, "cartId": "2ZUddo_BWs7FrRZ3BmoIx", "reason": "Refund Walmart-Aplazo rejected by <PERSON><PERSON>", "created": "2023-09-21T11:53:31.986801", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 1500.0, "refundAmount": 1500.0, "merchantCancelId": 3357, "loanId": 66402, "cartId": "BdIop-DZ43mGJZ6oaPy3-", "reason": "Defective product", "created": "2023-10-11T12:29:56.992754", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 1000.0, "refundAmount": 1000.0, "merchantCancelId": 3360, "loanId": 66403, "cartId": "0n5XA7-mZ78jFD-TwFRyq", "reason": "Wrong item received", "created": "2023-10-11T16:55:56.607759", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 200.0, "refundAmount": 200.0, "merchantCancelId": 3364, "loanId": 66404, "cartId": "OQgwPguY5rIVZWt3BZsKA", "reason": "Product not as described", "created": "2023-10-12T17:28:32.493259", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 500.0, "refundAmount": 500.0, "merchantCancelId": 3396, "loanId": 66405, "cartId": "u1YmH7hBGfQXKkc4lL3BY", "reason": "Product arrived damaged", "created": "2023-10-19T00:19:51.439044", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 100.0, "refundAmount": 100.0, "merchantCancelId": 3357, "loanId": 66406, "cartId": "BdIop-DZ43mGJZ6oaPy3-", "reason": "Product not received", "created": "2023-10-11T12:29:56.992754", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 1500.0, "refundAmount": 1500.0, "merchantCancelId": 3360, "loanId": 66407, "cartId": "0n5XA7-mZ78jFD-TwFRyq", "reason": "Product not as described", "created": "2023-10-11T16:55:56.607759", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 200.0, "refundAmount": 200.0, "merchantCancelId": 3364, "loanId": 66408, "cartId": "OQgwPguY5rIVZWt3BZsKA", "reason": "Product arrived damaged", "created": "2023-10-12T17:28:32.493259", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 500.0, "refundAmount": 500.0, "merchantCancelId": 3396, "loanId": 66409, "cartId": "u1YmH7hBGfQXKkc4lL3BY", "reason": "Wrong item received", "created": "2023-10-19T00:19:51.439044", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 100.0, "refundAmount": 100.0, "merchantCancelId": 3357, "loanId": 66410, "cartId": "BdIop-DZ43mGJZ6oaPy3-", "reason": "Defective product", "created": "2023-10-11T12:29:56.992754", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 1500.0, "refundAmount": 1500.0, "merchantCancelId": 3360, "loanId": 66411, "cartId": "0n5XA7-mZ78jFD-TwFRyq", "reason": "Product not received", "created": "2023-10-11T16:55:56.607759", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 200.0, "refundAmount": 200.0, "merchantCancelId": 3364, "loanId": 66412, "cartId": "OQgwPguY5rIVZWt3BZsKA", "reason": "Wrong item received", "created": "2023-10-12T17:28:32.493259", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 500.0, "refundAmount": 500.0, "merchantCancelId": 3396, "loanId": 66413, "cartId": "u1YmH7hBGfQXKkc4lL3BY", "reason": "Defective product", "created": "2023-10-19T00:19:51.439044", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 100.0, "refundAmount": 100.0, "merchantCancelId": 3357, "loanId": 66414, "cartId": "BdIop-DZ43mGJZ6oaPy3-", "reason": "Product not as described", "created": "2023-10-11T12:29:56.992754", "requestType": "R", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 1500.0, "refundAmount": 1500.0, "merchantCancelId": 3360, "loanId": 66415, "cartId": "0n5XA7-mZ78jFD-TwFRyq", "reason": "Product arrived damaged", "created": "2023-10-11T16:55:56.607759", "requestType": "R", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 200.0, "refundAmount": 100.0, "merchantCancelId": 3364, "loanId": 66416, "cartId": "OQgwPguY5rIVZWt3BZsKA", "reason": "Wrong item received", "created": "2023-10-12T17:28:32.493259", "requestType": "RP", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 500.0, "refundAmount": 250.0, "merchantCancelId": 3396, "loanId": 66417, "cartId": "u1YmH7hBGfQXKkc4lL3BY", "reason": "Defective product", "created": "2023-10-19T00:19:51.439044", "requestType": "RP", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 100.0, "refundAmount": 50.0, "merchantCancelId": 3357, "loanId": 66418, "cartId": "BdIop-DZ43mGJZ6oaPy3-", "reason": "Product not as described", "created": "2023-10-11T12:29:56.992754", "requestType": "RP", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 1500.0, "refundAmount": 750.0, "merchantCancelId": 3360, "loanId": 66491, "cartId": "0n5XA7-mZ78jFD-TwFRyq", "reason": "Product arrived damaged", "created": "2023-10-11T16:55:56.607759", "requestType": "RP", "merchantId": null}, {"statusLoan": "APROBADO", "statusPayment": "EN PROCESO", "statusRefund": "SOLICITADO", "saleAmount": 200.0, "refundAmount": 100.0, "merchantCancelId": 3364, "loanId": 66420, "cartId": "OQgwPguY5rIVZWt3BZsKA", "reason": "Wrong item received", "created": "2023-10-12T17:28:32.493259", "requestType": "RP", "merchantId": null}, {"statusLoan": "CANCELADO", "statusPayment": "EN PROCESO", "statusRefund": "DEVUELTO", "saleAmount": 500.0, "refundAmount": 250.0, "merchantCancelId": 3396, "loanId": 66421, "cartId": "u1YmH7hBGfQXKkc4lL3BY", "reason": "Defective product", "created": "2023-10-19T00:19:51.439044", "requestType": "RP", "merchantId": null}]