import { As<PERSON><PERSON><PERSON><PERSON>, I18nPluralPipe, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { provideTemporal } from '@aplazo/merchant/shared';
import { provideCsvMapper } from '@aplazo/merchant/shared-dash';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponent,
  AplazoCommonMessageComponents,
  AplazoMetricCardComponents,
  MetricCardComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import {
  AplazoSimpleTableComponent,
  AplazoSimpleTableComponents,
  AplazoSimpleTableHeaderCellDirective,
} from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { provideTranslocoScope, TranslocoService } from '@jsverse/transloco';
import { lastValueFrom, of, take } from 'rxjs';
import { RefundListUseCase } from '../../../../../app/features/refund/application/usecases/refund-list.usecase';
import { RefundsReportUseCase } from '../../../../../app/features/refund/application/usecases/refund-report.usecase';
import { RefundSearchUseCase } from '../../../../../app/features/refund/application/usecases/refund-search.usecase';
import { RefundStatsUseCase } from '../../../../../app/features/refund/application/usecases/refund-stats.usecase';
import { Refund } from '../../../../../app/features/refund/domain/entities/refund';
import { RefundListResponseDto } from '../../../../../app/features/refund/domain/entities/refund-list-response.dto';
import { RefundStats } from '../../../../../app/features/refund/domain/entities/refund-stats';
import { RefundsComponent } from '../../../../../app/features/refund/infra/pages/refunds/refunds.component';
import { CriteriaRefundService } from '../../../../../app/features/refund/infra/services/criteria-refund.service';
import { SearchComponent } from '../../../../../app/features/shared/components/search.component';
import { EventManagerService } from '../../../../../app/services/event-manger.service';
import { getTranslocoModule } from '../../../shared/transloco-testing';

const refundStatMock = {
  refunded: {
    refundAmount: 350,
    totalRefunds: 7,
  },
  requested: {
    refundAmount: 50,
    totalRefunds: 1,
  },
};

const refundListMock = {
  content: [
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22356,
      loanId: 143593,
      cartId: '199906914gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-13T09:10:12.400253',
      requestType: 'RP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22357,
      loanId: 143593,
      cartId: '199906914gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-13T09:10:12.770346',
      requestType: 'RP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22358,
      loanId: 143593,
      cartId: '199906914gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-13T09:10:13.125288',
      requestType: 'RP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22359,
      loanId: 143593,
      cartId: '199906914gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-13T09:10:13.497328',
      requestType: 'RP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22456,
      loanId: 142544,
      cartId: '199906784gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-14T09:10:24.078706',
      requestType: 'RP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22457,
      loanId: 142544,
      cartId: '199906784gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-14T09:10:24.512737',
      requestType: 'RP',
      merchantId: null,
    },
    {
      statusLoan: 'APROBADO',
      statusPayment: 'EN PROCESO',
      statusRefund: 'DEVUELTO',
      saleAmount: 200,
      refundAmount: 50,
      merchantCancelId: 22458,
      loanId: 142544,
      cartId: '199906784gddr10_esv23',
      reason: 'WM - POSUI - refund',
      created: '2024-11-14T09:10:24.894251',
      requestType: 'RP',
      merchantId: null,
    },
  ],
  number: 0,
  size: 10,
  totalElements: 7,
  totalPages: 1,
  hasContent: true,
  numberOfElements: 7,
  first: true,
  last: true,
};

const setup = (args?: {
  refund?: Partial<RefundListResponseDto>;
  stats?: Partial<RefundStats>;
  search?: Refund[];
  report?: string[][];
}) => {
  const defaultConfig: {
    refund?: Partial<RefundListResponseDto>;
    stats?: Partial<RefundStats>;
    search?: Refund[];
    report?: string[][];
  } = {
    refund: refundListMock,
    stats: refundStatMock,
    search: [],
    report: [],
  };

  const refunds = args?.refund || defaultConfig.refund;
  const stats = args?.stats || defaultConfig.stats;
  const search = args?.search || defaultConfig.search;
  const report = args?.report || defaultConfig.report;

  TestBed.configureTestingModule({
    imports: [
      AsyncPipe,
      NgFor,
      NgIf,
      ReactiveFormsModule,
      I18nPluralPipe,
      AplazoDynamicPipe,
      AplazoIconComponent,
      AplazoCardComponent,
      AplazoButtonComponent,
      AplazoMetricCardComponents,
      AplazoPaginationComponent,
      AplazoTooltipDirective,
      AplazoSimpleTableComponents,
      AplazoCommonMessageComponents,
      AplazoFormFieldDirectives,
      AplazoSelectComponents,
      AplazoFormDatepickerComponent,
      SearchComponent,
      getTranslocoModule(),
    ],
    providers: [
      provideTranslocoScope('refunds'),
      CriteriaRefundService,
      TranslocoService,
      AplazoIconRegistryService,
      provideTemporal(),
      provideCsvMapper(),
      provideLoaderTesting(),
      provideNotifierTesting(),
      {
        provide: RefundListUseCase,
        useValue: {
          execute: () => of(refunds),
        },
      },
      {
        provide: RefundStatsUseCase,
        useValue: {
          execute: () => of(stats),
        },
      },
      {
        provide: RefundSearchUseCase,
        useValue: {
          execute: () => of(search),
        },
      },
      {
        provide: RefundsReportUseCase,
        useValue: {
          execute: () => of(report),
        },
      },
      {
        provide: EventManagerService,
        useValue: {
          sendTrackEvent: () => {
            void 0;
          },
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(RefundsComponent);
  const component = fixture.componentInstance;

  const listUsecase = TestBed.inject(RefundListUseCase);
  const statsUsecase = TestBed.inject(RefundStatsUseCase);
  const reportUsecase = TestBed.inject(RefundsReportUseCase);
  const searchUsecase = TestBed.inject(RefundSearchUseCase);

  fixture.detectChanges();

  return {
    component,
    fixture,
    listUsecase,
    statsUsecase,
    reportUsecase,
    searchUsecase,
  };
};

describe('RefundsComponent', () => {
  it('should be created', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should have a title', async () => {
    const { component, fixture } = setup();

    const title = fixture.debugElement.query(By.css('h3'));

    const expectedTextUI = await lastValueFrom(
      component.refundsTitleText$.pipe(take(1))
    );

    expect(title.nativeElement.textContent.trim()).toEqual(
      expectedTextUI.title
    );
  });

  it('should have a four card statistics', () => {
    const { fixture } = setup();

    const statistics = fixture.debugElement.queryAll(
      By.directive(MetricCardComponent)
    );

    expect(statistics.length).toEqual(4);
  });

  it('should have a counter label and includes the total of refunds', async () => {
    const { component, fixture } = setup();

    const counterLabel = fixture.debugElement.query(By.css('h5 > span'));

    const total = await lastValueFrom(component.refundsCounting$.pipe(take(1)));

    expect(counterLabel.nativeElement.textContent.includes(total))
      .withContext('The counter label should include the total of refunds')
      .toBeTrue();
  });

  it('should have a counter label and not includes the total of refunds', done => {
    const { component, fixture } = setup({
      refund: {
        ...refundListMock,
        content: [],
        totalElements: 0,
        numberOfElements: 0,
        totalPages: 0,
      },
    });

    let total = 0;
    const counterLabel = fixture.debugElement.query(By.css('h5 > span'));

    component.refundsCounting$.subscribe(count => {
      total = count;
    });

    expect(counterLabel.nativeElement.textContent.includes(total))
      .withContext('The counter label should NOT include the total of refunds')
      .toBeFalse();

    done();
  });
  it('should have a table with 9 columns', () => {
    const { fixture } = setup();

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    expect(table).toBeDefined();

    const columHeaders = table.queryAll(
      By.directive(AplazoSimpleTableHeaderCellDirective)
    );

    expect(columHeaders.length).toEqual(9);
  });

  it('should show pagination', done => {
    const { component, fixture } = setup();
    let totalPages = 0;

    component.pagesByRefundsCounting$.subscribe(pages => {
      totalPages = pages;
    });

    const pagination = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );

    expect(totalPages)
      .withContext('The total pages should be greater than 0')
      .toBeGreaterThan(0);
    expect(pagination)
      .withContext('The page should show the pagination component')
      .toBeDefined();

    done();
  });

  it('should not show pagination', async () => {
    const { component, fixture } = setup({
      refund: {
        ...refundListMock,
        content: [],
        totalElements: 0,
        numberOfElements: 0,
        totalPages: 0,
      },
    });

    const totalPages = await lastValueFrom(
      component.pagesByRefundsCounting$.pipe(take(1))
    );

    const pagination = fixture.debugElement.query(
      By.directive(AplazoPaginationComponent)
    );

    expect(totalPages).withContext('The total pages should be 0').toEqual(0);
    expect(pagination)
      .withContext('The page should NOT show the pagination component')
      .toBeNull();
  });

  it('should show a message when there are no refunds', () => {
    const { fixture } = setup({
      refund: {
        ...refundListMock,
        content: [],
        totalElements: 0,
        numberOfElements: 0,
        totalPages: 0,
      },
    });

    const message = fixture.debugElement.query(
      By.directive(AplazoCommonMessageComponent)
    );

    expect(message).toBeDefined();
  });

  it('should update page on changePage', done => {
    const { component } = setup();

    const eventTrackSpy = spyOn(
      TestBed.inject(EventManagerService),
      'sendTrackEvent'
    ).and.callThrough();

    let result = 0;
    const newPage = 2;

    component.currentPage$.subscribe(page => {
      result = page;
    });

    component.changePage(newPage);

    expect(eventTrackSpy)
      .withContext('The event track should be called when the page changes')
      .toHaveBeenCalledTimes(1);
    expect(result)
      .withContext('The current page should be the same that the new page')
      .toBe(newPage);

    done();
  });

  // it('should update date range on changeDateRange', done => {
  //   const { component } = setup();

  //   let result: B2BDateRange | null = null;

  //   component.dateRange$.subscribe(range => {
  //     result = range;
  //   });

  //   expect(result).toEqual({
  //     endDate: component.todayRawDateDayFirst,
  //     startDate: component.sevenDaysAgoRawDateDayFirst,
  //   });

  //   component.setNewDateRange({
  //     formatted: ['16/04/2024', '17/04/2024'],
  //     date: [],
  //   });

  //   expect(result).toEqual({
  //     endDate: '17/04/2024',
  //     startDate: '16/04/2024',
  //   });

  //   done();
  // });

  it('should execute ReportUseCase on download', () => {
    const { component, reportUsecase } = setup();
    const reportUsecaseSpy = spyOn(reportUsecase, 'execute').and.callThrough();

    component.download();

    expect(reportUsecaseSpy).toHaveBeenCalledTimes(1);
  });
});
