import { TestBed } from '@angular/core/testing';
import { B2BDateRange, TemporalService } from '@aplazo/merchant/shared';
import {
  CriteriaRefundService,
  IRefundCriteriaDto,
} from '../../../../../app/features/refund/infra/services/criteria-refund.service';

describe('CriteriaRefundService', () => {
  let service: CriteriaRefundService;
  let temporal: TemporalService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [CriteriaRefundService, TemporalService],
    });

    service = TestBed.inject(CriteriaRefundService);
    temporal = TestBed.inject(TemporalService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(CriteriaRefundService);
  });

  it('should update the criteria refunds', done => {
    let result: IRefundCriteriaDto | undefined;

    service.getRefundCriteria$().subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(service.initialRefundCriteria);

    done();
  });

  it('should update the page number', done => {
    let result: number | undefined;

    service.getPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    service.setPageNum(1);

    expect(result).toBe(1);

    done();
  });

  it('should update the page size', done => {
    let result: number | undefined;

    service.getPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    service.setPageSize(20);

    expect(result).toBe(20);

    done();
  });

  it('should update the search value', done => {
    let result: string | undefined;

    service.getSearchValue$().subscribe(searchValue => {
      result = searchValue;
    });

    expect(result).toBe('');

    service.setSearchValue('search');

    expect(result).toBe('search');

    done();
  });

  it('should update the date range', done => {
    let result: B2BDateRange | undefined;

    service.getDateRange$().subscribe(dateRange => {
      result = dateRange;
    });

    expect(result).toEqual({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    service.setDateRange({
      startDate: temporal.todayRawDayFirst,
      endDate: temporal.todayRawDayFirst,
    });

    expect(result).toEqual({
      startDate: temporal.todayRawDayFirst,
      endDate: temporal.todayRawDayFirst,
    });

    done();
  });

  it('should update the status', done => {
    let result: string | undefined;

    service.getStatus$().subscribe(status => {
      result = status;
    });

    expect(result).toBe('refunded');

    service.setStatus('requested');

    expect(result).toBe('requested');

    done();
  });

  it('should clear the criteria', done => {
    let result: IRefundCriteriaDto | undefined;

    service.getRefundCriteria$().subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(service.initialRefundCriteria);

    service.setPageNum(1);
    service.setStatus('requested');

    expect(result).toEqual({
      ...service.initialRefundCriteria,
      status: 'requested',
    });

    service.clearCriteria();

    expect(result).toEqual(service.initialRefundCriteria);

    done();
  });

  it('should not update the page number if it is the same', done => {
    let result: number | undefined;

    service.getPageNum$().subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(0);

    service.setPageNum(0);

    expect(result).toBe(0);

    done();
  });

  it('should not update the page size if it is the same', done => {
    let result: number | undefined;

    service.getPageSize$().subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(10);

    service.setPageSize(10);

    expect(result).toBe(10);

    done();
  });

  it('should not update the search value if it is the same', done => {
    let result: string | undefined;

    service.getSearchValue$().subscribe(searchValue => {
      result = searchValue;
    });

    expect(result).toBe('');

    service.setSearchValue('');

    expect(result).toBe('');

    done();
  });

  it('should not update the date range if it is the same', done => {
    let result: B2BDateRange | undefined;

    service.getDateRange$().subscribe(dateRange => {
      result = dateRange;
    });

    expect(result).toEqual({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    service.setDateRange({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    expect(result).toEqual({
      startDate: temporal.sevenDaysAgo,
      endDate: temporal.todayRawDayFirst,
    });

    done();
  });

  it('should not update the status if it is the same', done => {
    let result: string | undefined;

    service.getStatus$().subscribe(status => {
      result = status;
    });

    expect(result).toBe('refunded');

    service.setStatus('refunded');

    expect(result).toBe('refunded');

    done();
  });
});
