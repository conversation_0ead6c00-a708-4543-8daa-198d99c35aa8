import { Component } from '@angular/core';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { By } from '@angular/platform-browser';
import {
  AplazoFormFieldComponent,
  AplazoFormInputDirective,
} from '@aplazo/shared-ui/forms';
import { AplazoIconRegistryService } from '@aplazo/shared-ui/icon';
import { SearchComponent } from '../../../../app/features/shared/components/search.component';

@Component({
  template: `
    <app-ui-search
      [formControl]="search"
      [minLength]="minLenght"></app-ui-search>
  `,
  imports: [SearchComponent, ReactiveFormsModule],
})
export class HostTestComponent {
  minLenght = 3;
  search = new FormControl<string>('', [Validators.minLength(this.minLenght)]);
}

describe('SearchComponent', () => {
  let fixture: ComponentFixture<HostTestComponent>;
  let hostComponent: HostTestComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [SearchComponent, ReactiveFormsModule, HostTestComponent],
      providers: [AplazoIconRegistryService],
    });

    fixture = TestBed.createComponent(HostTestComponent);
    hostComponent = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should have a search input and update value', () => {
    const searchInput = fixture.debugElement.query(
      By.directive(AplazoFormInputDirective)
    );

    searchInput.nativeElement.value = 'search value';
    searchInput.nativeElement.dispatchEvent(new Event('input'));

    fixture.detectChanges();

    expect(hostComponent.search.value).toBe('search value');
  });

  it('should show error message when search input has less than 3 characters', () => {
    const formField = fixture.debugElement.query(
      By.directive(AplazoFormFieldComponent)
    );
    const searchInput = formField.query(By.directive(AplazoFormInputDirective));

    searchInput.nativeElement.value = 'se';
    searchInput.nativeElement.dispatchEvent(new Event('input'));

    hostComponent.search.markAllAsTouched();

    fixture.detectChanges();

    const errorMessage = formField.query(By.css('p'));

    expect(errorMessage.nativeElement.textContent.trim()).toBe(
      'Para una búsqueda ingrese mínimo ' +
        hostComponent.minLenght +
        ' caracteres'
    );
  });

  it('should not show error message when search input has 3 characters', () => {
    const formField = fixture.debugElement.query(
      By.directive(AplazoFormFieldComponent)
    );
    const searchInput = formField.query(By.directive(AplazoFormInputDirective));

    searchInput.nativeElement.value = 'sea';
    searchInput.nativeElement.dispatchEvent(new Event('input'));

    hostComponent.search.markAllAsTouched();

    fixture.detectChanges();

    const errorMessage = formField.query(By.css('p'));

    expect(errorMessage).toBeNull();
  });

  it('should reset search input when click on clear button', fakeAsync(() => {
    const resetComponentSpy = spyOn(
      hostComponent.search,
      'reset'
    ).and.callThrough();

    const searchInput = fixture.debugElement.query(
      By.directive(AplazoFormInputDirective)
    );

    searchInput.nativeElement.value = 'search value';
    searchInput.nativeElement.dispatchEvent(new Event('input'));

    tick();
    fixture.detectChanges();

    hostComponent.search.markAllAsTouched();

    const clearButton = fixture.debugElement.query(
      By.css('button[aplzInputSuffix]')
    );

    clearButton.nativeElement.click();

    fixture.detectChanges();

    expect(hostComponent.search.value)
      .withContext('Search input should be reseted')
      .toBeNull();
    expect(resetComponentSpy).toHaveBeenCalledTimes(1);
  }));
});
