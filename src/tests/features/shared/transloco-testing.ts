import {
  TranslocoTestingModule,
  TranslocoTestingOptions,
} from '@jsverse/transloco';
import balance from '../../../assets/i18n/balance/es.json';
import clarifications from '../../../assets/i18n/clarifications/es.json';
import dashboard from '../../../assets/i18n/dashboard/es.json';
import refunds from '../../../assets/i18n/refunds/es.json';
import settlements from '../../../assets/i18n/settlements/es.json';

export function getTranslocoModule(options: TranslocoTestingOptions = {}) {
  return TranslocoTestingModule.forRoot({
    langs: {
      'clarifications/es': clarifications,
      'refunds/es': refunds,
      'settlements/es': settlements,
      'dashboard/es': dashboard,
      'balance/es': balance,
    },
    translocoConfig: {
      availableLangs: ['es'],
      defaultLang: 'es',
    },
    preloadLangs: true,
    ...options,
  });
}
