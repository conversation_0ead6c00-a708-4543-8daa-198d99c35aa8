import { IUserJwt, PluginIntegrations } from '@aplazo/merchant/shared';
import { IUserPersistenceDTO } from '../../../../../app/features/user/src/application/dtos/user-persistence.dto';
import { User<PERSON>apper } from '../../../../../app/features/user/src/application/mappers/user.mapper';
import { User } from '../../../../../app/features/user/src/domain/entities/user';
import { UserEmail } from '../../../../../app/features/user/src/domain/entities/user-email';
import users from '../../infra/local-users.db.json';

describe('UserMapper', () => {
  describe('loginToDomain', () => {
    const userTest = users[3];
    const userTestJwt: IUserJwt = {
      '2fa': userTest['2fa'],
      exp: userTest.exp,
      name: userTest.name ?? '',
      role: userTest.role,
      username: userTest.sub,
      finishReg: userTest.finishReg,
      integrationType: userTest.integrationType as PluginIntegrations,
      sessionId: userTest.sessionId,
      merchantId: userTest.merchantId,
    };

    it('should map to new user successfully without email', () => {
      const newUser = UserMapper.loginToDomain(
        userTestJwt.username ?? '',
        userTestJwt
      );

      expect(newUser).toBeInstanceOf(User);
      expect(newUser.email).toBeFalsy();
    });

    it('should map to new user successfully with email', () => {
      const newUser = UserMapper.loginToDomain(
        userTestJwt.username ?? '',
        userTestJwt,
        UserEmail.create('<EMAIL>')
      );

      expect(newUser).toBeInstanceOf(User);
      expect(newUser.email).toBe('<EMAIL>');
    });
  });

  describe('persistanceToDomain', () => {
    it('should map to new user without email', () => {
      const userPersistence: IUserPersistenceDTO = {
        accessToken: '123',
        email: '',
        isLoggedIn: true,
        lastLogin: new Date().toISOString(),
        merchantId: 8383,
        merchantName: 'Yara',
        role: 'ROLE_MERCHANT',
        username: '<EMAIL>',
      };

      const newUser = UserMapper.persistanceToDomain(userPersistence);

      expect(newUser).toBeInstanceOf(User);
      expect(newUser.email).toBeFalsy();
    });

    it('should map to new user with email', () => {
      const userPersistence: IUserPersistenceDTO = {
        accessToken: '123',
        email: '<EMAIL>',
        isLoggedIn: true,
        lastLogin: new Date().toISOString(),
        merchantId: 8383,
        merchantName: 'Yara',
        role: 'ROLE_MERCHANT',
        username: '<EMAIL>',
      };

      const newUser = UserMapper.persistanceToDomain(userPersistence);

      expect(newUser).toBeInstanceOf(User);
      expect(newUser.email).toBe('<EMAIL>');
    });
  });

  describe('domainToPersistence', () => {
    it('should map to IUserPersistenceDTO', () => {
      const user: User = User.create({
        username: '<EMAIL>',
        merchantId: 8383,
        merchantName: 'Yara',
        role: 'ROLE_MERCHANT',
      });

      const userPersistence = UserMapper.domainToPersistence(user);
      expect(userPersistence).toEqual({
        accessToken: '',
        email: '',
        isLoggedIn: false,
        lastLogin: '1970-01-01T00:00:00.000Z',
        merchantId: 8383,
        merchantName: 'Yara',
        role: 'ROLE_MERCHANT',
        username: '<EMAIL>',
      });
    });
  });
});
