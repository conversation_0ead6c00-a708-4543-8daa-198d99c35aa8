import { IUserSetNewPasswordDto } from '../../../../../app/features/user/src/application/dtos/set-new-password.dto';
import {
  emptyPasswordNewPasswordDefaultError,
  emptyTokenNewPasswordDefaultError,
  UserForgotPasswordMapper,
} from '../../../../../app/features/user/src/application/mappers/user-new-password.mapper';
import {
  invalidLengthPasswordErrorMessage,
  notLowercasePasswordErrorMessage,
  notNumberPasswordErrorMessage,
  notUppercasePasswordErrorMessage,
  notValidCharacterPasswordErrorMessage,
} from '../../../../../app/features/user/src/domain/entities/user-password';

describe('UserForgotPasswordMapper', () => {
  it('should throw RuntimeMerchantError when token is null or undefined', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: null,
      password: 'password',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(emptyTokenNewPasswordDefaultError);

    const argsTest2: IUserSetNewPasswordDto = {
      token: undefined,
      password: 'password',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest2)
    ).toThrowError(emptyTokenNewPasswordDefaultError);
  });

  it('should throw RuntimeMerchantError when password is null or undefined', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: null,
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(emptyPasswordNewPasswordDefaultError);

    const argsTest2: IUserSetNewPasswordDto = {
      token: 'token',
      password: undefined,
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest2)
    ).toThrowError(emptyPasswordNewPasswordDefaultError);
  });

  it('should throw RuntimeMerchantError when password does not have at least 8 characters', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'pass',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(invalidLengthPasswordErrorMessage);
  });

  it('should throw RuntimeMerchantError when password does not have at least one uppercase letter', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'password',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(notUppercasePasswordErrorMessage);
  });

  it('should throw RuntimeMerchantError when password does not have at least one lowercase letter', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'PASSWORD',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(notLowercasePasswordErrorMessage);
  });

  it('should throw RuntimeMerchantError when password does not have at least one number', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(notNumberPasswordErrorMessage);
  });

  it('should throw RuntimeMerchantError when password does not have at least one valid character', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1',
      newRoles: false,
    };

    expect(() =>
      UserForgotPasswordMapper.toNewRolesRepository(argsTest)
    ).toThrowError(notValidCharacterPasswordErrorMessage);
  });

  it('should return code and password', () => {
    const argsTest: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
      newRoles: false,
    };

    const result = UserForgotPasswordMapper.toNewRolesRepository(argsTest);

    expect(result).toEqual({
      code: 'token',
      password: 'Password1.',
    });
  });
});
