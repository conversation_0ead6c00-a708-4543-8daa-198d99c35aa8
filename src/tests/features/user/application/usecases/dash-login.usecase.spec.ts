import {
  IUserJwt,
  JwtDecoderService,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import {
  LocalJwtDecoder,
  LocalLoader,
  LocalNotifier,
} from '@aplazo/merchant/shared-testing';
import { Observable, isEmpty, lastValue<PERSON>rom, of } from 'rxjs';
import { DashUserPersistenceService } from '../../../../../app/features/user/src/application/services/local-persistence.service';
import { UserStoreService } from '../../../../../app/features/user/src/application/services/user-store.service';
import { DashUserLoginUseCase } from '../../../../../app/features/user/src/application/usecases/dash-login.usecase';
import { Credentials } from '../../../../../app/features/user/src/domain/entities/credentials';
import { User } from '../../../../../app/features/user/src/domain/entities/user';
import { UserRepository } from '../../../../../app/features/user/src/domain/repositories/user.repository';
import { DashUserSimpleStore } from '../../../../../app/features/user/src/infra/services/dash-user-simple-store.service';
import { LocalDashLoginRepository } from '../../infra/repositories/local-dash-login.repository';
import { LocalDashUserPersistenceService } from '../../infra/services/local-dash-user-persistence.service';

describe('DashLoginUseCase', () => {
  let usecase: DashUserLoginUseCase;
  let repository: UserRepository<Observable<string>>;
  let loader: LoaderService;
  let notifier: NotifierService;
  let persistenceService: DashUserPersistenceService;
  let jwtDecoder: JwtDecoderService<IUserJwt>;
  let store: UserStoreService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;

  beforeEach(() => {
    repository = new LocalDashLoginRepository();
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    jwtDecoder = new LocalJwtDecoder();
    store = new DashUserSimpleStore();
    persistenceService = new LocalDashUserPersistenceService();

    usecase = new DashUserLoginUseCase(
      repository,
      loader,
      notifier,
      persistenceService,
      jwtDecoder,
      store
    );

    loaderShowSpy = spyOn(loader, 'show');
    loaderHideSpy = spyOn(loader, 'hide');
    notifierWarningSpy = spyOn(notifier, 'warning');
  });

  it('should create an instance of DashLoginUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(DashUserLoginUseCase);
  });

  it('should return a user on successful login with credentials with email', async () => {
    const credentials: Credentials = {
      username: '<EMAIL>',
      password: '2323',
    };

    const controlUser = {
      username: '<EMAIL>',
      role: 'ROLE_MERCHANT',
      merchantId: 348,
      merchantName: 'Alice',
    };

    const user = await lastValueFrom(usecase.execute(credentials));

    expect(user).toBeTruthy();
    expect(user).toBeInstanceOf(User);
    expect(user.username).toBe(controlUser.username);
    expect(user.role).toBe(controlUser.role);
    expect(user.merchantId).toBe(controlUser.merchantId);
    expect(user.merchantName).toBe(controlUser.merchantName);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).not.toHaveBeenCalled();
  });

  it('should return a user on successful login with credentials without email', async () => {
    const credentials: Credentials = {
      username: 'emma-sell_agent_53',
      password: '2323',
    };

    const controlUser = {
      username: 'emma-sell_agent_53',
      role: 'ROLE_PANEL_SUPPORT',
      merchantId: 529,
      merchantName: 'Emma Collins',
    };

    const user = await lastValueFrom(usecase.execute(credentials));

    expect(user).toBeTruthy();
    expect(user).toBeInstanceOf(User);
    expect(user.username).toBe(controlUser.username);
    expect(user.role).toBe(controlUser.role);
    expect(user.merchantId).toBe(controlUser.merchantId);
    expect(user.merchantName).toBe(controlUser.merchantName);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).not.toHaveBeenCalled();
  });

  it('should finish usecase with complete and no emission when the token response is empty when login with credentials with email', async () => {
    const repoSpy = spyOn(repository, 'loginWithEmail').and.returnValue(of(''));

    const credentials: Credentials = {
      username: '<EMAIL>',
      password: '2323',
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(credentials).pipe(isEmpty())
    );

    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(isCompleteWithNoEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: '¡Ups! Parece que ha ocurrido un error',
      message: 'Verifique su usuario / contraseña',
    });
  });

  it('should finish with complete and no emission when the role is not valid for login with credentials with email', async () => {
    const credentials: Credentials = {
      username: '<EMAIL>',
      password: '2323',
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(credentials).pipe(isEmpty())
    );

    expect(isCompleteWithNoEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: '¡Ups! Parece que ha ocurrido un error',
      message: 'El usuario no tiene privilegios para iniciar sesión',
    });
  });

  it('should finish with complete and no emission when the persistence service throws error', async () => {
    const persistenceSpy = spyOn(
      persistenceService,
      'saveAuthenticatedUser'
    ).and.throwError('Deliberate error');

    const credentials: Credentials = {
      username: '<EMAIL>',
      password: '2323',
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(credentials).pipe(isEmpty())
    );

    expect(persistenceSpy).toHaveBeenCalledTimes(1);
    expect(isCompleteWithNoEmission).toBeTrue();
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: '¡Ups! Parece que ha ocurrido un error',
      message: 'No se pudo guardar la información del usuario',
    });
  });
});
