import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, throwError } from 'rxjs';
import { UserForgotPasswordNewRolesDto } from '../../../../../app/features/user/src/application/dtos/forgot-password-v2.dto';
import { ForgotPasswordUseCase } from '../../../../../app/features/user/src/application/usecases/forgot-password.usecase';
import { UserForgotPasswordResetRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgot-password-v2.repository';
import { UserForgotPasswordRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgot-password.repository';
import { LocalForgotOldRepository } from '../../infra/repositories/local-forgot-old.repository';
import { LocalForgotV2Repository } from '../../infra/repositories/local-forgot-v2.repository';

describe('UserRefreshLoginUseCase', () => {
  let usecase: ForgotPasswordUseCase;

  let repository: UserForgotPasswordRepository<FormData, Observable<void>>;
  let repositoryV2: UserForgotPasswordResetRepository<
    string,
    Observable<UserForgotPasswordNewRolesDto>
  >;
  let loader: LoaderService;
  let notifier: NotifierService;
  let useCaseErrorHandler: UseCaseErrorHandler;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierSuccessSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;

  beforeEach(() => {
    repository = new LocalForgotOldRepository();
    repositoryV2 = new LocalForgotV2Repository();
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    useCaseErrorHandler = new LocalUsecaseErrorHandler();
    usecase = new ForgotPasswordUseCase(
      repository,
      repositoryV2,
      loader,
      notifier,
      useCaseErrorHandler
    );

    loaderShowSpy = spyOn(loader, 'show');
    loaderHideSpy = spyOn(loader, 'hide');
    notifierSuccessSpy = spyOn(notifier, 'success');
    notifierWarningSpy = spyOn(notifier, 'warning');
  });

  it('should create an instance of UserRefreshLoginUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(ForgotPasswordUseCase);
  });

  it('should execute successfully', async () => {
    const repositorySpy = spyOn(repository, 'requestReset').and.callThrough();
    const repositoryV2Spy = spyOn(repositoryV2, 'reset').and.callThrough();

    await lastValueFrom(
      usecase.execute({
        email: '<EMAIL>',
      })
    );

    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(repositoryV2Spy).toHaveBeenCalledTimes(1);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'El enlace ha sido enviado',
      message:
        'Verifique su buzón de correo electrónico, debería recibir un enlace de reinicio que será válido 5 minutos, en caso contrario, solicite nuevamente la recuperación de contraseña.',
    });
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should emulate success when the old repository response with error', async () => {
    const repositorySpy = spyOn(repository, 'requestReset').and.returnValue(
      throwError(() => new Error('Deliberate error'))
    );
    const repositoryV2Spy = spyOn(repositoryV2, 'reset').and.callThrough();

    await lastValueFrom(
      usecase.execute({
        email: '<EMAIL>',
      })
    );
    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(repositoryV2Spy).toHaveBeenCalledTimes(1);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'El enlace ha sido enviado',
      message:
        'Verifique su buzón de correo electrónico, debería recibir un enlace de reinicio que será válido 5 minutos, en caso contrario, solicite nuevamente la recuperación de contraseña.',
    });
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should emulate success when the v2 repository response with error', async () => {
    const repositorySpy = spyOn(repository, 'requestReset').and.callThrough();
    const repositoryV2Spy = spyOn(repositoryV2, 'reset').and.returnValue(
      throwError(() => new Error('Deliberate error'))
    );

    await lastValueFrom(
      usecase.execute({
        email: '<EMAIL>',
      })
    );

    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(repositoryV2Spy).toHaveBeenCalledTimes(1);

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'El enlace ha sido enviado',
      message:
        'Verifique su buzón de correo electrónico, debería recibir un enlace de reinicio que será válido 5 minutos, en caso contrario, solicite nuevamente la recuperación de contraseña.',
    });
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });
});
