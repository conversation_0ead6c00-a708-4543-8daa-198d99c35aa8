import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, throwError } from 'rxjs';
import { IUserForgottenPasswordVerificationTokenDto } from '../../../../../app/features/user/src/application/dtos/verification-token.dto';
import { UserForgottenPasswordVerificationTokenUseCase } from '../../../../../app/features/user/src/application/usecases/forgotten-password-verification-token.usecase';
import { UserForgottenPasswordVerificationTokenRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgotten-password-verification-token.repository';
import { LocalForgottenPasswordRepository } from '../../infra/repositories/local-forgotten-password.repository';

describe('UserForgottenPasswordVerificationUseCase', () => {
  let usecase: UserForgottenPasswordVerificationTokenUseCase;
  let repository: UserForgottenPasswordVerificationTokenRepository<
    IUserForgottenPasswordVerificationTokenDto,
    Observable<{ status: boolean }>
  >;
  let loader: LoaderService;
  let notifier: NotifierService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;
  let notifierSuccessSpy: jasmine.Spy;

  beforeEach(() => {
    repository = new LocalForgottenPasswordRepository();
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    usecase = new UserForgottenPasswordVerificationTokenUseCase(
      repository,
      loader,
      notifier
    );

    loaderShowSpy = spyOn(loader, 'show');
    loaderHideSpy = spyOn(loader, 'hide');
    notifierWarningSpy = spyOn(notifier, 'warning');
    notifierSuccessSpy = spyOn(notifier, 'success');
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(
      UserForgottenPasswordVerificationTokenUseCase
    );
  });

  it('should execute successfully', async () => {
    const repositorySpy = spyOn(repository, 'validateToken').and.callThrough();

    const result = await lastValueFrom(usecase.execute({ token: 'token' }));

    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(result).toEqual({ status: true });
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'Correo verificado',
    });
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should not call repository execution when token is empty', async () => {
    const repositorySpy = spyOn(repository, 'validateToken').and.callThrough();
    const result = await lastValueFrom(usecase.execute({ token: '' }));

    expect(repositorySpy).toHaveBeenCalledTimes(0);
    expect(result).toEqual({ status: false });
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'Enlace no válido',
    });
  });

  it('should return status false when repository fails', async () => {
    const repositorySpy = spyOn(repository, 'validateToken').and.returnValue(
      throwError(() => new Error('Deliberate error'))
    );
    const result = await lastValueFrom(usecase.execute({ token: 'token' }));

    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(result).toEqual({ status: false });
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'Ups',
      message: 'Deliberate error',
    });
  });
});
