import { RedirectionService } from '@aplazo/merchant/shared';
import { LocalRedirecter } from '@aplazo/merchant/shared-testing';
import { lastValueFrom } from 'rxjs';
import { DashUserPersistenceService } from '../../../../../app/features/user/src/application/services/local-persistence.service';
import { UserStoreService } from '../../../../../app/features/user/src/application/services/user-store.service';
import { UserLogoutUseCase } from '../../../../../app/features/user/src/application/usecases/logout.usecase';
import { DashUserSimpleStore } from '../../../../../app/features/user/src/infra/services/dash-user-simple-store.service';
import { LocalDashUserPersistenceService } from '../../infra/services/local-dash-user-persistence.service';

describe('UserLogoutUseCase', () => {
  let usecase: UserLogoutUseCase;
  let persistence: DashUserPersistenceService;
  let store: UserStoreService;
  let redirecter: RedirectionService;

  beforeEach(() => {
    persistence = new LocalDashUserPersistenceService();
    store = new DashUserSimpleStore();
    redirecter = new LocalRedirecter();
    usecase = new UserLogoutUseCase(persistence, store, redirecter);
  });

  it('should create an instance of UserLogoutUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UserLogoutUseCase);
  });

  it('should execute successfully', async () => {
    const persistenceSpy = spyOn(
      persistence,
      'deAuthenticateUser'
    ).and.callThrough();
    const storeSpy = spyOn(store, 'clearUserStore').and.callThrough();
    const redirecterSpy = spyOn(
      redirecter,
      'internalNavigation'
    ).and.callThrough();

    await lastValueFrom(usecase.execute('/'));

    expect(persistenceSpy).toHaveBeenCalledTimes(1);
    expect(storeSpy).toHaveBeenCalledTimes(1);
    expect(redirecterSpy).toHaveBeenCalledTimes(1);
  });
});
