import { TestBed } from '@angular/core/testing';
import { lastValueFrom } from 'rxjs';
import { UserMapper } from '../../../../../app/features/user/src/application/mappers/user.mapper';
import { DashUserPersistenceService } from '../../../../../app/features/user/src/application/services/local-persistence.service';
import { UserStoreService } from '../../../../../app/features/user/src/application/services/user-store.service';
import { UserRefreshLoginUseCase } from '../../../../../app/features/user/src/application/usecases/refresh-login.usecase';
import { DashUserSimpleStore } from '../../../../../app/features/user/src/infra/services/dash-user-simple-store.service';
import { LocalDashUserPersistenceService } from '../../infra/services/local-dash-user-persistence.service';

describe('UserRefreshLoginUseCase', () => {
  let usecase: UserRefreshLoginUseCase;
  let persistence: DashUserPersistenceService;
  let store: UserStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        UserRefreshLoginUseCase,
        {
          provide: DashUserPersistenceService,
          useClass: LocalDashUserPersistenceService,
        },
        {
          provide: UserStoreService,
          useClass: DashUserSimpleStore,
        },
      ],
    });

    persistence = TestBed.inject(DashUserPersistenceService);
    store = TestBed.inject(UserStoreService);
    usecase = TestBed.inject(UserRefreshLoginUseCase);
  });

  it('should create an instance of UserRefreshLoginUseCase', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UserRefreshLoginUseCase);
  });

  it('should execute successfully and return a user', async () => {
    const persistenceSpy = spyOn(
      persistence,
      'getAuthenticatedUser'
    ).and.callThrough();
    const storeSpy = spyOn(store, 'setUser').and.callThrough();

    const controlUser = {
      merchantId: 348,
      email: '<EMAIL>',
      role: 'ROLE_MERCHANT',
      merchantName: 'Alice',
      isLoggedIn: true,
    };

    const result = await lastValueFrom(usecase.execute());

    expect(persistenceSpy).toHaveBeenCalledTimes(1);
    expect(storeSpy).toHaveBeenCalledTimes(1);
    expect(result.email).toBe(controlUser.email);
    expect(result.role).toBe(controlUser.role);
    expect(result.merchantName).toBe(controlUser.merchantName);
    expect(result.merchantId).toBe(controlUser.merchantId);
    expect(result.isLoggedIn).toBe(controlUser.isLoggedIn);
  });

  it('should execute successfully and return null', async () => {
    const persistenceSpy = spyOn(
      persistence,
      'getAuthenticatedUser'
    ).and.returnValue(null);
    const storeSpy = spyOn(store, 'setUser').and.callThrough();
    const mapperSpy = spyOn(
      UserMapper,
      'persistanceToDomain'
    ).and.callThrough();

    const result = await lastValueFrom(usecase.execute());

    expect(persistenceSpy).toHaveBeenCalledTimes(1);
    expect(mapperSpy)
      .withContext('UserMapper.persistanceToDomain should not have been called')
      .toHaveBeenCalledTimes(0);
    expect(storeSpy).toHaveBeenCalledTimes(0);
    expect(result).toBeNull();
  });

  it('should handle error and return null', async () => {
    const persistenceSpy = spyOn(
      persistence,
      'getAuthenticatedUser'
    ).and.throwError('Deliberate error');

    const storeSpy = spyOn(store, 'setFailure').and.callThrough();

    const result = await lastValueFrom(usecase.execute());

    expect(persistenceSpy).toHaveBeenCalledTimes(1);
    expect(storeSpy).toHaveBeenCalledTimes(1);
    expect(result).toBeNull();
  });
});
