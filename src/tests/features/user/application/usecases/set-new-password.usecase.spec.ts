import { HttpErrorResponse } from '@angular/common/http';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, Observable, throwError } from 'rxjs';
import { NewRoleSetPasswordDto } from '../../../../../app/features/user/src/application/dtos/new-role-set-password.dto';
import { IUserSetNewPasswordDto } from '../../../../../app/features/user/src/application/dtos/set-new-password.dto';
import {
  expiredCodeErrorMessage,
  setNewPasswordDefaultControlledErrorTitle,
  setNewPasswordDefaultUncontrolledErrorTitle,
  UserSetNewPasswordUseCase,
} from '../../../../../app/features/user/src/application/usecases/set-new-password.usecase';
import {
  UserNewRolesSetNewPasswordRepository,
  UserSetNewPasswordRepository,
} from '../../../../../app/features/user/src/domain/repositories/user-set-new-password.repository';
import { LocalSetNewPasswordV2Repository } from '../../infra/repositories/local-set-new-password-v2.repository';
import { LocalSetNewPasswordRepository } from '../../infra/repositories/local-set-new-password.repository';

describe('UserSetNewPasswordUseCase', () => {
  let usecase: UserSetNewPasswordUseCase;
  let repoOld: UserSetNewPasswordRepository<FormData, Observable<void>>;
  let repoV2: UserNewRolesSetNewPasswordRepository<
    { code: string; password: string },
    Observable<NewRoleSetPasswordDto>
  >;
  let loader: LoaderService;
  let notifier: NotifierService;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;
  let notifierSuccessSpy: jasmine.Spy;

  beforeEach(() => {
    repoOld = new LocalSetNewPasswordRepository();
    repoV2 = new LocalSetNewPasswordV2Repository();
    loader = new LocalLoader();
    notifier = new LocalNotifier();
    usecase = new UserSetNewPasswordUseCase(repoOld, repoV2, loader, notifier);

    loaderShowSpy = spyOn(loader, 'show');
    loaderHideSpy = spyOn(loader, 'hide');
    notifierWarningSpy = spyOn(notifier, 'warning');
    notifierSuccessSpy = spyOn(notifier, 'success');
  });

  it('should be created', () => {
    expect(usecase).toBeDefined();
    expect(usecase).toBeInstanceOf(UserSetNewPasswordUseCase);
  });

  it('should execute successfully setPassword with old repository and shows success notification', async () => {
    const request: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
    };

    await lastValueFrom(usecase.execute(request));

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'Contraseña guardada correctamente',
    });
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should execute successfully setPassword with v2 repository and shows success notification', async () => {
    const request: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
      newRoles: true,
    };

    await lastValueFrom(usecase.execute(request));

    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledWith({
      title: 'Contraseña guardada correctamente',
    });
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should finish usecase with complete and no emission when old repository fails with 400', async () => {
    const repoSpy = spyOn(repoOld, 'setPassword').and.returnValue(
      throwError(
        () => new HttpErrorResponse({ error: 'Deliberate error', status: 400 })
      )
    );

    const request: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isCompleteWithNoEmission).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: setNewPasswordDefaultControlledErrorTitle,
      message: expiredCodeErrorMessage,
    });
  });

  it('should finish usecase with complete and no emission when v2 repository fails with 400', async () => {
    const repoSpy = spyOn(repoV2, 'setPassword').and.returnValue(
      throwError(
        () => new HttpErrorResponse({ error: 'Deliberate error', status: 400 })
      )
    );

    const request: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
      newRoles: true,
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isCompleteWithNoEmission).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: setNewPasswordDefaultControlledErrorTitle,
      message: expiredCodeErrorMessage,
    });
  });

  it('should finish usecase with complete and no emission when old repository fails with error code different to 400', async () => {
    const repoSpy = spyOn(repoOld, 'setPassword').and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: { message: 'Deliberate error' },
            status: 500,
            url: '/test',
            statusText: 'Error',
          })
      )
    );

    const request: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isCompleteWithNoEmission).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: setNewPasswordDefaultUncontrolledErrorTitle,
      message: 'Deliberate error',
    });
  });

  it('should finish usecase with complete and no emission when v2 repository fails with error code different to 400', async () => {
    const repoSpy = spyOn(repoV2, 'setPassword').and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: { message: 'Deliberate error' },
            status: 500,
            url: '/test',
            statusText: 'Error',
          })
      )
    );

    const request: IUserSetNewPasswordDto = {
      token: 'token',
      password: 'Password1.',
      newRoles: true,
    };

    const isCompleteWithNoEmission = await lastValueFrom(
      usecase.execute(request).pipe(isEmpty())
    );

    expect(isCompleteWithNoEmission).toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierSuccessSpy).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: setNewPasswordDefaultUncontrolledErrorTitle,
      message: 'Deliberate error',
    });
  });
});
