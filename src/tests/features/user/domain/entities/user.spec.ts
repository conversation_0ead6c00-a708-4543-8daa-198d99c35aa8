import { User } from '../../../../../app/features/user/src/domain/entities/user';

describe('User', () => {
  it('should create a new instance', () => {
    const result = User.create({
      merchantId: 123,
      username: 'username',
      role: 'role',
      merchantName: 'merchantName',
    });

    expect(result).toBeInstanceOf(User);
  });

  it('should throw an error when merchantId is nullish', () => {
    expect(() => {
      User.create(
        // @ts-expect-error: testing nullish value
        {
          username: 'username',
          role: 'role',
          merchantName: 'merchantName',
        }
      );
    })
      .withContext('merchantId is nullish and should throw an error')
      .toThrowError();
  });

  it('should throw an error when username is nullish', () => {
    expect(() => {
      User.create(
        // @ts-expect-error: testing nullish value
        {
          merchantId: 123,
          role: 'role',
          merchantName: 'merchantName',
        }
      );
    })
      .withContext('username is nullish and should throw an error')
      .toThrowError();
  });

  it('should throw an error when role is nullish', () => {
    expect(() => {
      User.create(
        // @ts-expect-error: testing nullish value
        {
          merchantId: 123,
          username: 'username',
          merchantName: 'merchantName',
        }
      );
    })
      .withContext('role is nullish and should throw an error')
      .toThrowError();
  });

  it('should throw an error when merchantName is nullish', () => {
    expect(() => {
      User.create(
        // @ts-expect-error: testing nullish value
        {
          merchantId: 123,
          username: 'username',
          role: 'role',
        }
      );
    })
      .withContext('merchantName is nullish and should throw an error')
      .toThrowError();
  });

  it('should throw an error when any value is nullish', () => {
    expect(() => {
      User.create({
        merchantId: 123,
        username: 'username',
        role: 'role',
        merchantName: null,
      });
    })
      .withContext('any value is nullish and should throw an error')
      .toThrowError();
  });

  it('should throw an error when any value is empty', () => {
    expect(() => {
      User.create({
        merchantId: 123,
        username: 'username',
        role: 'role',
        merchantName: '',
      });
    })
      .withContext('any value is empty and should throw an error')
      .toThrowError();
  });
});
