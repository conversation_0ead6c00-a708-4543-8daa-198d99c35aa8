import { VALID_MERCHANT_ROLES } from 'src/app/features/user/src/domain/entities/valid-roles';

describe('ValidRoles', () => {
  it('should be an array of strings', () => {
    expect(Array.isArray(VALID_MERCHANT_ROLES))
      .withContext('VALID_MERCHANT_ROLES should be an array')
      .toBeTrue();
  });

  it('should contain the role ROLE_MERCHANT', () => {
    expect(VALID_MERCHANT_ROLES)
      .withContext('VALID_MERCHANT_ROLES should contain ROLE_MERCHANT')
      .toContain('ROLE_MERCHANT');
  });

  it('should contain the role ROLE_PANEL_ADMIN', () => {
    expect(VALID_MERCHANT_ROLES)
      .withContext('VALID_MERCHANT_ROLES should contain ROLE_PANEL_ADMIN')
      .toContain('ROLE_PANEL_ADMIN');
  });

  it('should contain the role ROLE_PANEL_SUPPORT', () => {
    expect(VALID_MERCHANT_ROLES)
      .withContext('VALID_MERCHANT_ROLES should contain ROLE_PANEL_SUPPORT')
      .toContain('ROLE_PANEL_SUPPORT');
  });

  it('should contain the role ROLE_PANEL_FINANCE', () => {
    expect(VALID_MERCHANT_ROLES)
      .withContext('VALID_MERCHANT_ROLES should contain ROLE_PANEL_FINANCE')
      .toContain('ROLE_PANEL_FINANCE');
  });

  it('should contain the role ROLE_PANEL_MARKETING', () => {
    expect(VALID_MERCHANT_ROLES)
      .withContext('VALID_MERCHANT_ROLES should contain ROLE_PANEL_MARKETING')
      .toContain('ROLE_PANEL_MARKETING');
  });

  it('should contain the role ROLE_ADMIN_INC', () => {
    expect(VALID_MERCHANT_ROLES)
      .withContext('VALID_MERCHANT_ROLES should contain ROLE_ADMIN_INC')
      .toContain('ROLE_ADMIN_INC');
  });
});
