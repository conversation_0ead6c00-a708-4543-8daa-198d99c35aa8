import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Observable } from 'rxjs';
import { provideCoreEnvironment } from '../../../../../app/config/merchant-core.environment';
import { UserForgotPasswordNewRolesDto } from '../../../../../app/features/user/src/application/dtos/forgot-password-v2.dto';
import { NewRoleSetPasswordDto } from '../../../../../app/features/user/src/application/dtos/new-role-set-password.dto';
import { IUserForgottenPasswordVerificationTokenDto } from '../../../../../app/features/user/src/application/dtos/verification-token.dto';
import { DashUserPersistenceService } from '../../../../../app/features/user/src/application/services/local-persistence.service';
import { UserStoreService } from '../../../../../app/features/user/src/application/services/user-store.service';
import { UserForgotPasswordResetRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgot-password-v2.repository';
import { UserForgotPasswordRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgot-password.repository';
import { UserForgottenPasswordVerificationTokenRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgotten-password-verification-token.repository';
import {
  UserNewRolesSetNewPasswordRepository,
  UserSetNewPasswordRepository,
} from '../../../../../app/features/user/src/domain/repositories/user-set-new-password.repository';
import { UserRepository } from '../../../../../app/features/user/src/domain/repositories/user.repository';
import {
  provideUserRepositories,
  provideUserServices,
} from '../../../../../app/features/user/src/infra/config/providers';
import { UserForgotPasswordRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user-forgot-password-impl.repository';
import { UserForgotPasswordResetV2WithHttp } from '../../../../../app/features/user/src/infra/repositories/user-forgot-password-v2.repository';
import { UserForgottenPasswordVerificationTokenRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user-forgotten-password-verification-token.repository';
import { UserNewRolesSetNewPasswordWithHttp } from '../../../../../app/features/user/src/infra/repositories/user-new-roles-set-password-with-http.repository';
import { UserSetNewPasswordRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user-set-new-password.repository';
import { UserRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user.repository';
import { DashUserSimpleStore } from '../../../../../app/features/user/src/infra/services/dash-user-simple-store.service';
import { DashPersistenceWithNativeStorage } from '../../../../../app/features/user/src/infra/services/persistence-implementation.service';

describe('user config providers', () => {
  describe('provideUserRepositories', () => {
    let userRepository: UserRepository<Observable<string>>;
    let forgotPasswordRepository: UserForgotPasswordRepository<
      FormData,
      Observable<void>
    >;
    let forgottenPasswordRepository: UserForgottenPasswordVerificationTokenRepository<
      IUserForgottenPasswordVerificationTokenDto,
      Observable<{ status: boolean }>
    >;
    let setNewPasswordRepository: UserSetNewPasswordRepository<
      FormData,
      Observable<void>
    >;
    let forgotPasswordResetRepository: UserForgotPasswordResetRepository<
      string,
      Observable<UserForgotPasswordNewRolesDto>
    >;
    let newRolesSetNewPasswordRepository: UserNewRolesSetNewPasswordRepository<
      { code: string; password: string },
      Observable<NewRoleSetPasswordDto>
    >;

    beforeEach(() => {
      TestBed.configureTestingModule({
        providers: [
          provideHttpClient(),
          provideHttpClientTesting(),
          provideUserRepositories(),
          provideCoreEnvironment(),
        ],
      });

      userRepository = TestBed.inject(UserRepository);
      forgotPasswordRepository = TestBed.inject(UserForgotPasswordRepository);
      forgottenPasswordRepository = TestBed.inject(
        UserForgottenPasswordVerificationTokenRepository
      );
      setNewPasswordRepository = TestBed.inject(UserSetNewPasswordRepository);
      forgotPasswordResetRepository = TestBed.inject(
        UserForgotPasswordResetRepository
      );
      newRolesSetNewPasswordRepository = TestBed.inject(
        UserNewRolesSetNewPasswordRepository
      );
    });

    it('should have a UserRepository implementation', () => {
      expect(userRepository).toBeInstanceOf(UserRepositoryImpl);
    });

    it('should have a UserForgotPasswordRepository implementation', () => {
      expect(forgotPasswordRepository).toBeInstanceOf(
        UserForgotPasswordRepositoryImpl
      );
    });

    it('should have a UserForgottenPasswordVerificationTokenRepository implementation', () => {
      expect(forgottenPasswordRepository).toBeInstanceOf(
        UserForgottenPasswordVerificationTokenRepositoryImpl
      );
    });

    it('should have a UserSetNewPasswordRepository implementation', () => {
      expect(setNewPasswordRepository).toBeInstanceOf(
        UserSetNewPasswordRepositoryImpl
      );
    });

    it('should have a UserForgotPasswordResetRepository implementation', () => {
      expect(forgotPasswordResetRepository).toBeInstanceOf(
        UserForgotPasswordResetV2WithHttp
      );
    });

    it('should have a UserNewRolesSetNewPasswordRepository implementation', () => {
      expect(newRolesSetNewPasswordRepository).toBeInstanceOf(
        UserNewRolesSetNewPasswordWithHttp
      );
    });
  });

  describe('provideUserServices', () => {
    let userStoreService: UserStoreService;
    let dashUserPersistenceService: DashUserPersistenceService;

    beforeEach(() => {
      TestBed.configureTestingModule({
        providers: [provideUserServices()],
      });

      userStoreService = TestBed.inject(UserStoreService);
      dashUserPersistenceService = TestBed.inject(DashUserPersistenceService);
    });

    it('should have a UserStoreService implementation', () => {
      expect(userStoreService).toBeInstanceOf(DashUserSimpleStore);
    });

    it('should have a DashUserPersistenceService implementation', () => {
      expect(dashUserPersistenceService).toBeInstanceOf(
        DashPersistenceWithNativeStorage
      );
    });
  });
});
