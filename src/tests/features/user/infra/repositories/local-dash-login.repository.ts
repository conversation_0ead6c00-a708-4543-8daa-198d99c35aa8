import { Observable, of } from 'rxjs';
import { Credentials } from '../../../../../app/features/user/src/domain/entities/credentials';
import { UserRepository } from '../../../../../app/features/user/src/domain/repositories/user.repository';
import users from '../local-users.db.json';

export class LocalDashLoginRepository
  implements UserRepository<Observable<string>>
{
  readonly #users = users;

  loginWithoutEmail(credentials: Credentials): Observable<string> {
    return of(JSON.stringify(this.#findUserByCredentials(credentials)));
  }

  loginWithEmail(credentials: Credentials): Observable<string> {
    return of(JSON.stringify(this.#findUserByCredentials(credentials)));
  }

  #findUserByCredentials(credentials: Credentials) {
    const user = this.#users.find(
      item =>
        item.sub === credentials.username &&
        item.password === credentials.password
    );

    if (!user) {
      throw new Error('Usuario no encontrado');
    }

    return user;
  }
}
