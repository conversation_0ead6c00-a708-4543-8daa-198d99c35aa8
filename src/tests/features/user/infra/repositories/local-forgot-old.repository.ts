import { Observable, of } from 'rxjs';
import { UserForgotPasswordRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgot-password.repository';

export class LocalForgotOldRepository
  implements UserForgotPasswordRepository<FormData, Observable<void>>
{
  requestReset(args: FormData): Observable<void> {
    console.log('LocalForgotOldRepository::requestReset', args);
    return of(undefined);
  }
}
