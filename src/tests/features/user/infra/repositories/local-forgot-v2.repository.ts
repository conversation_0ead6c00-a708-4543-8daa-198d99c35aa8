import { Observable, of } from 'rxjs';
import { UserForgotPasswordNewRolesDto } from '../../../../../app/features/user/src/application/dtos/forgot-password-v2.dto';
import { UserForgotPasswordResetRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgot-password-v2.repository';
export class LocalForgotV2Repository
  implements
    UserForgotPasswordResetRepository<
      string,
      Observable<UserForgotPasswordNewRolesDto>
    >
{
  reset(args: string): Observable<UserForgotPasswordNewRolesDto> {
    console.log('LocalForgotV2Repository::reset', args);
    return of({ content: { success: true }, error: null, code: 200 });
  }
}
