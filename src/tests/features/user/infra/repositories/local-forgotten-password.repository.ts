import { Observable, of } from 'rxjs';
import { IUserForgottenPasswordVerificationTokenDto } from '../../../../../app/features/user/src/application/dtos/verification-token.dto';
import { UserForgottenPasswordVerificationTokenRepository } from '../../../../../app/features/user/src/domain/repositories/user-forgotten-password-verification-token.repository';
export class LocalForgottenPasswordRepository
  implements
    UserForgottenPasswordVerificationTokenRepository<
      IUserForgottenPasswordVerificationTokenDto,
      Observable<{ status: boolean }>
    >
{
  validateToken(
    args: IUserForgottenPasswordVerificationTokenDto
  ): Observable<{ status: boolean }> {
    return of({ status: Boolean(args.token) });
  }
}
