import { Observable, of } from 'rxjs';
import users from '../local-users.db.json';
import { LoginCentralizedRepository } from 'src/app/features/user/src/domain/repositories/login-centralized.repository';
import { CredentialsLoginCentralized } from 'src/app/features/user/src/domain/entities/credentials-login-centralized';

export class LocalLoginCentralizedRepository
  implements LoginCentralizedRepository<Observable<string>>
{
  execute(credentials: CredentialsLoginCentralized): Observable<string> {
    return of(JSON.stringify(this.#findUserByCredentials(credentials)));
  }

  readonly #users = users;

  #findUserByCredentials(credentials: CredentialsLoginCentralized) {
    const user = this.#users.find(
      item =>
        item.sub === credentials.merchantUsername &&
        item.password === credentials.merchantPassword
    );

    if (!user) {
      throw new Error('Usuario no encontrado');
    }

    return user;
  }
}
