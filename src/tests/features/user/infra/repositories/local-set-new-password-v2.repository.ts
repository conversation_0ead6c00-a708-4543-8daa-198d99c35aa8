import { Observable, of } from 'rxjs';
import { NewRoleSetPasswordDto } from '../../../../../app/features/user/src/application/dtos/new-role-set-password.dto';
import { UserNewRolesSetNewPasswordRepository } from '../../../../../app/features/user/src/domain/repositories/user-set-new-password.repository';

export class LocalSetNewPasswordV2Repository
  implements
    UserNewRolesSetNewPasswordRepository<
      { code: string; password: string },
      Observable<NewRoleSetPasswordDto>
    >
{
  setPassword(args: {
    code: string;
    password: string;
  }): Observable<NewRoleSetPasswordDto> {
    console.log('LocalSetNewPasswordV2Repository::setPassword', args);

    return of({
      content: {
        success: true,
      },
      error: null,
      code: 200,
    });
  }
}
