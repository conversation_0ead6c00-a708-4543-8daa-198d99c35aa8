import { Observable, of } from 'rxjs';
import { UserSetNewPasswordRepository } from '../../../../../app/features/user/src/domain/repositories/user-set-new-password.repository';

export class LocalSetNewPasswordRepository
  implements UserSetNewPasswordRepository<FormData, Observable<void>>
{
  setPassword(data: FormData): Observable<void> {
    console.log('LocalSetNewPasswordRepository::setPassword', data);
    return of(undefined);
  }
}
