import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { UserForgotPasswordRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user-forgot-password-impl.repository';

describe('UserForgotPasswordRepositoryImpl', () => {
  let httpTestingController: HttpTestingController;
  let service: UserForgotPasswordRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMerchantAccessBaseUrl: 'https://merchant-acs.aplazo.net/api',
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        UserForgotPasswordRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(UserForgotPasswordRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(UserForgotPasswordRepositoryImpl);
  });

  it('should execute successfully', () => {
    const request: FormData = new FormData();
    request.append('email', '<EMAIL>');

    service.requestReset(request).subscribe({
      next: response => {
        expect(response).toBeFalsy();
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/forgot-password'
    );

    expect(req.request.method).toEqual('POST');

    req.flush(null);
  });

  it('should throw error when requestReset throw error', () => {
    const request: FormData = new FormData();
    request.append('email', '<EMAIL>');

    service.requestReset(request).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/forgot-password'
    );
    expect(req.request.method).toEqual('POST');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
