import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { UserForgotPasswordNewRolesDto } from '../../../../../app/features/user/src/application/dtos/forgot-password-v2.dto';
import { UserForgotPasswordResetV2WithHttp } from '../../../../../app/features/user/src/infra/repositories/user-forgot-password-v2.repository';

describe('UserForgotPasswordV2Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: UserForgotPasswordResetV2WithHttp;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMerchantAccessBaseUrl: 'https://merchant-acs.aplazo.net/api',
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        UserForgotPasswordResetV2WithHttp,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(UserForgotPasswordResetV2WithHttp);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(UserForgotPasswordResetV2WithHttp);
  });

  it('should execute successfully', () => {
    const request = '<EMAIL>';
    const response: UserForgotPasswordNewRolesDto = {
      code: 200,
      content: { success: true },
      error: null,
    };

    service.reset(request).subscribe({
      next: response => {
        expect(response).toEqual(response);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://merchant-acs.aplazo.net/api/auth/generate-reset-code'
    );

    expect(req.request.method).toEqual('POST');
    req.flush(response);
  });

  it('should throw error when reset throw error', () => {
    const request = '<EMAIL>';

    service.reset(request).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://merchant-acs.aplazo.net/api/auth/generate-reset-code'
    );

    expect(req.request.method).toEqual('POST');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
