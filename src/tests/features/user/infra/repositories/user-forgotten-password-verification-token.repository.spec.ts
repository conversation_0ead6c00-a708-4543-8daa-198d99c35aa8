import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { IUserForgottenPasswordVerificationTokenDto } from '../../../../../app/features/user/src/application/dtos/verification-token.dto';
import { UserForgottenPasswordVerificationTokenRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user-forgotten-password-verification-token.repository';

describe('UserForgotPasswordV2Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: UserForgottenPasswordVerificationTokenRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMerchantAccessBaseUrl: 'https://merchant-acs.aplazo.net/api',
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        UserForgottenPasswordVerificationTokenRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(
      UserForgottenPasswordVerificationTokenRepositoryImpl
    );
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(
      UserForgottenPasswordVerificationTokenRepositoryImpl
    );
  });

  it('should validateToken successfully', () => {
    const dto: IUserForgottenPasswordVerificationTokenDto = {
      token: '123',
    };

    service.validateToken(dto).subscribe({
      next: res => {
        expect(res.status).toEqual(true);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/forgot-verify?token=123'
    );

    expect(req.request.method).toEqual('GET');
    req.flush({ status: true });
  });

  it('should validateToken fail', () => {
    const dto: IUserForgottenPasswordVerificationTokenDto = {
      token: '123',
    };

    service.validateToken(dto).subscribe({
      next: res => {
        expect(res.status).toEqual(false);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/forgot-verify?token=123'
    );

    expect(req.request.method).toEqual('GET');
    req.flush({ status: false });
  });

  it('should throw error when validateToken throw error', () => {
    const dto: IUserForgottenPasswordVerificationTokenDto = {
      token: '123',
    };

    service.validateToken(dto).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/forgot-verify?token=123'
    );

    expect(req.request.method).toEqual('GET');
    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
