import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { NewRoleSetPasswordDto } from '../../../../../app/features/user/src/application/dtos/new-role-set-password.dto';
import { UserNewRolesSetNewPasswordWithHttp } from '../../../../../app/features/user/src/infra/repositories/user-new-roles-set-password-with-http.repository';

describe('UserNewRolesSetNewPasswordWithHttp', () => {
  let httpTestingController: HttpTestingController;
  let service: UserNewRolesSetNewPasswordWithHttp;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMerchantAccessBaseUrl: 'https://merchant-acs.aplazo.net/api',
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        UserNewRolesSetNewPasswordWithHttp,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(UserNewRolesSetNewPasswordWithHttp);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(UserNewRolesSetNewPasswordWithHttp);
  });

  it('shoudl set new password', () => {
    const args: { code: string; password: string } = {
      code: 'code',
      password: 'password',
    };

    const response: NewRoleSetPasswordDto = {
      code: 200,
      content: { success: true },
      error: null,
    };

    service.setPassword(args).subscribe({
      next: res => {
        expect(res).toEqual(response);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://merchant-acs.aplazo.net/api/auth/change-password'
    );

    expect(req.request.method).toEqual('POST');
    req.flush(response);
  });

  it('should throw error when set new password throw error', () => {
    const args: { code: string; password: string } = {
      code: 'code',
      password: 'password',
    };

    service.setPassword(args).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://merchant-acs.aplazo.net/api/auth/change-password'
    );

    expect(req.request.method).toEqual('POST');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
