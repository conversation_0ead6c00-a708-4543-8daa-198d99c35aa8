import {
  HttpClient,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { UserSetNewPasswordRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user-set-new-password.repository';

describe('UserForgotPasswordV2Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: UserSetNewPasswordRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMerchantAccessBaseUrl: 'https://merchant-acs.aplazo.net/api',
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        UserSetNewPasswordRepositoryImpl,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(UserSetNewPasswordRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(UserSetNewPasswordRepositoryImpl);
  });

  it('should set new password', () => {
    const args: FormData = new FormData();
    args.append('code', 'code');
    args.append('password', 'password');

    service.setPassword(args).subscribe({
      next: response => {
        expect(response).toBeFalsy();
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://api.aplazo.net/merchant/forgot-change-password'
    );

    expect(req.request.method).toEqual('POST');

    req.flush(null);
  });
});
