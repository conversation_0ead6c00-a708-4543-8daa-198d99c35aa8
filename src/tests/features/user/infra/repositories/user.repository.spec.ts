import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MERCHANT_CORE_ENVIRONMENT } from '../../../../../app/config/merchant-core.environment';
import { Credentials } from '../../../../../app/features/user/src/domain/entities/credentials';
import { UserRepositoryImpl } from '../../../../../app/features/user/src/infra/repositories/user.repository';

describe('UserRepositoryImpl', () => {
  let httpTestingController: HttpTestingController;
  let service: UserRepositoryImpl;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        UserRepositoryImpl,
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: {
            apiMerchantAccessBaseUrl: 'https://merchant-acs.aplazo.net/api',
            apiBaseUrl: 'https://api.aplazo.net/',
          },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(UserRepositoryImpl);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(UserRepositoryImpl);
  });

  it('should retrieve the login response when call loginWithEmail', () => {
    const expectedResponse = {
      Authorization: 'Bearer token',
    };

    const credentials: Credentials = {
      username: 'test',
      password: 'test',
    };

    service.loginWithEmail(credentials).subscribe({
      next: response => {
        expect(response).toBe(
          expectedResponse.Authorization.replace('Bearer ', '')
        );
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne('https://api.aplazo.net/login');
    expect(req.request.method).toEqual('POST');
    req.flush(expectedResponse);
  });

  it('should retrieve the login response when call loginWithoutEmail', () => {
    const expectedResponse = {
      content: { token: 'token' },
    };

    const credentials: Credentials = {
      username: 'test',
      password: 'test',
    };

    service.loginWithoutEmail(credentials).subscribe({
      next: response => {
        expect(response).toBe(expectedResponse.content.token);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'https://merchant-acs.aplazo.net/api/auth/login'
    );
    expect(req.request.method).toEqual('POST');
    req.flush(expectedResponse);
  });

  it('should throw error when loginWithEmail throw error', () => {
    const credentials: Credentials = {
      username: 'test',
      password: 'test',
    };

    service.loginWithEmail(credentials).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne('https://api.aplazo.net/login');
    expect(req.request.method).toEqual('POST');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });

  it('should throw error when loginWithoutEmail throw error', () => {
    const credentials: Credentials = {
      username: 'test',
      password: 'test',
    };

    service.loginWithoutEmail(credentials).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.error).toBe('Deliberate error');
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'https://merchant-acs.aplazo.net/api/auth/login'
    );
    expect(req.request.method).toEqual('POST');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
