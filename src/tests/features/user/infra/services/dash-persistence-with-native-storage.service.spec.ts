import { DOCUMENT } from '@angular/core';
/* eslint-disable @typescript-eslint/no-empty-function */

import { TestBed } from '@angular/core/testing';
import { IUserPersistenceDTO } from '../../../../../app/features/user/src/application/dtos/user-persistence.dto';
import { DashPersistenceWithNativeStorage } from '../../../../../app/features/user/src/infra/services/persistence-implementation.service';

describe('DashPersistenceWithNativeStorage', () => {
  let service: DashPersistenceWithNativeStorage;
  let doc: Document;
  let setItemSpy: jasmine.Spy;
  let getItemSpy: jasmine.Spy;
  let removeItemSpy: jasmine.Spy;

  const testUser: IUserPersistenceDTO = {
    username: '<EMAIL>',
    email: '<EMAIL>',
    merchantId: 1,
    merchantName: 'test merchant name',
    role: 'ADMIN',
    accessToken: 'token',
    lastLogin: new Date().toISOString(),
    isLoggedIn: false,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        DashPersistenceWithNativeStorage,
        {
          provide: DOCUMENT,
          useValue: {
            defaultView: {
              sessionStorage: {
                setItem: () => {},
                getItem: () => {},
                removeItem: () => {},
              },
            },
          },
        },
      ],
    });

    service = TestBed.inject(DashPersistenceWithNativeStorage);
    doc = TestBed.inject(DOCUMENT);

    setItemSpy = spyOn(doc.defaultView.sessionStorage, 'setItem');
    getItemSpy = spyOn(doc.defaultView.sessionStorage, 'getItem');
    removeItemSpy = spyOn(doc.defaultView.sessionStorage, 'removeItem');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(DashPersistenceWithNativeStorage);
  });

  it('should save authenticated user', async () => {
    setItemSpy.and.callThrough();

    await service.saveAuthenticatedUser(testUser);

    expect(setItemSpy).toHaveBeenCalledTimes(1);
    expect(setItemSpy).toHaveBeenCalledWith(
      'AplazoToken',
      JSON.stringify(testUser)
    );
  });

  it('should throw error when saving user but setItem throws error', async () => {
    setItemSpy.and.throwError(
      'Session storage is not available or not defined or QuotaExceededError (DOM Exception 22)'
    );

    await expectAsync(service.saveAuthenticatedUser(testUser)).toBeRejected();
  });

  it('should getAuthenticatedUser', async () => {
    getItemSpy.and.returnValue(JSON.stringify(testUser));

    const result = await service.getAuthenticatedUser();

    expect(getItemSpy).toHaveBeenCalledTimes(1);
    expect(result).toEqual(testUser);
  });

  it('should throw error when getting user but getItem throws error', async () => {
    getItemSpy.and.throwError(
      'Session storage is not available or not defined or QuotaExceededError (DOM Exception 22)'
    );

    await expectAsync(service.getAuthenticatedUser()).toBeRejected();

    expect(getItemSpy).toHaveBeenCalledTimes(1);
  });

  it('should throw error when getting user but getItem returns null', async () => {
    getItemSpy.and.returnValue(null);

    await expectAsync(service.getAuthenticatedUser()).toBeRejected();

    expect(getItemSpy).toHaveBeenCalledTimes(1);
  });

  it('should remove authenticated user', async () => {
    removeItemSpy.and.callThrough();

    await service.deAuthenticateUser();

    expect(removeItemSpy).toHaveBeenCalledTimes(1);
    expect(removeItemSpy).toHaveBeenCalledWith('AplazoToken');
  });

  it('should throw error when removing user but removeItem throws error', async () => {
    removeItemSpy.and.throwError(
      'Session storage is not available or not defined or QuotaExceededError (DOM Exception 22)'
    );

    await expectAsync(service.deAuthenticateUser()).toBeRejected();

    expect(removeItemSpy).toHaveBeenCalledTimes(1);
  });
});
