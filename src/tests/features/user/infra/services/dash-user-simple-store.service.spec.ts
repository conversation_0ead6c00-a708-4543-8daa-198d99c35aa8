import { IUserPersistenceDTO } from '../../../../../app/features/user/src/application/dtos/user-persistence.dto';
import {
  DashUserSimpleStore,
  emptyUser,
} from '../../../../../app/features/user/src/infra/services/dash-user-simple-store.service';

describe('DashUserSimpleStore', () => {
  let store: DashUserSimpleStore;

  const testUser: IUserPersistenceDTO = {
    username: '<EMAIL>',
    email: '<EMAIL>',
    merchantId: 1,
    merchantName: 'test merchant name',
    role: 'ADMIN',
    accessToken: 'token',
    lastLogin: new Date().toISOString(),
    isLoggedIn: false,
  };

  beforeEach(() => {
    store = new DashUserSimpleStore();
  });

  it('should set user', done => {
    let result: IUserPersistenceDTO | undefined;

    store.user$.subscribe(user => {
      result = user;
    });

    store.setUser(testUser);

    expect(result.merchantId).toBe(testUser.merchantId);
    expect(result.merchantName).toBe(testUser.merchantName);
    expect(result.role).toBe(testUser.role);
    expect(result.username).toBe(testUser.username);
    expect(result.email).toBe(testUser.email);
    expect(result.accessToken).toBe(testUser.accessToken);
    expect(result.lastLogin).toBe(testUser.lastLogin);
    expect(result.isLoggedIn).toBe(testUser.isLoggedIn);

    done();
  });

  it('should set failure', done => {
    let result: IUserPersistenceDTO | undefined;

    store.user$.subscribe(user => {
      result = user;
    });

    store.setFailure(new Error('test error'));

    expect(result).toEqual(emptyUser);
    done();
  });

  it('should clear user', done => {
    let result: IUserPersistenceDTO | undefined;

    store.user$.subscribe(user => {
      result = user;
    });

    store.setUser(testUser);

    expect(result).toEqual(testUser);

    store.clearUserStore();

    expect(result).toEqual(emptyUser);

    done();
  });

  it('should set token', done => {
    let result: string | undefined;

    store.tokenSession$.subscribe(token => {
      result = token;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.accessToken);
    done();
  });

  it('should set user email', done => {
    let result: string | undefined;

    store.userEmail$.subscribe(email => {
      result = email;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.email);
    done();
  });

  it('should set username', done => {
    let result: string | undefined;

    store.username$.subscribe(username => {
      result = username;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.username);
    done();
  });

  it('should set merchant id', done => {
    let result: number | undefined;

    store.merchantId$.subscribe(id => {
      result = id;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.merchantId);
    done();
  });

  it('should set merchant name', done => {
    let result: string | undefined;

    store.merchantName$.subscribe(name => {
      result = name;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.merchantName);
    done();
  });

  it('should set merchant', done => {
    let result: { name: string; id: string; email: string } | undefined;

    store.merchant$.subscribe(merchant => {
      result = merchant;
    });

    store.setUser(testUser);

    expect(result).toEqual({
      name: testUser.merchantName,
      id: testUser.merchantId?.toString() ?? '0',
      email: testUser.email,
    });
    done();
  });

  it('should set role', done => {
    let result: string | undefined;

    store.role$.subscribe(role => {
      result = role;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.role);
    done();
  });

  it('should set isLoggedIn', done => {
    let result: boolean | undefined;

    store.isLoggedIn$.subscribe(isLoggedIn => {
      result = isLoggedIn;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.isLoggedIn);
    done();
  });

  it('should get sync role', () => {
    expect(store.getRole()).toBe('');

    store.setUser(testUser);

    expect(store.getRole()).toBe(testUser.role);
  });
});
