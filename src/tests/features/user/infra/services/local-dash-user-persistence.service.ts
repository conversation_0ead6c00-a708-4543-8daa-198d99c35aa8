/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { IUserPersistenceDTO } from '../../../../../app/features/user/src/application/dtos/user-persistence.dto';
import { DashUserPersistenceService } from '../../../../../app/features/user/src/application/services/local-persistence.service';
import users from '../local-users.db.json';

export class LocalDashUserPersistenceService
  implements DashUserPersistenceService
{
  #johnUser = users.find(i => i.sub === '<EMAIL>');
  #user: IUserPersistenceDTO | undefined = {
    accessToken: JSON.stringify(this.#johnUser!),
    email: this.#johnUser!.sub,
    isLoggedIn: true,
    role: this.#johnUser!.role,
    lastLogin: new Date().toISOString(),
    merchantId: this.#johnUser!.merchantId,
    merchantName: this.#johnUser!.name ?? this.#johnUser!.username ?? '',
    username: this.#johnUser!.sub,
  };

  async getAuthenticatedUser(): Promise<IUserPersistenceDTO> {
    if (!this.#user) {
      throw new Error('Usuario no encontrado');
    }

    return this.#user;
  }

  async saveAuthenticatedUser(user: IUserPersistenceDTO): Promise<void> {
    this.#user = user;
    console.log(
      'DashUserPersistenceService::saveAuthenticatedUser',
      user.username
    );
  }

  async deAuthenticateUser(): Promise<void> {
    this.#user = undefined;
    console.log('DashUserPersistenceService::deAuthenticateUser');
  }
}
