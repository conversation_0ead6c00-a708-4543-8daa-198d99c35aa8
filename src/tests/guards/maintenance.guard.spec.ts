import { TestBed } from '@angular/core/testing';
import { Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { StatsigService } from '@statsig/angular-bindings';
import { DASH_ROUTES } from '../../app/config/app-route-core';
import { isUnderMaintenanceGuard } from '../../app/guards/maintenance.guard';

describe('isUnderMaintenanceGuard', () => {
  let featureFlagsServiceMock: jasmine.SpyObj<StatsigService>;
  let router: Router;
  let stateSnapshotMock: RouterStateSnapshot;

  beforeEach(() => {
    featureFlagsServiceMock = jasmine.createSpyObj('FeatureFlagsService', [
      'getFlag',
    ]);

    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      providers: [
        {
          provide: StatsigService,
          useValue: jasmine.createSpyObj('StatsigService', ['checkGate']),
        },
      ],
    });

    router = TestBed.inject(Router);
    spyOn(router, 'parseUrl').and.callThrough();

    featureFlagsServiceMock = TestBed.inject(
      StatsigService
    ) as jasmine.SpyObj<StatsigService>;
  });

  it('should redirect to maintenance page when maintenance is enabled and current route is not maintenance', async () => {
    // Arrange
    stateSnapshotMock = { url: '/some-route' } as RouterStateSnapshot;
    featureFlagsServiceMock.checkGate.and.returnValue(true);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(featureFlagsServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).toHaveBeenCalledWith(`/${DASH_ROUTES.unavailable}`);
    expect(result).toBeInstanceOf(UrlTree);
  });

  it('should redirect to authentication when maintenance is disabled but current route is maintenance', async () => {
    // Arrange
    stateSnapshotMock = {
      url: `/${DASH_ROUTES.unavailable}`,
    } as RouterStateSnapshot;
    featureFlagsServiceMock.checkGate.and.returnValue(false);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(featureFlagsServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).toHaveBeenCalledWith(
      `/${DASH_ROUTES.authentication}`
    );
    expect(result).toBeInstanceOf(UrlTree);
  });

  it('should allow navigation when maintenance is enabled and current route is maintenance', async () => {
    // Arrange
    stateSnapshotMock = {
      url: `/${DASH_ROUTES.unavailable}`,
    } as RouterStateSnapshot;
    featureFlagsServiceMock.checkGate.and.returnValue(true);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(featureFlagsServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).not.toHaveBeenCalled();
    expect(result).toBeTrue();
  });

  it('should allow navigation when maintenance is disabled and current route is not maintenance', async () => {
    // Arrange
    stateSnapshotMock = { url: '/some-other-route' } as RouterStateSnapshot;
    featureFlagsServiceMock.checkGate.and.returnValue(false);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(featureFlagsServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).not.toHaveBeenCalled();
    expect(result).toBeTrue();
  });

  it('should handle null flag value as maintenance disabled', async () => {
    // Arrange
    stateSnapshotMock = { url: '/some-route' } as RouterStateSnapshot;
    featureFlagsServiceMock.checkGate.and.returnValue(false);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(featureFlagsServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).not.toHaveBeenCalled();
    expect(result).toBeTrue();
  });
});
