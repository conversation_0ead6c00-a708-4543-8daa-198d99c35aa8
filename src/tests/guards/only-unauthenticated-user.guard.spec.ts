import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter, Route, UrlTree } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { lastValueFrom, Observable, of } from 'rxjs';
import { DASH_ROUTES } from '../../app/config/app-route-core';
import { UserStoreService } from '../../app/features/user/src/application/services/user-store.service';
import { preventNavigationByUnauthenticatedUser } from '../../app/guards/only-unauthenticated-user.guard';

@Component({
  standalone: true,
  template: `<span>test</span>`,
})
export class TestComponent {}

const setup = (mockUserStore: unknown, routes: Route[]) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      { provide: UserStoreService, useValue: mockUserStore },
      provideRouter(routes),
    ],
  });

  return TestBed.runInInjectionContext(() =>
    preventNavigationByUnauthenticatedUser({} as any, {} as any)
  );
};

const routes: Route[] = [
  {
    path: 'unauthenticated',
    component: TestComponent,
    canActivate: [preventNavigationByUnauthenticatedUser],
  },
  {
    path: DASH_ROUTES.rootApp,
    component: TestComponent,
  },
];

describe('preventNavigationByUnauthenticatedUser', () => {
  it('should allow navigation if user is not logged in', async () => {
    const mockUserStore = {
      isLoggedIn$: of(false),
    };

    const guard = await lastValueFrom(
      (await setup(mockUserStore, routes)) as Observable<boolean | UrlTree>
    );

    await RouterTestingHarness.create('unauthenticated');

    expect(guard).toBeTrue();
  });

  it('should prevent navigation if user is logged in', async () => {
    const mockUserStore = {
      isLoggedIn$: of(true),
    };

    const guard = await lastValueFrom(
      (await setup(mockUserStore, routes)) as Observable<boolean | UrlTree>
    );

    await RouterTestingHarness.create('unauthenticated');

    expect(guard).toBeInstanceOf(UrlTree);
    expect((guard as UrlTree).toString()).toBe(`/${DASH_ROUTES.rootApp}`);
  });
});
