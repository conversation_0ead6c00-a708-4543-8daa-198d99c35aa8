import { makeEnvironmentProviders } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import { of } from 'rxjs';
import apiKeySdk from '../assets/i18n/api-key-sdk/es.json';
import balanceDetails from '../assets/i18n/balance-details/es.json';
import balance from '../assets/i18n/balance/es.json';
import bankingInfo from '../assets/i18n/banking-info/es.json';
import changePassword from '../assets/i18n/change-password/es.json';
import clarifications from '../assets/i18n/clarifications/es.json';
import dailyCombine from '../assets/i18n/daily-combine/es.json';
import dailyHours from '../assets/i18n/daily-hours/es.json';
import dailyWeekdays from '../assets/i18n/daily-weekdays/es.json';
import dashboard from '../assets/i18n/dashboard/es.json';
import unknown from '../assets/i18n/es.json';
import home from '../assets/i18n/home/<USER>';
import legacyReport from '../assets/i18n/legacy-report/es.json';
import login from '../assets/i18n/login/es.json';
import maintenance from '../assets/i18n/maintenance/es.json';
import profile from '../assets/i18n/profile/es.json';
import refunds from '../assets/i18n/refunds/es.json';

const NAME_MAPPING = {
  'api-key-sdk': apiKeySdk,
  balance: balance,
  'balance-details': balanceDetails,
  'banking-info': bankingInfo,
  'change-password': changePassword,
  clarifications: clarifications,
  'daily-combine': dailyCombine,
  'daily-hours': dailyHours,
  'daily-weekdays': dailyWeekdays,
  dashboard: dashboard,
  home: home,
  'legacy-report': legacyReport,
  login: login,
  maintenance: maintenance,
  profile: profile,
  refunds: refunds,
  '': unknown,
  unknown: unknown,
} as const;

export type I18nScopeTesting = keyof typeof NAME_MAPPING;

export function provideI18NTesting(...scopes: I18nScopeTesting[]) {
  // Default to empty scope if no scopes provided
  const scopesToUse = scopes.length > 0 ? scopes : [''];

  // Merge all translations from the provided scopes
  const mergedTranslations = scopesToUse.reduce(
    (acc, scope) => {
      const scopeTranslations = NAME_MAPPING[scope];
      return { ...acc, ...scopeTranslations };
    },
    {} as Record<string, any>
  );

  const i18nLocal = {
    getTranslateObjectByKey: ({ key }: { key: string }) =>
      of(mergedTranslations[key]),
    getTranslateByKey: ({ key }: { key: string }) =>
      of(mergedTranslations[key]),
  };

  return makeEnvironmentProviders([
    {
      provide: I18NService,
      useValue: i18nLocal,
    },
  ]);
}
