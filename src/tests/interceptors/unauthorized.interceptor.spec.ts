import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NotifierService } from '@aplazo/merchant/shared';
import { of } from 'rxjs';
import { DASH_ROUTES } from '../../app/config/app-route-core';
import { MERCHANT_CORE_ENVIRONMENT } from '../../app/config/merchant-core.environment';
import { UserLogoutUseCase } from '../../app/features/user/src/application/usecases/logout.usecase';
import { unauthorizedInterceptor } from '../../app/interceptors/unauthorized.interceptor';

describe('unauthorizedInterceptor', () => {
  let httpClient: HttpClient;
  let httpTestingController: HttpTestingController;
  let notifier: jasmine.SpyObj<NotifierService>;
  let usecase: jasmine.SpyObj<UserLogoutUseCase>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(withInterceptors([unauthorizedInterceptor])),
        provideHttpClientTesting(),
        {
          provide: NotifierService,
          useValue: jasmine.createSpyObj('NotifierService', ['warning']),
        },
        {
          provide: MERCHANT_CORE_ENVIRONMENT,
          useValue: { apiBaseUrl: 'http://localhost:4200/' },
        },
        {
          provide: UserLogoutUseCase,
          useValue: jasmine.createSpyObj('UserLogoutUseCase', ['execute']),
        },
      ],
    });

    httpClient = TestBed.inject(HttpClient);
    httpTestingController = TestBed.inject(HttpTestingController);
    notifier = TestBed.inject(
      NotifierService
    ) as jasmine.SpyObj<NotifierService>;
    usecase = TestBed.inject(
      UserLogoutUseCase
    ) as jasmine.SpyObj<UserLogoutUseCase>;
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should call logout usecase when http response is 403 and all conditions are met', () => {
    notifier.warning.and.returnValue(undefined);
    usecase.execute.and.returnValue(of(undefined));

    httpClient.get('http://localhost:4200/resource').subscribe({
      next: fail,
      error: e => {
        expect(e.status).toBe(403);
        expect(usecase.execute).toHaveBeenCalledTimes(1);
        expect(usecase.execute).toHaveBeenCalledWith(
          `/${DASH_ROUTES.authentication}`
        );

        expect(notifier.warning).toHaveBeenCalledTimes(1);

        expect(notifier.warning).toHaveBeenCalledWith({
          title: 'La sesión ha expirado',
          message: 'Por favor, inicie sesión nuevamente',
        });
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:4200/resource'
    );

    req.flush('Deliberate error 403', { status: 403, statusText: 'Forbidden' });
  });

  it('should not call logout usecase when http response is not errorResponse', () => {
    httpClient.get('http://localhost:4200/resource').subscribe({
      next: response => {
        expect(response).toBeFalsy();
        expect(usecase.execute).toHaveBeenCalledTimes(0);
        expect(notifier.warning).toHaveBeenCalledTimes(0);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'http://localhost:4200/resource'
    );

    req.flush(null);
  });

  it('should not call logout usecase when http response is error but not 403', () => {
    httpClient.get('http://localhost:4200/resource').subscribe({
      next: fail,
      error: e => {
        expect(e.status).toBe(400);
        expect(usecase.execute).toHaveBeenCalledTimes(0);
        expect(notifier.warning).toHaveBeenCalledTimes(0);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:4200/resource'
    );

    req.flush('Deliberate error 400', {
      status: 400,
      statusText: 'Bad Request',
    });
  });

  it('should not call logout usecase whent url is from login even if 403', () => {
    httpClient.get('http://localhost:4200/login').subscribe({
      next: fail,
      error: e => {
        expect(e.status).toBe(403);
        expect(usecase.execute).toHaveBeenCalledTimes(0);
        expect(notifier.warning).toHaveBeenCalledTimes(0);
      },
    });

    const req = httpTestingController.expectOne('http://localhost:4200/login');

    req.flush('Deliberate error 403', { status: 403, statusText: 'Forbidden' });
  });
});
