import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { UserStoreService } from '../../app/features/user/src/application/services/user-store.service';
import { userAccessTokenInterceptor } from '../../app/interceptors/user-access-token.interceptor';

const setup = (userStore: unknown) => {
  TestBed.configureTestingModule({
    providers: [
      provideHttpClient(withInterceptors([userAccessTokenInterceptor])),
      provideHttpClientTesting(),
      {
        provide: UserStoreService,
        useValue: userStore,
      },
    ],
  });

  return {
    httpClient: TestBed.inject(HttpClient),
    httpTestingController: TestBed.inject(HttpTestingController),
  };
};

describe('userAccessTokenInterceptor', () => {
  it('should not add the Authorization header if the resource is one of the whitelisted ones', () => {
    const { httpClient, httpTestingController } = setup({
      isLoggedIn$: of(true),
      tokenSession$: of(''),
    });

    httpClient.get('http://localhost:4200/image.png').subscribe();

    const req = httpTestingController.expectOne(
      'http://localhost:4200/image.png'
    );

    expect(req.request.method).toEqual('GET');

    expect(req.request.headers.get('Authorization')).toBeNull();

    httpClient.get('http://localhost:4200/image.webp').subscribe();

    const req1 = httpTestingController.expectOne(
      'http://localhost:4200/image.webp'
    );

    expect(req1.request.method).toEqual('GET');

    expect(req1.request.headers.get('Authorization')).toBeNull();

    httpClient.get('http://localhost:4200/image.svg').subscribe();

    const req2 = httpTestingController.expectOne(
      'http://localhost:4200/image.svg'
    );

    expect(req2.request.method).toEqual('GET');

    expect(req2.request.headers.get('Authorization')).toBeNull();

    httpClient.get('http://localhost:4200/file.json').subscribe();

    const req3 = httpTestingController.expectOne(
      'http://localhost:4200/file.json'
    );

    expect(req3.request.method).toEqual('GET');

    expect(req3.request.headers.get('Authorization')).toBeNull();

    httpTestingController.verify();
  });

  it('should add the Authorization header if the resource is not one of the whitelisted ones and all conditions are met', () => {
    const { httpClient, httpTestingController } = setup({
      isLoggedIn$: of(true),
      tokenSession$: of('fake-token'),
    });

    httpClient.get('http://localhost:4200/amazing/resource').subscribe();

    const req = httpTestingController.expectOne(
      'http://localhost:4200/amazing/resource'
    );

    expect(req.request.method).toEqual('GET');

    expect(req.request.headers.get('Authorization')).toBe('Bearer fake-token');

    httpTestingController.verify();
  });

  it('should not add the Authorization header if the resource is not one of the whitelisted ones but user is not logged in', () => {
    const { httpClient, httpTestingController } = setup({
      isLoggedIn$: of(false),
      tokenSession$: of('fake-token'),
    });

    httpClient.get('http://localhost:4200/amazing/resource').subscribe();

    const req = httpTestingController.expectOne(
      'http://localhost:4200/amazing/resource'
    );

    expect(req.request.method).toEqual('GET');

    expect(req.request.headers.get('Authorization')).toBeNull();
  });
});
