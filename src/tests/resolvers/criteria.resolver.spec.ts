import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { lastValueFrom, Observable } from 'rxjs';
import { loadBranchOfficesIntoCriteria } from '../../app/resolvers/criteria.resolver';
import { BranchOfficesStore } from '../../app/services/branch-offices.store';
import { SharedCriteria } from '../../app/services/shared-criteria.store';

@Component({
  standalone: true,
  template: `<span>test</span>`,
})
export class TestComponent {}

describe('loadBranchOfficesIntoCriteria', () => {
  let criteria: jasmine.SpyObj<SharedCriteria>;
  let resolver: Observable<any>;
  let branchOfficesStore: BranchOfficesStore;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [TestComponent],
      providers: [
        provideRouter([
          {
            path: '',
            component: TestComponent,
            resolve: { branches: loadBranchOfficesIntoCriteria },
          },
        ]),
        {
          provide: SharedCriteria,
          useValue: jasmine.createSpyObj('SharedCriteria', [
            'setBranchOffices',
          ]),
        },
        BranchOfficesStore,
      ],
    });

    criteria = TestBed.inject(SharedCriteria) as jasmine.SpyObj<SharedCriteria>;
    branchOfficesStore = TestBed.inject(BranchOfficesStore);
    resolver = (await TestBed.runInInjectionContext(() =>
      loadBranchOfficesIntoCriteria({} as any, {} as any)
    )) as Observable<any>;
  });

  it('should load branches into criteria', async () => {
    const branches = [
      { id: 1, name: 'test1' },
      { id: 2, name: 'test2' },
    ];
    branchOfficesStore.setBranchOffices(branches);

    criteria.setBranchOffices.and.returnValue(undefined);

    const resolv = await lastValueFrom(resolver);

    expect(resolv).toEqual(branches);
    expect(criteria.setBranchOffices).toHaveBeenCalledTimes(1);
  });
});
