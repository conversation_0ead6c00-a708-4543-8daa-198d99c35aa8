import { IBranchOfficeUIDto } from '../../app/features/branch-offices/src/domain/entities/branch-office';
import { BranchOfficesStore } from '../../app/services/branch-offices.store';

describe('BranchOfficesStore', () => {
  let store: BranchOfficesStore;

  beforeEach(() => {
    store = new BranchOfficesStore();
  });

  it('should be created', () => {
    expect(store).toBeTruthy();
    expect(store).toBeInstanceOf(BranchOfficesStore);
  });

  it('should set branch offices', done => {
    let result: IBranchOfficeUIDto[] | undefined;

    store.branchOffices$().subscribe(branches => {
      result = branches;
    });

    store.setBranchOffices([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    expect(result).toEqual([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    done();
  });

  it('should not set branch offices if they are the same', done => {
    let result: IBranchOfficeUIDto[] | undefined;

    store.branchOffices$().subscribe(branches => {
      result = branches;
    });

    store.setBranchOffices([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    expect(result).toEqual([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    store.setBranchOffices([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    expect(result).toEqual([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    done();
  });

  it('should clear branch offices', done => {
    let result: IBranchOfficeUIDto[] | undefined;

    store.branchOffices$().subscribe(branches => {
      result = branches;
    });

    store.setBranchOffices([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    expect(result).toEqual([
      { id: 1, name: 'test' },
      { id: 2, name: 'test2' },
    ]);

    store.clearBranchOffices();

    expect(result).toEqual([]);

    done();
  });
});
