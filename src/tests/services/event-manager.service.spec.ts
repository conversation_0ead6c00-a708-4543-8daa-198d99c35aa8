import { TestBed } from '@angular/core/testing';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { of } from 'rxjs';
import { UserStoreService } from '../../app/features/user/src/application/services/user-store.service';
import { EventManagerService } from '../../app/services/event-manger.service';

const setup = (userStore: unknown) => {
  TestBed.configureTestingModule({
    providers: [
      EventManagerService,
      { provide: UserStoreService, useValue: userStore },
      {
        provide: TagManagerService,
        useValue: jasmine.createSpyObj<TagManagerService>(['trackEvent']),
      },
    ],
  });

  return {
    service: TestBed.inject(EventManagerService),
    tagManager: TestBed.inject(
      TagManagerService
    ) as jasmine.SpyObj<TagManagerService>,
  };
};

describe('EventManagerService', () => {
  it('should be created', () => {
    const { service } = setup({});

    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(EventManagerService);
  });

  it('should call trackEvent with the correct event', () => {
    const { service, tagManager } = setup({
      merchant$: of({ id: 123, name: 'merchant' }),
    });

    service.sendTrackEvent('pageView', {});

    expect(tagManager.trackEvent).toHaveBeenCalledTimes(1);
    expect(tagManager.trackEvent).toHaveBeenCalledWith({
      event: 'pageView',
      merchantId: 123,
      merchantName: 'merchant',
      description: {
        startDate: '',
        endDate: '',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
        buttonName: '',
        genericInfo: '',
        url: '',
        title: '',
        category: '',
        label: '',
        timestamp: undefined,
      },
    });
  });

  it('should call trackEvent with the correct event and description', () => {
    const { service, tagManager } = setup({
      merchant$: of({ id: 123, name: 'merchant' }),
    });

    service.sendTrackEvent('dateRange', {
      startDate: '02/02/2024',
      endDate: '02/02/2024',
    });

    expect(tagManager.trackEvent).toHaveBeenCalledTimes(1);
    expect(tagManager.trackEvent).toHaveBeenCalledWith({
      event: 'dateRange',
      merchantId: 123,
      merchantName: 'merchant',
      description: {
        startDate: '02/02/2024',
        endDate: '02/02/2024',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
        buttonName: '',
        genericInfo: '',
        url: '',
        title: '',
        category: '',
        label: '',
        timestamp: undefined,
      },
    });
  });

  it('should call trackEvent with the correct event and description when merchant id is not a number', () => {
    const { service, tagManager } = setup({
      merchant$: of({ id: 'a123', name: 'merchant' }),
    });

    service.sendTrackEvent('dateRange', {
      startDate: '02/02/2024',
      endDate: '02/02/2024',
    });

    expect(tagManager.trackEvent).toHaveBeenCalledTimes(1);
    expect(tagManager.trackEvent).toHaveBeenCalledWith({
      event: 'dateRange',
      merchantId: 0,
      merchantName: 'merchant',
      description: {
        startDate: '02/02/2024',
        endDate: '02/02/2024',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
        buttonName: '',
        genericInfo: '',
        url: '',
        title: '',
        category: '',
        label: '',
        timestamp: undefined,
      },
    });
  });

  it('should call trackEvent with the correct event and description when merchant name is not defined', () => {
    const { service, tagManager } = setup({
      merchant$: of({ id: 123 }),
    });

    service.sendTrackEvent('dateRange', {
      startDate: '02/02/2024',
      endDate: '02/02/2024',
    });

    expect(tagManager.trackEvent).toHaveBeenCalledTimes(1);
    expect(tagManager.trackEvent).toHaveBeenCalledWith({
      event: 'dateRange',
      merchantId: 123,
      merchantName: '',
      description: {
        startDate: '02/02/2024',
        endDate: '02/02/2024',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
        buttonName: '',
        genericInfo: '',
        url: '',
        title: '',
        category: '',
        label: '',
        timestamp: undefined,
      },
    });
  });
});
