import { SharedCriteriaWithBranchOfficesUIDto } from '../../app/features/shared/shared-criteria';
import {
  initialSharedCriteria,
  SharedCriteria,
} from '../../app/services/shared-criteria.store';

describe('SharedCriteriaStore', () => {
  let store: SharedCriteria;

  beforeEach(() => {
    store = new SharedCriteria();
  });

  it('should be created', () => {
    expect(store).toBeTruthy();
    expect(store).toBeInstanceOf(SharedCriteria);
  });

  it('should set criteria', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(initialSharedCriteria);

    store.setCriteria({
      ...initialSharedCriteria,
      status: 'cancelled',
      days: [0, 1, 2, 3],
    });

    expect(result.status).toBe('cancelled');
    expect(result.days).toEqual([0, 1, 2, 3]);

    done();
  });

  it('should set criteria to initial value when passed invalid criteria', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(initialSharedCriteria);

    store.setCriteria(undefined);

    expect(result).toEqual(initialSharedCriteria);
    done();
  });

  it('should clear criteria', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result).toEqual(initialSharedCriteria);

    store.setCriteria({
      ...initialSharedCriteria,
      status: 'cancelled',
      days: [0, 1, 2, 3],
    });

    expect(result.status).toBe('cancelled');
    expect(result.days).toEqual([0, 1, 2, 3]);

    store.clearCriteria();

    expect(result).toEqual(initialSharedCriteria);

    done();
  });

  it('should set branch offices', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    store.setBranchOffices({ selection: [1, 2], allSelected: true });

    expect(result.branchOffices).toEqual([1, 2]);
    expect(result.isAllBranchesSelected).toBeTrue();

    done();
  });

  it('should set date range', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.dateRange.startDate).toBe(
      initialSharedCriteria.dateRange.startDate
    );

    store.setDateRange({
      startDate: '01/02/2024',
      endDate: '01/03/2024',
    });

    expect(result.dateRange.startDate).toBe('01/02/2024');
    expect(result.dateRange.endDate).toBe('01/03/2024');

    done();
  });

  it('should set page number', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.pageNum).toBe(initialSharedCriteria.pageNum);

    store.setPageNum(5);

    expect(result.pageNum).toBe(5);

    done();
  });

  it('should set page size', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.pageSize).toBe(initialSharedCriteria.pageSize);

    store.setPageSize(5);

    expect(result.pageSize).toBe(5);

    done();
  });

  it('should set status', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.status).toBe(initialSharedCriteria.status);

    store.setStatus('cancelled');

    expect(result.status).toBe('cancelled');

    done();
  });

  it('should set search', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.element).toBe(initialSharedCriteria.element);

    store.setSearch('test');

    expect(result.element).toBe('test');

    done();
  });

  it('should set days', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.days).toEqual(initialSharedCriteria.days);

    store.setDays([0, 1, 2, 3]);

    expect(result.days).toEqual([0, 1, 2, 3]);

    done();
  });

  it('should reflect changes in selectedBranchOffices$', done => {
    let result: number[] | undefined;

    store.selectedBranchOffices$.subscribe(branches => {
      result = branches;
    });

    expect(result).toEqual([]);

    store.setBranchOffices({ selection: [1, 2], allSelected: true });

    expect(result).toEqual([1, 2]);

    done();
  });

  it('should reflect changes in dateRange$', done => {
    let result: { startDate: string; endDate: string } | undefined;

    store.dateRange$.subscribe(dateRange => {
      result = dateRange;
    });

    expect(result).toEqual({
      startDate: initialSharedCriteria.dateRange.startDate,
      endDate: initialSharedCriteria.dateRange.endDate,
    });

    store.setDateRange({
      startDate: '01/02/2024',
      endDate: '01/03/2024',
    });

    expect(result).toEqual({
      startDate: '01/02/2024',
      endDate: '01/03/2024',
    });

    done();
  });

  it('should reflect changes in page$', done => {
    let result: number | undefined;

    store.page$.subscribe(pageNum => {
      result = pageNum;
    });

    expect(result).toBe(initialSharedCriteria.pageNum);

    store.setPageNum(5);

    expect(result).toBe(5);

    done();
  });

  it('should reflect changes in size$', done => {
    let result: number | undefined;

    store.itemsToShow$.subscribe(pageSize => {
      result = pageSize;
    });

    expect(result).toBe(initialSharedCriteria.pageSize);

    store.setPageSize(5);

    expect(result).toBe(5);

    done();
  });

  it('should reflect changes in status$', done => {
    let result: string | undefined;

    store.status$.subscribe(status => {
      result = status;
    });

    expect(result).toBe(initialSharedCriteria.status);

    store.setStatus('cancelled');

    expect(result).toBe('cancelled');

    done();
  });

  it('should reflect changes in search$', done => {
    let result: string | undefined;

    store.search$.subscribe(search => {
      result = search;
    });

    expect(result).toBe(initialSharedCriteria.element);

    store.setSearch('test');

    expect(result).toBe('test');

    done();
  });

  it('should reflect changes in days$', done => {
    let result: number[] | undefined;

    store.days$.subscribe(days => {
      result = days;
    });

    expect(result).toEqual(initialSharedCriteria.days);

    store.setDays([0, 1, 2, 3]);

    expect(result).toEqual([0, 1, 2, 3]);

    done();
  });

  it('should not update pageSize', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.pageSize).toBe(initialSharedCriteria.pageSize);

    store.setPageSize(initialSharedCriteria.pageSize);

    expect(result.pageSize).toBe(initialSharedCriteria.pageSize);

    done();
  });

  it('should not update search', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.element).toBe(initialSharedCriteria.element);

    store.setSearch(initialSharedCriteria.element);

    expect(result.element).toBe(initialSharedCriteria.element);

    done();
  });

  it('should not update days', done => {
    let result: SharedCriteriaWithBranchOfficesUIDto | undefined;

    store.criteria$.subscribe(criteria => {
      result = criteria;
    });

    expect(result.days).toEqual(initialSharedCriteria.days);

    store.setDays(initialSharedCriteria.days);

    expect(result.days).toEqual(initialSharedCriteria.days);

    done();
  });
});
