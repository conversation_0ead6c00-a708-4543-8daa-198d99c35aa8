{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "bundler", "importHelpers": true, "preserveSymlinks": true, "target": "ES2022", "module": "ES2022", "lib": ["ES2022", "dom"], "esModuleInterop": true, "resolveJsonModule": true, "skipLibCheck": true, "useDefineForClassFields": false}, "angularCompilerOptions": {"strictInjectionParameters": true, "strictTemplates": true, "preserveWhitespaces": true}, "exclude": ["node_modules"], "include": ["src/**/*.ts"]}